package com.hongminh54.storage.Database;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.logging.Level;

import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.Storage;


public abstract class Database {
    public String table = "PlayerData";
    Storage main;
    Connection connection;
    

    
    // Connection pool để tối ưu hiệu suất kết nối
    private final ConcurrentLinkedQueue<Connection> connectionPool = new ConcurrentLinkedQueue<>();
    private static final int MAX_POOL_SIZE = 5;

    public Database(Storage instance) {
        main = instance;
    }

    public abstract Connection getSQLConnection();

    public abstract void load();

    public void initialize() {
        connection = getSQLConnection();
        try {
            PreparedStatement ps = connection.prepareStatement("SELECT * FROM " + table + " WHERE player = ?");
            ps.setString(1, "init");
            ResultSet rs = ps.executeQuery();
            close(ps, rs);
        } catch (SQLException ex) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Unable to retrieve connection", ex);
        }
    }
    
    /**
     * Lấy kết nối từ pool hoặc tạo mới nếu pool trống
     * @return Kết nối SQL
     */
    public Connection getConnection() {
        Connection conn = connectionPool.poll();
        if (conn == null) {
            return getSQLConnection();
        }
        
        try {
            if (conn.isClosed()) {
                return getSQLConnection();
            }
        } catch (SQLException e) {
            return getSQLConnection();
        }
        
        return conn;
    }
    
    /**
     * Trả kết nối về pool thay vì đóng
     * @param conn Kết nối cần trả về
     */
    public void returnConnection(Connection conn) {
        if (conn == null) return;
        
        try {
            // Đảm bảo không còn transaction đang hoạt động khi trả kết nối về pool
            if (!conn.isClosed() && !conn.getAutoCommit()) {
                try {
                    conn.setAutoCommit(true);
                } catch (SQLException e) {
                    Storage.getStorage().getLogger().warning("Không thể đặt lại autoCommit khi trả connection: " + e.getMessage());
                }
            }
            
            // Xóa khỏi map theo dõi transaction

            
            // Tiếp tục quy trình trả kết nối về pool
            if (connectionPool.size() < MAX_POOL_SIZE) {
                connectionPool.add(conn);
            } else {
                try {
                    conn.close();
                } catch (SQLException e) {
                    Storage.getStorage().getLogger().warning("Lỗi khi đóng kết nối: " + e.getMessage());
                }
            }
        } catch (SQLException e) {
            // Xử lý lỗi khi kiểm tra trạng thái kết nối
            Storage.getStorage().getLogger().warning("Lỗi khi trả connection về pool: " + e.getMessage());
            try {
                    conn.close();
            } catch (SQLException ex) {
                // Bỏ qua
            }
        }
    }

    // These are the methods you can use to get things out of your database. You of course can make new ones to return different things in the database.
    // This returns the number of people the player killed.
    public PlayerData getData(String player) {
        if (player == null || player.isEmpty()) {
            Storage.getStorage().getLogger().warning("Không thể truy vấn dữ liệu với player null hoặc rỗng");
            return null;
        }

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        // Biến đếm số lần thử lại
        int retryCount = 0;
        final int maxRetries = 3;
        final long retryDelay = 500; // 500ms
        
        while (retryCount <= maxRetries) {
            try {
                conn = getConnection();
                
                if (conn == null) {
                    Storage.getStorage().getLogger().warning("Không thể lấy kết nối database để truy vấn dữ liệu cho " + player);
                    if (retryCount < maxRetries) {
                        retryCount++;
                        try {
                            Thread.sleep(retryDelay * retryCount);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                        continue; // Thử lại
                    } else {
                        break; // Hết số lần thử, thoát khỏi vòng lặp
                    }
                }
                
                // Đảm bảo kết nối còn hiệu lực
                try {
                    if (conn.isClosed() || !conn.isValid(1)) {
                        if (conn != null) {
                            try { conn.close(); } catch (SQLException ignored) {}
                        }
                        conn = getConnection(); // Tạo kết nối mới
                        if (conn == null) {
                            if (retryCount < maxRetries) {
                                retryCount++;
                                Thread.sleep(retryDelay * retryCount);
                                continue;
                            } else {
                                break;
                            }
                        }
                    }
                } catch (SQLException e) {
                    // Nếu không kiểm tra được trạng thái kết nối, tiếp tục sử dụng kết nối hiện tại
                    Storage.getStorage().getLogger().fine("Không thể kiểm tra trạng thái kết nối: " + e.getMessage());
                }
                
                // Sử dụng try-with-resources để đảm bảo đóng các tài nguyên
                try {
                    // Sử dụng PreparedStatement với tham số để tránh SQL injection
                    ps = conn.prepareStatement("SELECT * FROM " + table + " WHERE player = ?");
                    ps.setString(1, player);
                    ps.setQueryTimeout(30); // Thiết lập timeout 30 giây
                    rs = ps.executeQuery();
                    
                    if (rs.next()) {
                        String statsData = "{}";
                        boolean autoPickup = true;
                        
                        try {
                            statsData = rs.getString("statsData");
                            if (statsData == null) statsData = "{}";
                        } catch (SQLException e) {
                            // Cột có thể chưa tồn tại trong bảng cũ
                            statsData = "{}";
                            Storage.getStorage().getLogger().fine("Không tìm thấy cột statsData: " + e.getMessage());
                        }
                        
                        try {
                            autoPickup = rs.getBoolean("auto_pickup");
                        } catch (SQLException e) {
                            // Cột có thể chưa tồn tại trong bảng cũ
                            autoPickup = true; // Giá trị mặc định là true
                            Storage.getStorage().getLogger().fine("Không tìm thấy cột auto_pickup: " + e.getMessage());
                        }
                        
                        // Kiểm tra trường dữ liệu chính
                        String data = rs.getString("data");
                        if (data == null) data = "{}";
                        int maxStorage = rs.getInt("max");
                        
                        // Tạo đối tượng PlayerData trước khi đóng ResultSet
                        PlayerData result = new PlayerData(player, data, maxStorage, statsData, autoPickup);
                        
                        // Trả về đối tượng PlayerData
                        return result;
                    }
                } finally {
                    // Sử dụng phương thức close() an toàn
                    close(ps, rs);
                }
                
                // Không tìm thấy dữ liệu và không có lỗi, thoát khỏi vòng lặp
                break;
                
            } catch (SQLException ex) {
                // Kiểm tra xem lỗi có phải là database locked không
                boolean isLockError = ex.getMessage().contains("locked") || 
                                     ex.getMessage().contains("busy") ||
                                     ex.getMessage().contains("mutex") ||
                                     ex.getErrorCode() == 5; // SQLite error code for busy/locked
                
                if (isLockError && retryCount < maxRetries) {
                    retryCount++;
                    
                    // Log thông tin về việc thử lại
                    Storage.getStorage().getLogger().warning("Database bị khóa khi truy vấn dữ liệu cho " + player + 
                                                  ". Đang thử lại lần " + retryCount + "/" + maxRetries);
                    
                    try {
                        // Đóng tài nguyên trước khi thử lại sử dụng phương thức an toàn
                        close(ps, rs);
                        ps = null;
                        rs = null;

                        // Trả kết nối về pool (hoặc đóng nếu có vấn đề)
                        if (conn != null) {
                            try {
                                returnConnection(conn);
                                conn = null; // Đánh dấu conn là null để tạo mới ở lần lặp tiếp theo
                            } catch (Exception e) {
                                // Nếu không thể trả về, thử đóng
                                try { conn.close(); } catch (SQLException ignored) {}
                                conn = null;
                            }
                        }

                        // Chờ một chút trước khi thử lại
                        Thread.sleep(retryDelay * retryCount);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    } catch (Exception e) {
                        Storage.getStorage().getLogger().warning("Lỗi khi dọn dẹp tài nguyên để thử lại: " + e.getMessage());
                        break;
                    }
                    
                    // Tiếp tục vòng lặp để thử lại
                    continue;
                } else {
                    // Ghi log lỗi chi tiết
                    Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi SQL khi truy vấn dữ liệu cho " + player + ": " + ex.getMessage(), ex);
                    break;
                }
            } catch (Exception ex) {
                // Xử lý các loại lỗi khác
                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi không xác định khi truy vấn dữ liệu cho " + player + ": " + ex.getMessage(), ex);
                break;
            } finally {
                // Đảm bảo trả kết nối về pool
                if (conn != null) {
                    try {
                        returnConnection(conn);
                    } catch (Exception e) {
                        Storage.getStorage().getLogger().warning("Lỗi khi trả connection về pool: " + e.getMessage());
                        try { conn.close(); } catch (SQLException ignored) {}
                    }
                }
            }
        }
        
        if (retryCount > maxRetries) {
            Storage.getStorage().getLogger().severe("Không thể truy vấn dữ liệu cho " + player + " sau " + maxRetries + " lần thử");
        }
        
        return null;
    }

    public void createTable(@NotNull PlayerData playerData) {
        Connection conn = null;
        PreparedStatement ps = null;
        try {
            conn = getConnection();
            ps = conn.prepareStatement("INSERT INTO " + table + " (player,data,max,statsData,auto_pickup) VALUES(?,?,?,?,?)");
            ps.setString(1, playerData.getPlayer());
            ps.setString(2, playerData.getData());
            ps.setInt(3, playerData.getMax());
            ps.setString(4, playerData.getStatsData());
            ps.setBoolean(5, playerData.isAutoPickup());
            ps.executeUpdate();
        } catch (SQLException ex) {
            Storage.getStorage().getLogger().log(Level.SEVERE, Errors.sqlConnectionExecute(), ex);
        } finally {
            try {
                if (ps != null)
                    ps.close();
                if (conn != null)
                    returnConnection(conn);
            } catch (SQLException ex) {
                Storage.getStorage().getLogger().log(Level.SEVERE, Errors.sqlConnectionClose(), ex);
            }
        }
    }



    public void updateTable(@NotNull PlayerData playerData) {
        // Sử dụng retry đơn giản cho SQLite
        if (this instanceof com.hongminh54.storage.Database.SQLite) {
            com.hongminh54.storage.Database.SQLite sqliteDb = (com.hongminh54.storage.Database.SQLite) this;
            boolean success = sqliteDb.executeWithSimpleRetry(() -> {
                updateTableDirect(playerData);
            }, 3);

            if (!success) {
                Storage.getStorage().getLogger().warning("Không thể cập nhật dữ liệu sau 3 lần thử cho: " + playerData.getPlayer());
            }
        } else {
            updateTableDirect(playerData);
        }
    }

    /**
     * Cập nhật trực tiếp không có retry
     */
    private void updateTableDirect(@NotNull PlayerData playerData) {
        Connection conn = null;
        PreparedStatement ps = null;

        try {
            conn = getConnection();

            // Kiểm tra kết nối
            if (conn == null || conn.isClosed()) {
                Storage.getStorage().getLogger().severe("Không thể cập nhật dữ liệu: kết nối null hoặc đã đóng");
                return;
            }

            // Thực thi câu lệnh SQL đơn giản với autoCommit mặc định
            ps = conn.prepareStatement("UPDATE " + table + " SET data = ?, max = ?, statsData = ?, auto_pickup = ? " +
                    "WHERE player = ?");
            ps.setString(1, playerData.getData());
            ps.setInt(2, playerData.getMax());
            ps.setString(3, playerData.getStatsData());
            ps.setBoolean(4, playerData.isAutoPickup());
            ps.setString(5, playerData.getPlayer());
            ps.executeUpdate();

        } catch (SQLException ex) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi cập nhật dữ liệu: " + ex.getMessage(), ex);
        } finally {
            // Đóng PreparedStatement
            if (ps != null) {
                try {
                    ps.close();
                } catch (SQLException e) {
                    Storage.getStorage().getLogger().warning("Không thể đóng PreparedStatement: " + e.getMessage());
                }
            }

            // Trả kết nối về pool
            if (conn != null) {
                returnConnection(conn);
            }
        }
    }

    public void deleteData(String player) {
        Connection conn = null;
        PreparedStatement ps = null;
        try {
            conn = getConnection();
            ps = conn.prepareStatement("DELETE FROM " + table + " WHERE player = ?");
            ps.setString(1, player);
            ps.executeUpdate();
        } catch (SQLException ex) {
            Storage.getStorage().getLogger().log(Level.SEVERE, Errors.sqlConnectionExecute(), ex);
        } finally {
            try {
                if (ps != null)
                    ps.close();
                if (conn != null)
                    returnConnection(conn);
            } catch (SQLException ex) {
                Storage.getStorage().getLogger().log(Level.SEVERE, Errors.sqlConnectionClose(), ex);
            }
        }
    }

    public void close(PreparedStatement ps, ResultSet rs) {
        try {
            if (rs != null && !rs.isClosed()) {
                rs.close();
            }
        } catch (SQLException ex) {
            Storage.getStorage().getLogger().fine("Lỗi khi đóng ResultSet: " + ex.getMessage());
        }

        try {
            if (ps != null && !ps.isClosed()) {
                ps.close();
            }
        } catch (SQLException ex) {
            Storage.getStorage().getLogger().fine("Lỗi khi đóng PreparedStatement: " + ex.getMessage());
        }
    }
    
    /**
     * Đóng kết nối database khi plugin tắt
     */
    public void closeConnection() {
            // Đóng tất cả các kết nối trong pool
            for (Connection conn : connectionPool) {
            try {

                
                if (!conn.isClosed()) {
                    conn.close();
                }
            } catch (SQLException e) {
                Storage.getStorage().getLogger().warning("Không thể đóng kết nối khi shutdown: " + e.getMessage());
                }
            }
            connectionPool.clear();
            
        // Đóng kết nối chính nếu có
        try {
            if (connection != null && !connection.isClosed()) {

                connection.close();
            }
        } catch (SQLException e) {
            Storage.getStorage().getLogger().log(Level.WARNING, "Không thể đóng kết nối chính khi shutdown", e);
        }
    }
}
