name: Storage
version: '${project.version}'
main: com.hongminh54.storage.Storage
author: VoChiDanh, hongminh54, TYBZI
description: "${project.description}"
api-version: '1.13'
softdepend:
  - WorldGuard
  - PlaceholderAPI
commands:
  storage:
    description: Main command
    aliases:
      - kho
      - kks
  tntenchant:
    description: Thêm hoặc xóa phù phép TNT trên cúp
    aliases:
      - phupheptnt
  viewstorage:
    description: Xem kho khoáng sản của người chơi khác
    aliases:
      - xemkho
      - viewkho
  axeenchant:
    description: Thêm hoặc xóa phù phép trên rìu
    aliases:
      - phuphepriu
  hoeenchant:
    description: Thêm hoặc xóa phù phép trên cuốc
    aliases:
      - phuphepcuoc
  specialmaterial:
    description: Quản lý tài nguyên đặc biệt
    aliases:
      - tainguyendacbiet
  doiblock:
    description: Đ<PERSON>i phôi khoáng sản thành block
    aliases:
      - convertblock
  testskull:
    description: Test command để kiểm tra skull skin (chỉ để debug)
    aliases:
      - testhead
permissions:
  storage.toggle:
    description: quyền bật tắt tự động nhặt vật phẩm
    default: true
  storage.stats:
    description: Xem thống kê hoạt động khai thác
    default: true
  storage.transfer:
    description: Quyền chuyển tài nguyên cho người chơi khác
    default: true
  storage.transfer.others:
    description: Cho phép xem lịch sử chuyển kho của người chơi khác
    default: true
  storage.view.others:
    description: Cho phép xem kho khoáng sản của người chơi khác
    default: op
  storage.leaderboard:
    description: Xem bảng xếp hạng người chơi
    default: true
  storage.event:
    description: Quản lý sự kiện khai thác
    default: op
  storage.doiblock:
    description: Quyền sử dụng tính năng đổi phôi khoáng sản thành block
    default: true
  storage.admin:
    description: quyền admin
    default: op
  storage.admin.add:
    description: quyền thêm vật phẩm cho người chơi
    default: op
  storage.admin.remove:
    description: quyền xóa vật phẩm của người chơi
    default: op
  storage.admin.set:
    description: quyền set vật phẩm cho người chơi
    default: op
  storage.admin.max:
    description: quyền set số lượng tối đa người chơi có thể lưu trữ
    default: op
  storage.admin.reload:
    description: quyền tải lại config
    default: op
  storage.admin.update:
    description: quyền tải xuống bản cập nhật mới nhất từ GitHub
    default: op
  storage.admin.resetleaderboard:
    description: Quyền reset bảng xếp hạng
    default: op
  storage.admin.resetstats:
    description: Quyền reset thống kê của người chơi
    default: op
  storage.admin.cachestats:
    description: Quyền xem thống kê cache của plugin
    default: op
  storage.admin.clearcache:
    description: Quyền xóa và tải lại cache của plugin
    default: op
  storage.admin.specialmaterial:
    description: Quyền quản lý tài nguyên đặc biệt
    default: op
  storage.log.all:
    description: Cho phép xem toàn bộ lịch sử chuyển kho không bị giới hạn
    default: true
  storage.tnt.enchant:
    description: Quyền thêm/xóa phù phép TNT vào cúp
    default: op
  storage.tnt.admin:
    description: Quyền thêm/xóa phù phép TNT cho người chơi khác
    default: op
  storage.axe.enchant:
    description: Quyền thêm/xóa phù phép vào rìu
    default: op
  storage.axe.admin:
    description: Quyền thêm/xóa phù phép rìu cho người chơi khác
    default: op
  storage.hoe.enchant:
    description: Quyền thêm/xóa phù phép vào cuốc
    default: op
  storage.hoe.admin:
    description: Quyền thêm/xóa phù phép cuốc cho người chơi khác
    default: op
  storage.specialmaterial:
    description: Quyền quản lý tài nguyên đặc biệt
    default: op