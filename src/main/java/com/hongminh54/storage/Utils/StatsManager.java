package com.hongminh54.storage.Utils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.logging.Level;

import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.Database.SQLite;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;

public class StatsManager {
    
    // Lưu trữ dữ liệu thống kê của người chơi trong bộ nhớ cache
    private static final HashMap<String, HashMap<String, Integer>> playerStatsCache = new HashMap<>();
    
    // Các loại thống kê
    private static final String TOTAL_MINED = "total_mined";         // Tổng số khối đã đào
    private static final String TOTAL_DEPOSITED = "total_deposited"; // Tổng số đã gửi vào kho
    private static final String TOTAL_WITHDRAWN = "total_withdrawn"; // Tổng số đã rút ra
    private static final String TOTAL_SOLD = "total_sold";           // Tổng số đã bán
    
    // Tất cả các loại thống kê hỗ trợ
    public static final String[] ALL_STATS = {TOTAL_MINED, TOTAL_DEPOSITED, TOTAL_WITHDRAWN, TOTAL_SOLD};
    
    // Map lưu thời điểm cập nhật bảng xếp hạng lần cuối cho mỗi loại thống kê
    private static final Map<String, Long> lastLeaderboardUpdate = new ConcurrentHashMap<>();
    
    // Map lưu thời điểm thông báo đầy kho lần cuối cho mỗi người chơi
    private static final Map<String, Long> lastStorageFullNotification = new ConcurrentHashMap<>();
    
    /**
     * Thời gian trễ giữa các lần thông báo đầy kho (giây)
     */
    private static final int STORAGE_FULL_NOTIFICATION_DELAY = 30;
    
    private static final Map<String, Long> lastLogTime = new HashMap<>();
    private static final long LOG_COOLDOWN = 60000; // 1 phút cooldown
    
    // Batch processing cho stats để tránh deadlock
    private static final Queue<StatsUpdate> statsPendingUpdates = new ConcurrentLinkedQueue<>();
    private static int STATS_BATCH_SIZE = 30; // Giảm số lượng update tối đa trong một batch
    private static long STATS_BATCH_TIMEOUT = 15000; // 15 giây timeout cho batch
    private static int STATS_PROCESS_INTERVAL = 60; // Tăng số tick giữa các lần xử lý
    private static int STATS_RETRY_DELAY = 15000; // 15 giây delay trước khi thử lại khi có lỗi SQLite
    private static boolean STATS_BATCH_ENABLED = true; // Bật/tắt stats batch processing
    private static boolean LOG_STATS_BATCH = true; // Bật/tắt log xử lý batch
    private static boolean isProcessingStats = false; // Tránh nhiều thread xử lý cùng lúc
    private static long lastStatsProcessTime = System.currentTimeMillis();
    
    // Đối tượng khóa cho đồng bộ hóa batch process
    private static final Object STATS_BATCH_LOCK = new Object();
    
    // Map lưu thời điểm hoạt động cuối cùng của người chơi
    private static final Map<String, Map<String, Long>> playerLastActivityTime = new ConcurrentHashMap<>();
    
    // Loại hoạt động
    private static final String ACTIVITY_WITHDRAW = "withdraw";
    private static final String ACTIVITY_DEPOSIT = "deposit";
    private static final String ACTIVITY_SELL = "sell";
    private static final String ACTIVITY_TRANSFER = "transfer";
    
    /**
     * Tải cấu hình stats batch processing từ config.yml
     */
    public static void loadStatsConfig() {
        try {
            FileConfiguration config = File.getConfig();
            if (config.contains("stats_batch_processing")) {
                STATS_BATCH_ENABLED = config.getBoolean("stats_batch_processing.enabled", true);
                
                // Chỉ cập nhật nếu batch processing được bật
                if (STATS_BATCH_ENABLED) {
                    STATS_BATCH_SIZE = config.getInt("stats_batch_processing.batch_size", 30);
                    STATS_BATCH_TIMEOUT = config.getLong("stats_batch_processing.batch_timeout", 15000);
                    STATS_PROCESS_INTERVAL = config.getInt("stats_batch_processing.process_interval", 60);
                    STATS_RETRY_DELAY = config.getInt("stats_batch_processing.retry_delay", 15000);
                    LOG_STATS_BATCH = config.getBoolean("stats_batch_processing.log_batch_processing", true);
                    
                    Storage.getStorage().getLogger().info("Đã tải cấu hình stats batch processing: " +
                            "batch_size=" + STATS_BATCH_SIZE + ", " +
                            "batch_timeout=" + STATS_BATCH_TIMEOUT + "ms, " +
                            "process_interval=" + STATS_PROCESS_INTERVAL + " ticks, " +
                            "retry_delay=" + STATS_RETRY_DELAY + "ms");
                } else {
                    Storage.getStorage().getLogger().info("Stats batch processing đã bị tắt trong cấu hình");
                }
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi tải cấu hình stats batch processing: " + e.getMessage());
            // Giữ nguyên các giá trị mặc định nếu có lỗi
        }
    }
    
    // Stats update class để lưu trữ thông tin cập nhật
    private static class StatsUpdate {
        private final String playerName;
        private final String statsData;
        
        public StatsUpdate(String playerName, String statsData) {
            this.playerName = playerName;
            this.statsData = statsData;
        }
        
        public String getPlayerName() {
            return playerName;
        }
        
        public String getStatsData() {
            return statsData;
        }
    }
    
    /**
     * Thêm cập nhật stats vào hàng đợi xử lý
     */
    private static void addStatsToQueue(Player player) {
        // Chỉ thêm vào hàng đợi nếu có dữ liệu
        if (player != null && playerStatsCache.containsKey(player.getName())) {
            String statsData = convertStatsToString(player);
            statsPendingUpdates.add(new StatsUpdate(player.getName(), statsData));
            
            // Kiểm tra xem có cần xử lý batch không
            long now = System.currentTimeMillis();
            if (statsPendingUpdates.size() >= STATS_BATCH_SIZE || 
                    (now - lastStatsProcessTime >= STATS_BATCH_TIMEOUT && !statsPendingUpdates.isEmpty())) {
                processStatsBatch();
            }
        }
    }
    
    /**
     * Xử lý batch cập nhật thống kê
     */
    private static void processStatsBatch() {
        if (isProcessingStats) {
            return; // Đang xử lý, tránh trùng lặp
        }
        
        // Kiểm tra xem có cập nhật nào không
        if (statsPendingUpdates.isEmpty()) {
            return;
        }
        
        // Đánh dấu đang xử lý
        isProcessingStats = true;
        
        try {
            // Giới hạn kích thước batch
            int batchSize = Math.min(STATS_BATCH_SIZE, statsPendingUpdates.size());
            final List<StatsUpdate> batchUpdates = new ArrayList<>(batchSize);
            
            // Lấy ra các cập nhật cần xử lý
            for (int i = 0; i < batchSize && !statsPendingUpdates.isEmpty(); i++) {
                StatsUpdate update = statsPendingUpdates.poll();
                if (update != null) {
                    batchUpdates.add(update);
                }
            }
            
            if (batchUpdates.isEmpty()) {
                isProcessingStats = false;
                return;
            }
            
            // Ghi log về số lượng cập nhật
            if (LOG_STATS_BATCH && shouldLog("stats_batch_start")) {
                Storage.getStorage().getLogger().info("Bắt đầu xử lý batch stats với " + batchUpdates.size() + " cập nhật");
            }
            
            // Xử lý batch trong một thread riêng biệt
            Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
                Connection conn = null;
                PreparedStatement ps = null;
                
                try {
                    // Kiểm tra xem thread hiện tại có bị gián đoạn không
                    if (Thread.currentThread().isInterrupted()) {
                        Storage.getStorage().getLogger().warning("Thread bị gián đoạn, hủy xử lý batch stats");
                        // Thêm lại vào hàng đợi để xử lý sau
                        for (StatsUpdate update : batchUpdates) {
                            statsPendingUpdates.add(update);
                        }
                        return;
                    }
                
                    // Lấy kết nối đơn giản
                    conn = Storage.db.getConnection();

                    if (conn == null) {
                        Storage.getStorage().getLogger().warning("Không thể lấy kết nối SQLite, hủy xử lý batch stats");
                        // Thêm lại vào hàng đợi để xử lý sau
                        for (StatsUpdate update : batchUpdates) {
                            statsPendingUpdates.add(update);
                        }
                        return;
                    }
                    
                    // SQL query để cập nhật stats
                    ps = conn.prepareStatement("UPDATE PlayerData SET statsData = ? WHERE player = ?");
                    
                    // Thêm các cập nhật vào batch
                    for (StatsUpdate update : batchUpdates) {
                        ps.setString(1, update.getStatsData());
                        ps.setString(2, update.getPlayerName());
                        ps.addBatch();
                    }
                    
                    // Thực hiện batch đơn giản
                    try {
                        ps.executeBatch();

                        // Ghi log số lượng cập nhật thành công
                        if (LOG_STATS_BATCH && shouldLog("stats_batch_complete")) {
                            Storage.getStorage().getLogger().info("Hoàn thành xử lý batch stats với " + batchUpdates.size() + " cập nhật");
                        }
                    } catch (SQLException batchEx) {
                        if (batchEx.getMessage() != null &&
                            (batchEx.getMessage().contains("SQLITE_BUSY") ||
                             batchEx.getMessage().contains("database is locked") ||
                             batchEx.getMessage().contains("database table is locked"))) {
                            Storage.getStorage().getLogger().warning("Database bị khóa khi thực hiện batch, sẽ thử lại sau: " + batchEx.getMessage());
                            // Thêm lại vào hàng đợi để xử lý sau
                            for (StatsUpdate update : batchUpdates) {
                                statsPendingUpdates.add(update);
                            }
                        } else {
                            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi thực hiện batch stats", batchEx);
                        }
                    }
                } catch (SQLException ex) {
                    // Kiểm tra xem có phải lỗi do database bị khóa không
                    boolean isSQLiteBusy = ex.getMessage() != null &&
                        (ex.getMessage().contains("SQLITE_BUSY") ||
                         ex.getMessage().contains("database is locked") ||
                         ex.getMessage().contains("database table is locked") ||
                         ex.getMessage().contains("Connection is closed") ||
                         ex.getMessage().contains("database has been closed") ||
                         ex.getMessage().contains("unfinalized prepared statement") ||
                         ex.getMessage().contains("pthread_mutex_lock") ||
                         ex.getMessage().contains("Disk I/O error") ||
                         ex.getMessage().contains("database disk image is malformed"));
                    
                    if (isSQLiteBusy) {
                        Storage.getStorage().getLogger().warning("SQLite error: " + ex.getMessage() + ", thêm lại vào hàng đợi để thử lại sau");
                        
                        // Thêm lại vào hàng đợi nếu lỗi SQLITE_BUSY
                        for (StatsUpdate update : batchUpdates) {
                            statsPendingUpdates.add(update);
                        }
                        
                        // Thêm delay trước khi thử lại
                        lastStatsProcessTime = System.currentTimeMillis() + STATS_RETRY_DELAY;
                    } else {
                        Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi SQL khi cập nhật batch stats: " + ex.getMessage(), ex);
                        
                        // Thêm lại vào hàng đợi nếu có lỗi SQL khác
                        for (StatsUpdate update : batchUpdates) {
                            statsPendingUpdates.add(update);
                        }
                    }
                } finally {
                    // Đóng statement
                    try {
                        if (ps != null && !ps.isClosed()) {
                            ps.close();
                        }
                    } catch (SQLException ex) {
                        Storage.getStorage().getLogger().warning("Không thể đóng PreparedStatement: " + ex.getMessage());
                    }
                    

                    
                    // Trả kết nối về pool
                    try {
                        if (conn != null) {
                            Storage.db.returnConnection(conn);
                        }
                    } catch (Exception ex) {
                        Storage.getStorage().getLogger().warning("Không thể trả kết nối về pool: " + ex.getMessage());
                    }
                    
                    // Đánh dấu đã xử lý xong
                    isProcessingStats = false;
                    lastStatsProcessTime = System.currentTimeMillis();
                }
            });
            
            // Cho phép tiếp tục xử lý các batch tiếp theo khi hết process_interval
            if (!statsPendingUpdates.isEmpty()) {
                Bukkit.getScheduler().runTaskLater(Storage.getStorage(), StatsManager::processStatsBatch, STATS_PROCESS_INTERVAL);
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi không xác định khi xử lý batch stats: " + e.getMessage(), e);
            isProcessingStats = false; // Đảm bảo trạng thái được reset khi có lỗi
        }
    }
    
    /**
     * Gửi thông báo đầy kho cho người chơi
     */
    public static void sendStorageFullNotification(Player player, String material, int currentAmount, int maxAmount) {
        long currentTime = System.currentTimeMillis() / 1000;
        Long lastNotification = lastStorageFullNotification.get(player.getName());
        
        // Chỉ gửi thông báo nếu đã đủ thời gian trễ
        if (lastNotification == null || currentTime - lastNotification >= STORAGE_FULL_NOTIFICATION_DELAY) {
            // Kiểm tra material có null không
            if (material == null) {
                // Log lỗi và thoát
                Storage.getStorage().getLogger().warning("Material null khi gửi thông báo đầy kho cho " + player.getName());
                return;
            }

            String materialName = File.getConfig().getString("items." + material);
            if (materialName == null) {
                materialName = material.replace("_", " ");
            }
            
            // Tạo thông báo chi tiết
            String message = Chat.colorize("&c&l[Kho Khoáng Sản] &eKho của bạn đã đầy!");
            String details = Chat.colorize("&7Vật phẩm: &f" + materialName);
            String amount = Chat.colorize("&7Số lượng: &f" + currentAmount + "&7/&f" + maxAmount);
            String suggestion = Chat.colorize("&7Vui lòng rút bớt vật phẩm để có thể khai thác tiếp!");
            
            // Gửi thông báo
            player.sendMessage("");
            player.sendMessage(message);
            player.sendMessage(details);
            player.sendMessage(amount);
            player.sendMessage(suggestion);
            player.sendMessage("");
            
            // Cập nhật thời gian thông báo cuối cùng
            lastStorageFullNotification.put(player.getName(), currentTime);
        }
    }
    
    /**
     * Xóa thông báo đầy kho khỏi cache khi người chơi đăng xuất
     */
    public static void removeStorageFullNotification(String playerName) {
        lastStorageFullNotification.remove(playerName);
    }
    
    /**
     * Lấy thời gian trễ cập nhật bảng xếp hạng từ config
     */
    private static int getLeaderboardUpdateDelay() {
        return File.getConfig().getInt("settings.leaderboard_update_delay", 30);
    }
    
    /**
     * Khởi tạo dữ liệu thống kê mới cho người chơi
     */
    public static void initPlayerStats(Player player) {
        HashMap<String, Integer> statsMap = new HashMap<>();
        statsMap.put(TOTAL_MINED, 0);
        statsMap.put(TOTAL_DEPOSITED, 0);
        statsMap.put(TOTAL_WITHDRAWN, 0);
        statsMap.put(TOTAL_SOLD, 0);
        
        playerStatsCache.put(player.getName(), statsMap);
    }
    
    /**
     * Tải dữ liệu thống kê của người chơi từ cơ sở dữ liệu
     * @param player Người chơi cần tải dữ liệu
     * @param forceRefresh true nếu muốn tải lại từ database bất kể cache
     */
    public static void loadPlayerStats(@NotNull Player player, boolean forceRefresh) {
        if (player == null) return;
        
        // Nếu không yêu cầu tải lại và đã có dữ liệu trong cache thì không làm gì
        if (!forceRefresh && playerStatsCache.containsKey(player.getName())) {
            return;
        }
        
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = Storage.db.getConnection();
            if (conn == null) {
                Storage.getStorage().getLogger().severe("Không thể kết nối đến database để tải thống kê");
                return;
            }
            
            ps = conn.prepareStatement("SELECT statsData FROM " + Storage.db.table + " WHERE player = ?");
            ps.setString(1, player.getName());
            rs = ps.executeQuery();
            
            HashMap<String, Integer> statsMap = new HashMap<>();
            
            if (rs.next()) {
                String statsData = rs.getString("statsData");
                if (statsData != null && !statsData.isEmpty() && !statsData.equals("{}")) {
                    // Phân tích dữ liệu thống kê
                    statsData = statsData.substring(1, statsData.length() - 1); // Bỏ {}
                    String[] pairs = statsData.split(",");
                    
                    for (String pair : pairs) {
                        try {
                            String[] keyValue = pair.split("=");
                            if (keyValue.length == 2) {
                                String key = keyValue[0].trim();
                                int value = Integer.parseInt(keyValue[1].trim());
                                if (value >= 0) { // Chỉ chấp nhận giá trị không âm
                                    statsMap.put(key, value);
                                }
                            }
                        } catch (Exception e) {
                            Storage.getStorage().getLogger().warning("Lỗi khi phân tích dữ liệu thống kê cho " + player.getName() + ": " + pair);
                        }
                    }
                }
            }
            
            // Đảm bảo có tất cả các loại thống kê
            statsMap.putIfAbsent(TOTAL_MINED, 0);
            statsMap.putIfAbsent(TOTAL_DEPOSITED, 0);
            statsMap.putIfAbsent(TOTAL_WITHDRAWN, 0);
            statsMap.putIfAbsent(TOTAL_SOLD, 0);
            
            // Lưu vào cache
            playerStatsCache.put(player.getName(), statsMap);
            
            if (forceRefresh) {
                Storage.getStorage().getLogger().info("Đã tải lại dữ liệu thống kê của " + player.getName() + " từ database");
            }
            
        } catch (SQLException e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi tải thống kê cho " + player.getName() + ": " + e.getMessage(), e);
            // Khởi tạo dữ liệu mới nếu có lỗi
            initPlayerStats(player);
        } finally {
            try {
                if (rs != null) rs.close();
                if (ps != null) ps.close();
                if (conn != null) Storage.db.returnConnection(conn);
            } catch (SQLException e) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "Không thể đóng kết nối database", e);
            }
        }
    }
    
    /**
     * Tải dữ liệu thống kê của người chơi (overload cho tương thích ngược)
     * @param player Người chơi cần tải dữ liệu
     */
    public static void loadPlayerStats(@NotNull Player player) {
        loadPlayerStats(player, false);
    }
    
    /**
     * Chuyển đổi dữ liệu thống kê của người chơi sang dạng chuỗi để lưu vào cơ sở dữ liệu
     * @param player Người chơi
     * @return Chuỗi dữ liệu thống kê đã được định dạng
     */
    public static String convertStatsToString(@NotNull Player player) {
        if (!playerStatsCache.containsKey(player.getName())) {
            return "{}";
        }
        
        HashMap<String, Integer> stats = playerStatsCache.get(player.getName());
        if (stats.isEmpty()) {
            return "{}";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        
        boolean first = true;
        for (Map.Entry<String, Integer> entry : stats.entrySet()) {
            if (!first) {
                sb.append(",");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
            first = false;
        }
        
        sb.append("}");
        
        // Nén dữ liệu thống kê nếu đủ lớn
        return CompressUtils.compressString(sb.toString());
    }
    
    private static boolean shouldLog(String key) {
        long currentTime = System.currentTimeMillis();
        Long lastTime = lastLogTime.get(key);
        if (lastTime == null || currentTime - lastTime >= LOG_COOLDOWN) {
            lastLogTime.put(key, currentTime);
            return true;
        }
        return false;
    }
    
    /**
     * Kiểm tra và cập nhật bảng xếp hạng nếu cần
     */
    private static void checkAndUpdateLeaderboard(String statType) {
        long currentTime = System.currentTimeMillis();
        Long lastUpdate = lastLeaderboardUpdate.get(statType);
        
        // Kiểm tra xem đã đến lúc cập nhật bảng xếp hạng chưa
        // Mặc định cập nhật mỗi 45 giây (có thể cấu hình)
        if (lastUpdate == null || (currentTime - lastUpdate) >= (getLeaderboardUpdateDelay() * 1000)) {
            // Cập nhật thời gian
            lastLeaderboardUpdate.put(statType, currentTime);
            
            // Sử dụng hàng đợi để cập nhật bảng xếp hạng
            LeaderboardManager.requestUpdate(statType, false);
        }
    }
    
    /**
     * Tăng giá trị thống kê
     */
    public static void incrementStat(Player player, String statType, int amount) {
        if (amount < 0) {
            Storage.getStorage().getLogger().warning("Không thể tăng số liệu âm cho " + player.getName() + ": " + amount);
            return;
        }

        if (!playerStatsCache.containsKey(player.getName())) {
            loadPlayerStats(player);
        }
        
        HashMap<String, Integer> statsMap = playerStatsCache.get(player.getName());
        if (statsMap == null) {
            initPlayerStats(player);
            statsMap = playerStatsCache.get(player.getName());
        }

        synchronized (statsMap) {
            int currentValue = statsMap.getOrDefault(statType, 0);
            // Kiểm tra tràn số
            if (currentValue + amount < 0) {
                Storage.getStorage().getLogger().warning("Phát hiện tràn số cho " + player.getName() + " ở " + statType);
                statsMap.put(statType, Integer.MAX_VALUE);
            } else {
                statsMap.put(statType, currentValue + amount);
            }
            
            // Lưu vào bộ nhớ cache
            playerStatsCache.put(player.getName(), statsMap);
        }

        // Lưu dữ liệu vào cơ sở dữ liệu ngay lập tức
        savePlayerStats(player);
    }
    
    /**
     * Ghi nhận hoạt động khai thác
     * @param player Người chơi
     * @param amount Số lượng block (luôn là 1 vì chỉ tính số block đã đào)
     */
    public static void recordMining(Player player, int amount) {
        // Đảm bảo chỉ tính 1 block mỗi lần gọi
        if (amount != 1) {
            Storage.getStorage().getLogger().warning("Phát hiện ghi nhận số block không hợp lệ: " + amount + " cho " + player.getName());
            amount = 1;
        }
        
        incrementStat(player, TOTAL_MINED, amount);
        
        // Đảm bảo lưu vào database ngay lập tức
        savePlayerStats(player);
        
        // Kiểm tra và cập nhật bảng xếp hạng nếu cần
        checkAndUpdateLeaderboard(TOTAL_MINED);
    }
    
    /**
     * Ghi nhận hoạt động gửi vào kho
     */
    public static void recordDeposit(Player player, int amount) {
        incrementStat(player, TOTAL_DEPOSITED, amount);
        
        // Ghi lại thời điểm hoạt động
        recordActivity(player.getName(), ACTIVITY_DEPOSIT);
        
        // Đảm bảo lưu vào database ngay lập tức
        savePlayerStats(player);
        
        // Cập nhật bảng xếp hạng
        checkAndUpdateLeaderboard(TOTAL_DEPOSITED);
    }
    
    /**
     * Ghi nhận hoạt động rút ra khỏi kho
     */
    public static void recordWithdraw(Player player, int amount) {
        incrementStat(player, TOTAL_WITHDRAWN, amount);
        
        // Ghi lại thời điểm hoạt động
        recordActivity(player.getName(), ACTIVITY_WITHDRAW);
        
        // Đảm bảo lưu vào database ngay lập tức
        savePlayerStats(player);
        
        // Cập nhật bảng xếp hạng
        checkAndUpdateLeaderboard(TOTAL_WITHDRAWN);
    }
    
    /**
     * Ghi nhận hoạt động bán
     */
    public static void recordSell(Player player, int amount) {
        incrementStat(player, TOTAL_SOLD, amount);
        
        // Ghi lại thời điểm hoạt động
        recordActivity(player.getName(), ACTIVITY_SELL);
        
        // Đảm bảo lưu vào database ngay lập tức
        savePlayerStats(player);
        
        // Cập nhật bảng xếp hạng
        checkAndUpdateLeaderboard(TOTAL_SOLD);
    }
    
    /**
     * Ghi lại hoạt động chuyển khoáng sản
     */
    public static void recordTransfer(Player player) {
        // Ghi lại thời điểm hoạt động
        recordActivity(player.getName(), ACTIVITY_TRANSFER);
    }
    
    /**
     * Ghi nhận thời điểm hoạt động
     */
    private static void recordActivity(String playerName, String activityType) {
        playerLastActivityTime.computeIfAbsent(playerName, k -> new ConcurrentHashMap<>())
                              .put(activityType, System.currentTimeMillis());
    }
    
    /**
     * Kiểm tra nếu người chơi có hoạt động rút/bán/chuyển gần đây
     * @param player Người chơi cần kiểm tra
     * @param sinceTime Thời điểm bắt đầu kiểm tra (millis)
     * @return true nếu có hoạt động gần đây
     */
    public static boolean hasRecentActivity(Player player, long sinceTime) {
        if (player == null) return false;
        
        Map<String, Long> activities = playerLastActivityTime.get(player.getName());
        if (activities == null) return false;
        
        // Kiểm tra từng loại hoạt động
        for (String activityType : new String[]{ACTIVITY_WITHDRAW, ACTIVITY_SELL, ACTIVITY_TRANSFER}) {
            Long lastTime = activities.get(activityType);
            if (lastTime != null && lastTime > sinceTime) {
                Storage.getStorage().getLogger().info("Phát hiện hoạt động " + activityType + 
                    " của " + player.getName() + " vào " + new java.util.Date(lastTime));
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Lấy tổng số đã khai thác
     */
    public static int getTotalMined(Player player) {
        if (!playerStatsCache.containsKey(player.getName())) {
            loadPlayerStats(player);
        }
        return playerStatsCache.get(player.getName()).getOrDefault(TOTAL_MINED, 0);
    }
    
    /**
     * Lấy tổng số đã gửi vào kho
     */
    public static int getTotalDeposited(Player player) {
        if (!playerStatsCache.containsKey(player.getName())) {
            loadPlayerStats(player);
        }
        return playerStatsCache.get(player.getName()).getOrDefault(TOTAL_DEPOSITED, 0);
    }
    
    /**
     * Lấy tổng số đã rút
     */
    public static int getTotalWithdrawn(Player player) {
        if (!playerStatsCache.containsKey(player.getName())) {
            loadPlayerStats(player);
        }
        return playerStatsCache.get(player.getName()).getOrDefault(TOTAL_WITHDRAWN, 0);
    }
    
    /**
     * Lấy tổng số đã bán
     */
    public static int getTotalSold(Player player) {
        if (!playerStatsCache.containsKey(player.getName())) {
            loadPlayerStats(player);
        }
        return playerStatsCache.get(player.getName()).getOrDefault(TOTAL_SOLD, 0);
    }
    
    /**
     * Lấy danh sách các thông tin thống kê
     */
    public static List<String> getStatsInfo(Player player) {
        if (!playerStatsCache.containsKey(player.getName())) {
            loadPlayerStats(player);
        }
        
        List<String> info = new ArrayList<>();
        HashMap<String, Integer> statsMap = playerStatsCache.get(player.getName());
        
        info.add("&eTổng đã khai thác: &f" + statsMap.getOrDefault(TOTAL_MINED, 0) + " vật phẩm");
        info.add("&eTổng đã gửi vào kho: &f" + statsMap.getOrDefault(TOTAL_DEPOSITED, 0) + " vật phẩm");
        info.add("&eTổng đã rút ra: &f" + statsMap.getOrDefault(TOTAL_WITHDRAWN, 0) + " vật phẩm");
        info.add("&eTổng đã bán: &f" + statsMap.getOrDefault(TOTAL_SOLD, 0) + " vật phẩm");
        
        return info;
    }
    
    /**
     * Xóa dữ liệu thống kê của người chơi khỏi bộ nhớ cache khi họ đăng xuất
     * @param playerName Tên của người chơi
     */
    public static void removeFromCache(String playerName) {
        if (playerStatsCache.containsKey(playerName)) {
            playerStatsCache.remove(playerName);
            Storage.getStorage().getLogger().info("Đã xóa dữ liệu thống kê khỏi cache cho người chơi " + playerName);
        }
    }
    
    /**
     * Lưu dữ liệu thống kê của người chơi
     * @param player Người chơi cần lưu dữ liệu
     */
    public static void savePlayerStats(Player player) {
        if (player == null) return;
        
        try {
            // Thay vì lưu trực tiếp, chỉ thêm vào hàng đợi xử lý
            addStatsToQueue(player);
        } catch (Exception e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi thêm stats vào hàng đợi: " + e.getMessage(), e);
        }
    }
    
    /**
     * Lưu dữ liệu thống kê của người chơi bất đồng bộ
     * Phương thức này sử dụng batch processing để tránh deadlock
     */
    public static void savePlayerStatsAsync(Player player) {
        if (player == null) return;
        
        // Thêm vào hàng đợi thay vì lưu riêng lẻ
        addStatsToQueue(player);
    }
    
    /**
     * Reset thống kê của một người chơi cụ thể
     * 
     * @param playerName Tên người chơi
     * @return true nếu reset thành công, false nếu thất bại
     */
    public static boolean resetPlayerStats(String playerName) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        
        try {
            conn = Storage.db.getConnection();
            if (conn == null) {
                Storage.getStorage().getLogger().severe("Không thể kết nối đến database!");
                return false;
            }
            
            // Lưu trạng thái toggle hiện tại
            boolean currentToggle = false;
            Player player = Bukkit.getPlayer(playerName);
            if (player != null) {
                currentToggle = MineManager.toggle.getOrDefault(player, false);
            }
            
            // Kiểm tra xem người chơi có tồn tại trong database không
            ps = conn.prepareStatement("SELECT COUNT(*) FROM " + Storage.db.table + " WHERE player = ?");
            ps.setString(1, playerName);
            rs = ps.executeQuery();
            rs.next();
            boolean playerExists = rs.getInt(1) > 0;
            
            if (!playerExists) {
                return false;
            }
            
            // Reset dữ liệu thống kê
            ps.close();
            ps = conn.prepareStatement("UPDATE " + Storage.db.table + " SET statsData = '{}' WHERE player = ?");
            ps.setString(1, playerName);
            int rowsAffected = ps.executeUpdate();
            
            // Xóa khỏi cache
            if (playerStatsCache.containsKey(playerName)) {
                playerStatsCache.remove(playerName);
            }
            
            if (player != null) {
                // Tạo mới dữ liệu thống kê trong cache
                HashMap<String, Integer> stats = new HashMap<>();
                for (String stat : ALL_STATS) {
                    stats.put(stat, 0);
                }
                playerStatsCache.put(playerName, stats);
                
                // Khôi phục trạng thái toggle
                MineManager.toggle.put(player, currentToggle);
            }
            
            return rowsAffected > 0;
        } catch (SQLException e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi reset thống kê người chơi: " + e.getMessage(), e);
            return false;
        } finally {
            try {
                if (rs != null) rs.close();
                if (ps != null) ps.close();
                if (conn != null) Storage.db.returnConnection(conn);
            } catch (SQLException e) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi đóng kết nối: " + e.getMessage(), e);
            }
        }
    }
    
    /**
     * Phương thức debug để kiểm tra dữ liệu thống kê của người chơi
     */
    public static void debugPlayerStats(Player player) {
        if (!playerStatsCache.containsKey(player.getName())) {
            loadPlayerStats(player);
        }
        
        HashMap<String, Integer> statsMap = playerStatsCache.get(player.getName());
        if (statsMap == null) {
            Storage.getStorage().getLogger().warning("Không tìm thấy dữ liệu thống kê cho " + player.getName());
            return;
        }
        
        Storage.getStorage().getLogger().info("=== Debug Stats cho " + player.getName() + " ===");
        Storage.getStorage().getLogger().info("Tổng đã khai thác: " + statsMap.getOrDefault(TOTAL_MINED, 0));
        Storage.getStorage().getLogger().info("Tổng đã gửi vào kho: " + statsMap.getOrDefault(TOTAL_DEPOSITED, 0));
        Storage.getStorage().getLogger().info("Tổng đã rút ra: " + statsMap.getOrDefault(TOTAL_WITHDRAWN, 0));
        Storage.getStorage().getLogger().info("Tổng đã bán: " + statsMap.getOrDefault(TOTAL_SOLD, 0));
    }
    
    /**
     * Tối ưu cache thống kê cho môi trường có nhiều người chơi
     * Điều chỉnh các tham số batch và xử lý dữ liệu
     *
     * @param playerCount Số lượng người chơi hiện tại
     * @param isHighLoad true nếu server đang trong trạng thái tải cao
     * @param isLowTps true nếu TPS của server thấp
     */
    public static void optimizeForHighLoad(int playerCount, boolean isHighLoad, boolean isLowTps) {
        try {
            // Điều chỉnh các tham số batch dựa trên tải và TPS
            if (isHighLoad) {
                // Giảm kích thước batch để tránh quá tải CPU
                STATS_BATCH_SIZE = isLowTps ? 10 : 20;
                // Tăng thời gian giữa các xử lý batch
                STATS_PROCESS_INTERVAL = isLowTps ? 100 : 80;
            } else {
                // Khôi phục về giá trị mặc định từ config
                FileConfiguration config = File.getConfig();
                STATS_BATCH_SIZE = config.getInt("stats_batch_processing.batch_size", 30);
                STATS_PROCESS_INTERVAL = config.getInt("stats_batch_processing.process_interval", 60);
            }
            
            // Nếu TPS rất thấp, tăng batch timeout để tránh lỗi
            if (isLowTps) {
                STATS_BATCH_TIMEOUT = 30000; // 30 giây
            } else {
                // Khôi phục giá trị mặc định
                STATS_BATCH_TIMEOUT = File.getConfig().getLong("stats_batch_processing.batch_timeout", 15000);
            }
            
            // Làm sạch cache không sử dụng để giải phóng bộ nhớ
            cleanupStatsCache(playerCount);
            
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info(String.format(
                    "Đã tối ưu stats cache (Batch size: %d, Interval: %d, Timeout: %d ms)",
                    STATS_BATCH_SIZE, STATS_PROCESS_INTERVAL, STATS_BATCH_TIMEOUT
                ));
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Không thể tối ưu stats cache: " + e.getMessage());
        }
    }
    
    /**
     * Dọn dẹp cache thống kê người chơi không còn hoạt động
     * 
     * @param onlinePlayerCount Số lượng người chơi đang online
     */
    private static void cleanupStatsCache(int onlinePlayerCount) {
        // Chỉ làm sạch cache khi có nhiều người chơi
        if (onlinePlayerCount < 50) return;
        
        try {
            // Danh sách các người chơi cần xóa khỏi cache
            List<String> playersToRemove = new ArrayList<>();
            
            // Kiểm tra mỗi người chơi trong cache
            for (String playerName : playerStatsCache.keySet()) {
                // Lấy thời gian hoạt động cuối cùng
                Map<String, Long> activities = playerLastActivityTime.getOrDefault(playerName, new HashMap<>());
                
                // Kiểm tra xem người chơi còn online không
                Player player = Bukkit.getPlayer(playerName);
                if (player == null || !player.isOnline()) {
                    // Người chơi đã offline, kiểm tra thời gian không hoạt động
                    long lastActivity = 0;
                    for (Long time : activities.values()) {
                        lastActivity = Math.max(lastActivity, time);
                    }
                    
                    // Nếu không hoạt động trong 5 phút, xóa khỏi cache
                    if (System.currentTimeMillis() - lastActivity > 300000) {
                        playersToRemove.add(playerName);
                    }
                }
            }
            
            // Xóa người chơi không hoạt động khỏi cache
            for (String playerName : playersToRemove) {
                playerStatsCache.remove(playerName);
                playerLastActivityTime.remove(playerName);
                
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().fine("Đã xóa dữ liệu cache của " + playerName + " do không hoạt động");
                }
            }
            
            // Ghi log kết quả làm sạch
            if (!playersToRemove.isEmpty() && Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("Đã dọn dẹp stats cache cho " + playersToRemove.size() + " người chơi không hoạt động");
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi dọn dẹp stats cache: " + e.getMessage());
        }
    }
    
    public static void savePlayerStatsSynchronously(Player player) {
        if (player == null) return;
        
        try {
            String playerName = player.getName();
            if (!playerStatsCache.containsKey(playerName)) {
                return; // Không có dữ liệu để lưu
            }
            
            // Tạo đối tượng StatsUpdate nhưng không thêm vào hàng đợi
            String statsData = convertStatsToString(player);
            StatsUpdate update = new StatsUpdate(playerName, statsData);
            
            // Xử lý ngay lập tức
            List<StatsUpdate> updates = new ArrayList<>();
            updates.add(update);
            processBatchSynchronously(updates);
            
            Storage.getStorage().getLogger().info("Đã lưu dữ liệu thống kê đồng bộ cho " + playerName);
        } catch (Exception e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi lưu dữ liệu thống kê đồng bộ: " + e.getMessage(), e);
        }
    }
    
    /**
     * Xử lý batch đồng bộ khi plugin đã tắt
     * @param batchUpdates Danh sách cập nhật thống kê cần xử lý
     */
    private static void processBatchSynchronously(List<StatsUpdate> batchUpdates) {
        Connection conn = null;
        PreparedStatement ps = null;
        Statement stmt = null;
        boolean isTransactionActive = false;
        
        try {
            // Thực hiện cập nhật theo batch
            conn = Storage.db.getConnection();
            if (conn == null) {
                Storage.getStorage().getLogger().warning("Không thể tạo kết nối database khi xử lý batch đồng bộ");
                return;
            }
            
            // Xử lý đồng bộ hóa - sử dụng khóa đối tượng Database để đảm bảo chỉ một luồng thực hiện transaction
            synchronized (Storage.db) {
                // Lưu trạng thái auto-commit hiện tại
                boolean wasAutoCommit = true;
                try {
                    // Kiểm tra connection có đóng không
                    if (conn.isClosed()) {
                        Storage.getStorage().getLogger().warning("Kết nối đã đóng trước khi xử lý batch đồng bộ");
                        return;
                    }
                    
                    wasAutoCommit = conn.getAutoCommit();
                } catch (SQLException ex) {
                    // Đảm bảo mặc định là true nếu có lỗi
                    wasAutoCommit = true;
                    Storage.getStorage().getLogger().warning("Không thể lấy trạng thái auto-commit: " + ex.getMessage());
                }
                
                try {
                    // Tắt autocommit để sử dụng transaction
                    try {
                        conn.setAutoCommit(false);
                        isTransactionActive = true; // Đánh dấu transaction đã bắt đầu
                    } catch (SQLException e) {
                        Storage.getStorage().getLogger().warning("Lỗi khi tắt auto-commit: " + e.getMessage() + 
                                                               ". Sẽ tiếp tục với auto-commit=true");
                        // Không đặt isTransactionActive = true nếu không thể tắt autoCommit
                    }
                    
                    // Thiết lập PRAGMA an toàn - chỉ busy_timeout
                    try {
                        stmt = conn.createStatement();
                        // Chỉ thiết lập busy_timeout vì nó an toàn trong transaction
                        stmt.execute("PRAGMA busy_timeout = 30000");
                        stmt.close();
                        stmt = null;
                    } catch (SQLException e) {
                        Storage.getStorage().getLogger().fine("Không thể thiết lập PRAGMA SQLite: " + e.getMessage());
                        // Tiếp tục vì đây không phải lỗi nghiêm trọng
                    }
                    
                    // Chuẩn bị statement
                    ps = conn.prepareStatement("UPDATE " + Storage.db.table + " SET statsData = ? WHERE player = ?");
                    
                    // Nhóm các cập nhật theo người chơi để tối ưu hóa lưu trữ
                    Map<String, String> consolidatedUpdates = new HashMap<>();
                    
                    // Tổng hợp các cập nhật cho cùng một người chơi
                    for (StatsUpdate update : batchUpdates) {
                        consolidatedUpdates.put(update.getPlayerName(), update.getStatsData());
                    }
                    
                    // Thêm các cập nhật vào batch
                    for (Map.Entry<String, String> entry : consolidatedUpdates.entrySet()) {
                        String playerName = entry.getKey();
                        String statsData = entry.getValue();
                        
                        // Nén dữ liệu trước khi lưu
                        statsData = CompressUtils.compressString(statsData);
                        
                        ps.setString(1, statsData);
                        ps.setString(2, playerName);
                        ps.addBatch();
                    }
                    
                    // Thực hiện batch với xử lý lỗi cụ thể
                    try {
                    ps.executeBatch();
                    } catch (SQLException batchEx) {
                        if (batchEx.getMessage() != null && 
                            (batchEx.getMessage().contains("SQLITE_BUSY") || 
                             batchEx.getMessage().contains("database is locked") ||
                             batchEx.getMessage().contains("database table is locked"))) {
                            Storage.getStorage().getLogger().warning("Database bị khóa khi thực hiện batch, sẽ thử lại sau: " + batchEx.getMessage());
                            // Thêm lại vào hàng đợi để xử lý sau
                            for (StatsUpdate update : batchUpdates) {
                                statsPendingUpdates.add(update);
                            }
                            
                            // Thử rollback transaction
                            try {
                                if (isTransactionActive && !conn.isClosed()) {
                                    conn.rollback();
                                    isTransactionActive = false;
                                }
                            } catch (SQLException rollbackEx) {
                                Storage.getStorage().getLogger().warning("Không thể rollback transaction: " + rollbackEx.getMessage());
                            }
                        } else {
                            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi thực hiện batch stats", batchEx);
                            
                            // Thử rollback transaction
                            try {
                                if (isTransactionActive && !conn.isClosed()) {
                                    conn.rollback();
                                    isTransactionActive = false;
                                }
                            } catch (SQLException rollbackEx) {
                                Storage.getStorage().getLogger().warning("Không thể rollback transaction: " + rollbackEx.getMessage());
                            }
                        }
                    }
                    
                    // Commit chỉ khi transaction đang hoạt động (autoCommit=false)
                    if (isTransactionActive) {
                        try {
                            // Kiểm tra connection có đóng không trước khi commit
                            if (!conn.isClosed() && !conn.getAutoCommit()) {
                                conn.commit();
                            }
                        } catch (SQLException commitEx) {
                            Storage.getStorage().getLogger().warning("Lỗi khi commit transaction: " + commitEx.getMessage());
                            
                            // Rollback chỉ khi transaction đang hoạt động và kết nối còn mở
                            try {
                                if (!conn.isClosed() && !conn.getAutoCommit()) {
                                    try {
                                    conn.rollback();
                                        Storage.getStorage().getLogger().info("Đã rollback transaction sau khi commit thất bại");
                                    } catch (SQLException rollbackEx2) {
                                        Storage.getStorage().getLogger().log(Level.SEVERE, "Không thể rollback: " + rollbackEx2.getMessage(), rollbackEx2);
                                    }
                                }
                            } catch (SQLException rollbackEx) {
                                // Ghi log và tiếp tục
                                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi rollback transaction: " + rollbackEx.getMessage(), rollbackEx);
                            }
                            
                            // Thêm lại vào hàng đợi để xử lý sau
                            for (StatsUpdate update : batchUpdates) {
                                statsPendingUpdates.add(update);
                            }
                        }
                    }
                    
                    // Đặt lại auto-commit nếu cần
                    if (conn != null && !conn.isClosed() && wasAutoCommit && !conn.getAutoCommit()) {
                        try {
                            conn.setAutoCommit(true);
                        } catch (SQLException e) {
                            Storage.getStorage().getLogger().warning("Không thể khôi phục trạng thái auto-commit: " + e.getMessage());
                        }
                    }
                    
                    // Ghi log với thông tin số lượng cập nhật
                    if (LOG_STATS_BATCH && shouldLog("stats_batch")) {
                        Storage.getStorage().getLogger().info("Xử lý đồng bộ: Đã cập nhật thống kê cho " + 
                                                             consolidatedUpdates.size() + " người chơi (từ " + 
                                                             batchUpdates.size() + " cập nhật)");
                    }
                    
                } catch (SQLException ex) {
                    // Kiểm tra xem có phải lỗi do database bị khóa không
                    boolean isSQLiteBusy = ex.getMessage() != null &&
                        (ex.getMessage().contains("SQLITE_BUSY") ||
                         ex.getMessage().contains("database is locked") ||
                         ex.getMessage().contains("database table is locked") ||
                         ex.getMessage().contains("Connection is closed") ||
                         ex.getMessage().contains("database has been closed") ||
                         ex.getMessage().contains("unfinalized prepared statement") ||
                         ex.getMessage().contains("pthread_mutex_lock") ||
                         ex.getMessage().contains("Disk I/O error") ||
                         ex.getMessage().contains("database disk image is malformed"));
                    
                    if (isSQLiteBusy) {
                        Storage.getStorage().getLogger().warning("SQLite error: " + ex.getMessage() + ", thêm lại vào hàng đợi để thử lại sau");
                        
                        // Thêm lại vào hàng đợi nếu lỗi SQLITE_BUSY
                        for (StatsUpdate update : batchUpdates) {
                            statsPendingUpdates.add(update);
                        }
                        
                        // Thêm delay trước khi thử lại
                        lastStatsProcessTime = System.currentTimeMillis() + STATS_RETRY_DELAY;
                    } else {
                        Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi SQL khi cập nhật batch stats: " + ex.getMessage(), ex);
                        
                        // Thêm lại vào hàng đợi nếu có lỗi SQL khác
                        for (StatsUpdate update : batchUpdates) {
                            statsPendingUpdates.add(update);
                        }
                    }
                } finally {
                    // Đóng statement
                    try {
                        if (ps != null && !ps.isClosed()) {
                            ps.close();
                        }
                    } catch (SQLException ex) {
                        Storage.getStorage().getLogger().warning("Không thể đóng PreparedStatement: " + ex.getMessage());
                    }
                    
                    try {
                        if (stmt != null && !stmt.isClosed()) {
                            stmt.close();
                        }
                    } catch (SQLException ex) {
                        Storage.getStorage().getLogger().warning("Không thể đóng Statement: " + ex.getMessage());
                    }
                    
                    // Đặt lại auto-commit nếu đã thay đổi
                    try {
                        if (conn != null && !conn.isClosed() && !wasAutoCommit && !conn.getAutoCommit()) {
                            conn.setAutoCommit(true);
                        }
                    } catch (SQLException ex) {
                        Storage.getStorage().getLogger().warning("Không thể khôi phục trạng thái auto-commit: " + ex.getMessage());
                    }
                    
                    // Đóng kết nối
                    try {
                        if (conn != null && !conn.isClosed()) {
                            conn.close();
                        }
                    } catch (SQLException ex) {
                        Storage.getStorage().getLogger().warning("Không thể đóng kết nối: " + ex.getMessage());
                    }
                    
                    // Đánh dấu đã xử lý xong
                    isProcessingStats = false;
                    lastStatsProcessTime = System.currentTimeMillis();
                }
            } // End synchronized block
            
        } catch (Exception e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi không xác định khi xử lý batch stats đồng bộ: " + e.getMessage(), e);
        } finally {
            // Trả kết nối về pool
            if (conn != null) {
                try {
                    Storage.db.returnConnection(conn);
                } catch (Exception e) {
                    Storage.getStorage().getLogger().warning("Lỗi khi trả kết nối về pool: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * Xử lý một batch đồng bộ và an toàn với kết nối
     * Phương thức này đảm bảo kiểm tra trạng thái kết nối trước khi thực hiện các thao tác commit/rollback
     * @param conn Kết nối database cần xử lý
     * @return true nếu thành công, false nếu có lỗi
     */
    public static boolean processBatchSynchronously(Connection conn) {
        try {
            // Kiểm tra nếu kết nối không tồn tại hoặc đã đóng
            if (conn == null || conn.isClosed()) {
                Storage.getStorage().getLogger().warning("Bỏ qua xử lý batch vì kết nối không tồn tại hoặc đã đóng");
                return false;
            }
            
            // Kiểm tra trạng thái auto-commit trước khi commit
            if (!conn.getAutoCommit()) {
                try {
                    conn.commit();
                    return true;
                } catch (SQLException e) {
                    // Xử lý trường hợp 'no transaction is active'
                    if (e.getMessage().contains("no transaction is active")) {
                        Storage.getStorage().getLogger().info("Bỏ qua lỗi 'no transaction is active', không cần commit");
                        // Đặt lại trạng thái auto-commit nếu cần
                        try {
                            conn.setAutoCommit(true);
                        } catch (SQLException autoCommitEx) {
                            // Bỏ qua lỗi khi đặt lại auto-commit
                            Storage.getStorage().getLogger().fine("Không thể đặt lại autoCommit: " + autoCommitEx.getMessage());
                        }
                        return true; // Vẫn coi là thành công vì không có transaction cần commit
                    } else {
                        // Lỗi khác, ghi log và trả về false
                        Storage.getStorage().getLogger().warning("Lỗi khi commit transaction: " + e.getMessage());
                        
                        // Thử rollback nếu có lỗi commit
                        try {
                            conn.rollback();
                            Storage.getStorage().getLogger().info("Đã rollback transaction sau khi commit thất bại");
                        } catch (SQLException rollbackEx) {
                            // Xử lý lỗi rollback tương tự
                            if (rollbackEx.getMessage().contains("no transaction is active")) {
                                // Bỏ qua lỗi rollback nếu không có transaction đang hoạt động
                                Storage.getStorage().getLogger().fine("Bỏ qua lỗi 'no transaction is active' khi rollback");
                            } else {
                                Storage.getStorage().getLogger().warning("Không thể rollback sau lỗi commit: " + rollbackEx.getMessage());
                            }
                        }
                        return false;
                    }
                }
            } else {
                // Không cần commit khi ở chế độ auto-commit
                return true;
            }
        } catch (SQLException e) {
            // Xử lý các lỗi khác nhau tùy theo loại lỗi
            if (e.getMessage().contains("no transaction is active")) {
                Storage.getStorage().getLogger().warning("Phát hiện 'no transaction is active', không cần commit: " + e.getMessage());
                return true; // Vẫn coi là thành công vì không có transaction cần commit
            } else {
                Storage.getStorage().getLogger().warning("Lỗi khi xử lý batch: " + e.getMessage());
                return false;
            }
        }
    }
} 