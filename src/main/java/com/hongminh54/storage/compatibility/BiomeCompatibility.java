package com.hongminh54.storage.compatibility;

import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.block.Biome;
import org.bukkit.block.Block;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;

import java.util.Arrays;
import java.util.List;

/**
 * Lớp hỗ trợ tương thích Biome API cho Minecraft 1.12.2 - 1.21.x
 * Xử lý Biome changes, biome detection, và biome manipulation
 */
public class BiomeCompatibility {
    
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_18_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(18);
    
    /**
     * Lấy biome tại vị tr<PERSON> một cách an toàn
     * 
     * @param location Vị trí
     * @return Biome hoặc null nếu có lỗi
     */
    public static Biome getBiomeSafely(Location location) {
        if (location == null || location.getWorld() == null) {
            return null;
        }
        
        try {
            if (IS_1_18_OR_HIGHER) {
                // Minecraft 1.18+: Sử dụng getBiome(location)
                return location.getWorld().getBiome(location);
            } else {
                // Minecraft cũ hơn: Sử dụng getBiome(x, z) hoặc getBiome(x, y, z)
                return getBiomeLegacy(location);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy biome tại " + location + ": " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * Lấy biome tại block một cách an toàn
     * 
     * @param block Block
     * @return Biome hoặc null nếu có lỗi
     */
    public static Biome getBiomeSafely(Block block) {
        if (block == null) {
            return null;
        }
        
        try {
            return block.getBiome();
        } catch (Exception e) {
            // Fallback với location
            return getBiomeSafely(block.getLocation());
        }
    }
    
    /**
     * Set biome tại vị trí một cách an toàn
     * 
     * @param location Vị trí
     * @param biome Biome mới
     * @return true nếu set thành công
     */
    public static boolean setBiomeSafely(Location location, Biome biome) {
        if (location == null || location.getWorld() == null || biome == null) {
            return false;
        }
        
        try {
            if (IS_1_18_OR_HIGHER) {
                // Minecraft 1.18+: Sử dụng setBiome(location, biome)
                location.getWorld().setBiome(location, biome);
            } else {
                // Minecraft cũ hơn: Sử dụng setBiome(x, z, biome)
                setBiomeLegacy(location, biome);
            }
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set biome: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Set biome tại block một cách an toàn
     * 
     * @param block Block
     * @param biome Biome mới
     * @return true nếu set thành công
     */
    public static boolean setBiomeSafely(Block block, Biome biome) {
        if (block == null || biome == null) {
            return false;
        }
        
        try {
            block.setBiome(biome);
            return true;
        } catch (Exception e) {
            // Fallback với location
            return setBiomeSafely(block.getLocation(), biome);
        }
    }
    
    /**
     * Lấy Biome tương thích đa phiên bản
     * 
     * @param modernName Tên biome phiên bản mới (1.13+)
     * @param legacyName Tên biome phiên bản cũ (1.12.2)
     * @return Biome tương thích
     */
    public static Biome getCompatibleBiome(String modernName, String legacyName) {
        try {
            if (IS_PRE_113 && legacyName != null) {
                return Biome.valueOf(legacyName);
            } else {
                return Biome.valueOf(modernName);
            }
        } catch (IllegalArgumentException e) {
            // Thử với tên khác nếu không tìm thấy
            try {
                return Biome.valueOf(IS_PRE_113 ? modernName : legacyName);
            } catch (IllegalArgumentException ex) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Không tìm thấy Biome: " + modernName + "/" + legacyName);
                }
                return null;
            }
        }
    }
    
    /**
     * Kiểm tra biome có phải là ocean không
     * 
     * @param biome Biome cần kiểm tra
     * @return true nếu là ocean biome
     */
    public static boolean isOceanBiome(Biome biome) {
        if (biome == null) {
            return false;
        }
        
        String biomeName = biome.name().toLowerCase();
        return biomeName.contains("ocean") || 
               biomeName.contains("deep") ||
               biomeName.equals("river") ||
               biomeName.equals("frozen_river");
    }
    
    /**
     * Kiểm tra biome có phải là desert không
     * 
     * @param biome Biome cần kiểm tra
     * @return true nếu là desert biome
     */
    public static boolean isDesertBiome(Biome biome) {
        if (biome == null) {
            return false;
        }
        
        String biomeName = biome.name().toLowerCase();
        return biomeName.contains("desert") || 
               biomeName.contains("badlands") ||
               biomeName.contains("mesa");
    }
    
    /**
     * Kiểm tra biome có phải là forest không
     * 
     * @param biome Biome cần kiểm tra
     * @return true nếu là forest biome
     */
    public static boolean isForestBiome(Biome biome) {
        if (biome == null) {
            return false;
        }
        
        String biomeName = biome.name().toLowerCase();
        return biomeName.contains("forest") || 
               biomeName.contains("jungle") ||
               biomeName.contains("taiga") ||
               biomeName.contains("birch") ||
               biomeName.contains("oak");
    }
    
    /**
     * Kiểm tra biome có phải là mountain không
     * 
     * @param biome Biome cần kiểm tra
     * @return true nếu là mountain biome
     */
    public static boolean isMountainBiome(Biome biome) {
        if (biome == null) {
            return false;
        }
        
        String biomeName = biome.name().toLowerCase();
        return biomeName.contains("mountain") || 
               biomeName.contains("hills") ||
               biomeName.contains("peak") ||
               biomeName.contains("extreme");
    }
    
    /**
     * Kiểm tra biome có phải là cold không
     * 
     * @param biome Biome cần kiểm tra
     * @return true nếu là cold biome
     */
    public static boolean isColdBiome(Biome biome) {
        if (biome == null) {
            return false;
        }
        
        String biomeName = biome.name().toLowerCase();
        return biomeName.contains("frozen") || 
               biomeName.contains("ice") ||
               biomeName.contains("snow") ||
               biomeName.contains("tundra") ||
               biomeName.contains("cold");
    }
    
    /**
     * Kiểm tra biome có phải là nether không
     * 
     * @param biome Biome cần kiểm tra
     * @return true nếu là nether biome
     */
    public static boolean isNetherBiome(Biome biome) {
        if (biome == null) {
            return false;
        }
        
        String biomeName = biome.name().toLowerCase();
        return biomeName.contains("nether") || 
               biomeName.contains("hell") ||
               biomeName.contains("basalt") ||
               biomeName.contains("crimson") ||
               biomeName.contains("warped") ||
               biomeName.contains("soul");
    }
    
    /**
     * Kiểm tra biome có phải là end không
     * 
     * @param biome Biome cần kiểm tra
     * @return true nếu là end biome
     */
    public static boolean isEndBiome(Biome biome) {
        if (biome == null) {
            return false;
        }
        
        String biomeName = biome.name().toLowerCase();
        return biomeName.contains("end") || 
               biomeName.contains("void");
    }
    
    /**
     * Lấy danh sách tất cả biomes có sẵn
     * 
     * @return List của Biome
     */
    public static List<Biome> getAllBiomes() {
        try {
            return Arrays.asList(Biome.values());
        } catch (Exception e) {
            return java.util.Collections.emptyList();
        }
    }
    
    /**
     * Kiểm tra biome có tồn tại không
     * 
     * @param biomeName Tên biome
     * @return true nếu biome tồn tại
     */
    public static boolean biomeExists(String biomeName) {
        if (biomeName == null || biomeName.isEmpty()) {
            return false;
        }
        
        try {
            Biome.valueOf(biomeName.toUpperCase());
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * Fallback method cho phiên bản cũ
     */
    @SuppressWarnings("deprecation")
    private static Biome getBiomeLegacy(Location location) {
        try {
            World world = location.getWorld();
            int x = location.getBlockX();
            int z = location.getBlockZ();
            
            // Thử với getBiome(x, z) trước
            try {
                java.lang.reflect.Method getBiomeMethod = world.getClass().getMethod("getBiome", int.class, int.class);
                return (Biome) getBiomeMethod.invoke(world, x, z);
            } catch (Exception e) {
                // Thử với getBiome(x, y, z)
                int y = location.getBlockY();
                java.lang.reflect.Method getBiomeMethod = world.getClass().getMethod("getBiome", int.class, int.class, int.class);
                return (Biome) getBiomeMethod.invoke(world, x, y, z);
            }
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Fallback method cho set biome phiên bản cũ
     */
    @SuppressWarnings("deprecation")
    private static void setBiomeLegacy(Location location, Biome biome) throws Exception {
        World world = location.getWorld();
        int x = location.getBlockX();
        int z = location.getBlockZ();
        
        try {
            // Thử với setBiome(x, z, biome) trước
            java.lang.reflect.Method setBiomeMethod = world.getClass().getMethod("setBiome", int.class, int.class, Biome.class);
            setBiomeMethod.invoke(world, x, z, biome);
        } catch (Exception e) {
            // Thử với setBiome(x, y, z, biome)
            int y = location.getBlockY();
            java.lang.reflect.Method setBiomeMethod = world.getClass().getMethod("setBiome", int.class, int.class, int.class, Biome.class);
            setBiomeMethod.invoke(world, x, y, z, biome);
        }
    }
    
    /**
     * Lấy temperature của biome
     * 
     * @param biome Biome
     * @return Temperature hoặc 0.5 nếu có lỗi
     */
    public static float getBiomeTemperature(Biome biome) {
        if (biome == null) {
            return 0.5f;
        }
        
        try {
            // Sử dụng reflection để lấy temperature
            java.lang.reflect.Method getTemperatureMethod = biome.getClass().getMethod("getTemperature");
            return (Float) getTemperatureMethod.invoke(biome);
        } catch (Exception e) {
            // Fallback với hardcoded values
            return getBiomeTemperatureLegacy(biome);
        }
    }
    
    /**
     * Fallback method cho temperature
     */
    private static float getBiomeTemperatureLegacy(Biome biome) {
        String biomeName = biome.name().toLowerCase();
        
        if (isColdBiome(biome)) {
            return 0.0f;
        } else if (isDesertBiome(biome)) {
            return 2.0f;
        } else if (isNetherBiome(biome)) {
            return 2.0f;
        } else if (isOceanBiome(biome)) {
            return 0.5f;
        } else {
            return 0.8f; // Default temperate
        }
    }
}
