package com.hongminh54.storage.Utils;

import java.util.logging.Level;
import com.hongminh54.storage.Storage;

/**
 * Utility class để quản lý logging tập trung
 * Chỉ hiển thị log khi debug_logging được bật trong config.yml
 * Thay thế tất cả các logging trực tiếp để tránh spam console
 */
public class DebugLogger {
    
    /**
     * Kiểm tra xem debug logging có được bật không
     * @return true nếu debug_logging được bật trong config
     */
    public static boolean isDebugEnabled() {
        try {
            return Storage.getStorage().getConfig().getBoolean("debug_logging", false);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Ghi log với level INFO (chỉ khi debug_logging được bật)
     * @param message Thông điệp cần ghi log
     */
    public static void info(String message) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().info("[DEBUG] " + message);
        }
    }
    
    /**
     * Ghi log với level WARNING (chỉ khi debug_logging được bật)
     * @param message Thông điệp cần ghi log
     */
    public static void warning(String message) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().warning("[DEBUG] " + message);
        }
    }
    
    /**
     * Ghi log với level SEVERE (chỉ khi debug_logging được bật)
     * @param message Thông điệp cần ghi log
     */
    public static void severe(String message) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().severe("[DEBUG] " + message);
        }
    }
    
    /**
     * Ghi log với level INFO và exception (chỉ khi debug_logging được bật)
     * @param message Thông điệp cần ghi log
     * @param throwable Exception cần ghi log
     */
    public static void info(String message, Throwable throwable) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().log(Level.INFO, "[DEBUG] " + message, throwable);
        }
    }
    
    /**
     * Ghi log với level WARNING và exception (chỉ khi debug_logging được bật)
     * @param message Thông điệp cần ghi log
     * @param throwable Exception cần ghi log
     */
    public static void warning(String message, Throwable throwable) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().log(Level.WARNING, "[DEBUG] " + message, throwable);
        }
    }
    
    /**
     * Ghi log với level SEVERE và exception (chỉ khi debug_logging được bật)
     * @param message Thông điệp cần ghi log
     * @param throwable Exception cần ghi log
     */
    public static void severe(String message, Throwable throwable) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "[DEBUG] " + message, throwable);
        }
    }
    
    /**
     * Ghi log với custom level (chỉ khi debug_logging được bật)
     * @param level Level của log
     * @param message Thông điệp cần ghi log
     */
    public static void log(Level level, String message) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().log(level, "[DEBUG] " + message);
        }
    }
    
    /**
     * Ghi log với custom level và exception (chỉ khi debug_logging được bật)
     * @param level Level của log
     * @param message Thông điệp cần ghi log
     * @param throwable Exception cần ghi log
     */
    public static void log(Level level, String message, Throwable throwable) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().log(level, "[DEBUG] " + message, throwable);
        }
    }
    
    /**
     * Ghi log quan trọng luôn hiển thị (không phụ thuộc vào debug_logging)
     * Dành cho các thông báo khởi tạo plugin, lỗi nghiêm trọng
     * @param message Thông điệp cần ghi log
     */
    public static void alwaysInfo(String message) {
        Storage.getStorage().getLogger().info(message);
    }
    
    /**
     * Ghi log cảnh báo quan trọng luôn hiển thị (không phụ thuộc vào debug_logging)
     * @param message Thông điệp cần ghi log
     */
    public static void alwaysWarning(String message) {
        Storage.getStorage().getLogger().warning(message);
    }
    
    /**
     * Ghi log lỗi nghiêm trọng luôn hiển thị (không phụ thuộc vào debug_logging)
     * @param message Thông điệp cần ghi log
     */
    public static void alwaysSevere(String message) {
        Storage.getStorage().getLogger().severe(message);
    }
    
    /**
     * Ghi log lỗi nghiêm trọng với exception luôn hiển thị
     * @param message Thông điệp cần ghi log
     * @param throwable Exception cần ghi log
     */
    public static void alwaysSevere(String message, Throwable throwable) {
        Storage.getStorage().getLogger().log(Level.SEVERE, message, throwable);
    }
}
