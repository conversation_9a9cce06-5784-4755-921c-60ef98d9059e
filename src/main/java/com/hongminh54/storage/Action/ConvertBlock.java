package com.hongminh54.storage.Action;

import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;

public class ConvertBlock {
    private final Player p;
    private final String material;
    private final String materialData;
    private final Integer amount;
    private final boolean isReverseConversion; // true nếu là chuyển đổi từ block sang phôi
    
    // Số lượng phôi cần để tạo 1 block (mặc định là 9)
    private static final int INGOTS_PER_BLOCK = 9;

    public ConvertBlock(Player p, @NotNull String material, Integer amount) {
        this(p, material, amount, false);
    }
    
    public ConvertBlock(Player p, @NotNull String material, Integer amount, boolean isReverseConversion) {
        this.p = p;
        String material_data = material.replace(":", ";");
        if (material_data.contains(";")) materialData = material_data;
        else materialData = material_data + ";0";
        this.material = material_data.split(";")[0];
        this.amount = amount;
        this.isReverseConversion = isReverseConversion;
    }

    public void doAction() {
        // Kiểm tra tính nhất quán dữ liệu trước khi thao tác
        if (!MineManager.ensureDataConsistency(p)) {
            // Nếu có vấn đề, tải lại dữ liệu
            MineManager.loadPlayerData(p);
        }

        try {
            if (isReverseConversion) {
                doReverseConversion();
            } else {
                doNormalConversion();
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi " + (isReverseConversion ? "đổi block thành phôi" : "đổi phôi thành block") + ": " + e.getMessage());

            String errorMessage = File.getMessage().getString("user.error");
            if (errorMessage == null) {
                errorMessage = "&cĐã xảy ra lỗi khi xử lý yêu cầu của bạn.";
            }
            p.sendMessage(Chat.colorize(errorMessage));
        }
    }
    
    /**
     * Thực hiện chuyển đổi từ phôi sang block
     */
    private void doNormalConversion() {
            // Kiểm tra xem vật liệu có thể đổi thành block không
            if (!canConvertToBlock(material)) {
                String message = File.getMessage().getString("user.action.convert.not_convertible");
                if (message == null) {
                    message = "&cVật phẩm này không thể đổi thành block";
                }
                p.sendMessage(Chat.colorize(message));
                return;
            }

            // Lấy tên block tương ứng với phôi
            String blockMaterial = getBlockMaterial(materialData);
            if (blockMaterial == null) {
                String message = File.getMessage().getString("user.action.convert.not_convertible");
                if (message == null) {
                    message = "&cVật phẩm này không thể đổi thành block";
                }
                p.sendMessage(Chat.colorize(message));
                return;
            }
            
            // Lấy số lượng phôi hiện có
            int currentAmount = MineManager.getPlayerBlock(p, getMaterialData());
            
            // Kiểm tra số lượng trong kho
            if (currentAmount <= 0) {
                String message = File.getMessage().getString("user.action.convert.not_enough");
                if (message == null) {
                    message = "&cBạn không có đủ phôi &f#material#&c, bạn chỉ có &f#amount#";
                }
                
                String materialName = File.getConfig().getString("items." + getMaterialData());
                if (materialName == null) {
                    materialName = getMaterialData();
                }
                
                p.sendMessage(Chat.colorize(message
                        .replace("#material#", materialName)
                        .replace("#amount#", String.valueOf(currentAmount))
                        .replace("#current#", String.valueOf(currentAmount))));
                return;
            }

            // Kiểm tra số lượng vật phẩm có đủ để đổi không
            if (currentAmount < INGOTS_PER_BLOCK) {
                String materialName = File.getConfig().getString("items." + materialData);
                if (materialName == null) {
                    materialName = materialData;
                }
                
                String message = File.getMessage().getString("user.action.convert.not_enough");
                if (message == null) {
                    message = "&cBạn không đủ phôi &f#material# &cđể đổi thành block. Cần ít nhất &f#amount# &cphôi (hiện tại: &f#current#&c)";
                }
                
                p.sendMessage(Chat.colorize(message
                        .replace("#material#", materialName)
                        .replace("#amount#", String.valueOf(INGOTS_PER_BLOCK))
                        .replace("#current#", String.valueOf(currentAmount))));
                
                // Phát âm thanh thất bại
                try {
                    String failSound = File.getConfig().getString("effects.convert_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                    if (failSound == null || failSound.isEmpty()) {
                        failSound = File.getGUIConfig("convert_block").getString("convert_fail_sound", "ENTITY_VILLAGER_NO:1.0:1.0");
                    }
                    com.hongminh54.storage.Utils.SoundManager.playSoundFromConfig(p, failSound);
                } catch (Exception e) {
                    // Bỏ qua lỗi âm thanh
                }
                
                return;
            }
            
            // Xử lý đổi theo số lượng cụ thể hoặc tất cả
            int convertAmount;
            if (getAmount() > 0) {
                convertAmount = getAmount();
                if (currentAmount < convertAmount) {
                    String message = File.getMessage().getString("user.action.convert.not_enough");
                    if (message == null) {
                        message = "&cBạn không có đủ phôi &f#material#&c, bạn chỉ có &f#amount#";
                    }
                    
                    String materialName = File.getConfig().getString("items." + getMaterialData());
                    if (materialName == null) {
                        materialName = getMaterialData();
                    }
                    
                    p.sendMessage(Chat.colorize(message
                            .replace("#material#", materialName)
                            .replace("#amount#", String.valueOf(currentAmount))
                            .replace("#current#", String.valueOf(currentAmount))));

                    return;
                }
            } else {
                // Đổi tất cả có thể
                convertAmount = currentAmount;
            }
            
            // Tính số block có thể tạo và số phôi thực tế sẽ sử dụng
            int blocksToCreate = convertAmount / INGOTS_PER_BLOCK;
            int ingotsToUse = blocksToCreate * INGOTS_PER_BLOCK;
            
            if (blocksToCreate <= 0) {
                String message = File.getMessage().getString("user.action.convert.not_enough");
                if (message == null) {
                    message = "&cBạn cần ít nhất &f" + INGOTS_PER_BLOCK + " &cphôi để đổi thành 1 block";
                }
                
                String materialName = File.getConfig().getString("items." + getMaterialData());
                if (materialName == null) {
                    materialName = getMaterialData();
                }
                
                p.sendMessage(Chat.colorize(message
                        .replace("#material#", materialName)
                        .replace("#amount#", String.valueOf(INGOTS_PER_BLOCK))
                        .replace("#current#", String.valueOf(currentAmount))));

                return;
            }
            
            // Kiểm tra không gian trống trong kho cho block
            int maxStorage = MineManager.getMaxBlock(p);
            int blockCurrentAmount = MineManager.getPlayerBlock(p, blockMaterial);
            if (maxStorage - blockCurrentAmount < blocksToCreate) {
                String message = File.getMessage().getString("user.action.convert.storage_full");
                if (message == null) {
                    message = "&cKho đã gần đầy, không thể đổi thêm block!";
                }
                p.sendMessage(Chat.colorize(message));

                return;
            }
            
            // Thực hiện đổi
            boolean removeSuccess = MineManager.removeBlockAmount(p, getMaterialData(), ingotsToUse);
            if (!removeSuccess) {
                String errorMessage = File.getMessage().getString("user.error");
                if (errorMessage == null) {
                    errorMessage = "&cĐã xảy ra lỗi khi xử lý yêu cầu của bạn. Vui lòng thử lại sau.";
                }
                p.sendMessage(Chat.colorize(errorMessage));

                return;
            }
            
            boolean addSuccess = MineManager.addBlockAmount(p, blockMaterial, blocksToCreate);
            if (!addSuccess) {
                // Nếu thêm block thất bại, hoàn trả lại phôi
                MineManager.addBlockAmount(p, getMaterialData(), ingotsToUse);
                
                String errorMessage = File.getMessage().getString("user.error");
                if (errorMessage == null) {
                    errorMessage = "&cĐã xảy ra lỗi khi xử lý yêu cầu của bạn. Vui lòng thử lại sau.";
                }
                p.sendMessage(Chat.colorize(errorMessage));

                return;
            }
            
            // Lưu dữ liệu ngay lập tức sau khi đổi để tránh mất dữ liệu
            MineManager.savePlayerData(p);
            
            // Gửi thông báo thành công
            String message = File.getMessage().getString("user.action.convert.convert_success");
            if (message == null) {
                message = "&aĐã đổi &f#amount# &aphôi &f#material# &athành &f#block_amount# &ablock &8| &f#item_amount#/#max_storage# &aVật phẩm";
            }
            
            String materialName = File.getConfig().getString("items." + getMaterialData());
            if (materialName == null) {
                materialName = getMaterialData();
            }
            
            p.sendMessage(Chat.colorize(message
                    .replace("#amount#", String.valueOf(ingotsToUse))
                    .replace("#material#", materialName)
                    .replace("#block_amount#", String.valueOf(blocksToCreate))
                    .replace("#item_amount#", String.valueOf(MineManager.getPlayerBlock(p, getMaterialData())))
                    .replace("#max_storage#", String.valueOf(maxStorage))));
            
            // Phát âm thanh thành công
            try {
                String successSound = File.getConfig().getString("effects.convert_success.sound", "BLOCK_ANVIL_USE:1.0:1.2");
                if (successSound == null || successSound.isEmpty()) {
                    successSound = File.getGUIConfig("convert_block").getString("convert_success_sound", "BLOCK_ANVIL_USE:1.0:1.2");
                }
                com.hongminh54.storage.Utils.SoundManager.playSoundFromConfig(p, successSound);
            } catch (Exception e) {
                // Bỏ qua lỗi âm thanh
            }
            

    }
    
    /**
     * Thực hiện chuyển đổi từ block sang phôi
     */
    private void doReverseConversion() {
        // Kiểm tra xem vật liệu có thể đổi thành phôi không
        if (!canConvertToIngot(material)) {
            String message = File.getMessage().getString("user.action.convert.not_convertible_reverse");
            if (message == null) {
                message = "&cVật phẩm này không thể đổi thành phôi";
            }
            p.sendMessage(Chat.colorize(message));

            return;
        }
        
        // Lấy tên phôi tương ứng với block
        String ingotMaterial = getIngotMaterial(materialData);
        if (ingotMaterial == null) {
            String message = File.getMessage().getString("user.action.convert.not_convertible_reverse");
            if (message == null) {
                message = "&cVật phẩm này không thể đổi thành phôi";
            }
            p.sendMessage(Chat.colorize(message));

            return;
        }
        
        // Lấy số lượng block hiện có
        int currentAmount = MineManager.getPlayerBlock(p, getMaterialData());
        
        // Kiểm tra số lượng trong kho
        if (currentAmount <= 0) {
            String message = File.getMessage().getString("user.action.convert.not_enough_block");
            if (message == null) {
                message = "&cBạn không có đủ block &f#material#&c, bạn chỉ có &f#amount#";
            }
            
            String materialName = File.getConfig().getString("items." + getMaterialData());
            if (materialName == null) {
                materialName = getMaterialData();
            }
            
            p.sendMessage(Chat.colorize(message
                    .replace("#material#", materialName)
                    .replace("#amount#", String.valueOf(currentAmount))
                    .replace("#current#", String.valueOf(currentAmount))));

            return;
        }
        
        // Xử lý đổi theo số lượng cụ thể hoặc tất cả
        int convertAmount;
        if (getAmount() > 0) {
            convertAmount = getAmount();
            if (currentAmount < convertAmount) {
                String message = File.getMessage().getString("user.action.convert.not_enough_block");
                if (message == null) {
                    message = "&cBạn không có đủ block &f#material#&c, bạn chỉ có &f#amount#";
                }
                
                String materialName = File.getConfig().getString("items." + getMaterialData());
                if (materialName == null) {
                    materialName = getMaterialData();
                }
                
                p.sendMessage(Chat.colorize(message
                        .replace("#material#", materialName)
                        .replace("#amount#", String.valueOf(currentAmount))
                        .replace("#current#", String.valueOf(currentAmount))));

                return;
            }
        } else {
            // Đổi tất cả có thể
            convertAmount = currentAmount;
        }
        
        // Tính số phôi sẽ nhận được
        int ingotsToCreate = convertAmount * INGOTS_PER_BLOCK;
        
        // Kiểm tra không gian trống trong kho cho phôi
        int maxStorage = MineManager.getMaxBlock(p);
        int ingotCurrentAmount = MineManager.getPlayerBlock(p, ingotMaterial);
        if (maxStorage - ingotCurrentAmount < ingotsToCreate) {
            String message = File.getMessage().getString("user.action.convert.storage_full");
            if (message == null) {
                message = "&cKho đã gần đầy, không thể đổi thêm phôi!";
            }
            p.sendMessage(Chat.colorize(message));

            return;
        }
        
        // Thực hiện đổi
        boolean removeSuccess = MineManager.removeBlockAmount(p, getMaterialData(), convertAmount);
        if (!removeSuccess) {
            String errorMessage = File.getMessage().getString("user.error");
            if (errorMessage == null) {
                errorMessage = "&cĐã xảy ra lỗi khi xử lý yêu cầu của bạn. Vui lòng thử lại sau.";
            }
            p.sendMessage(Chat.colorize(errorMessage));

            return;
        }
        
        boolean addSuccess = MineManager.addBlockAmount(p, ingotMaterial, ingotsToCreate);
        if (!addSuccess) {
            // Nếu thêm phôi thất bại, hoàn trả lại block
            MineManager.addBlockAmount(p, getMaterialData(), convertAmount);
            
            String errorMessage = File.getMessage().getString("user.error");
            if (errorMessage == null) {
                errorMessage = "&cĐã xảy ra lỗi khi xử lý yêu cầu của bạn. Vui lòng thử lại sau.";
            }
            p.sendMessage(Chat.colorize(errorMessage));

            return;
        }
        
        // Lưu dữ liệu ngay lập tức sau khi đổi để tránh mất dữ liệu
        MineManager.savePlayerData(p);
        
        // Gửi thông báo thành công
        String message = File.getMessage().getString("user.action.convert.convert_success_reverse");
        if (message == null) {
            message = "&aĐã đổi &f#amount# &ablock &f#material# &athành &f#ingot_amount# &aphôi &8| &f#item_amount#/#max_storage# &aVật phẩm";
        }
        
        String materialName = File.getConfig().getString("items." + getMaterialData());
        if (materialName == null) {
            materialName = getMaterialData();
        }
        
        p.sendMessage(Chat.colorize(message
                .replace("#amount#", String.valueOf(convertAmount))
                .replace("#material#", materialName)
                .replace("#ingot_amount#", String.valueOf(ingotsToCreate))
                .replace("#item_amount#", String.valueOf(MineManager.getPlayerBlock(p, ingotMaterial)))
                .replace("#max_storage#", String.valueOf(maxStorage))));
        
        // Phát âm thanh thành công
        try {
            String successSound = File.getConfig().getString("effects.convert_success.sound", "BLOCK_ANVIL_USE:1.0:1.2");
            if (successSound == null || successSound.isEmpty()) {
                successSound = File.getGUIConfig("convert_block").getString("convert_success_sound", "BLOCK_ANVIL_USE:1.0:1.2");
            }
            com.hongminh54.storage.Utils.SoundManager.playSoundFromConfig(p, successSound);
        } catch (Exception e) {
            // Bỏ qua lỗi âm thanh
        }
        

    }
    
    /**
     * Kiểm tra xem vật liệu có thể đổi thành block không
     * @param material Tên vật liệu
     * @return true nếu có thể đổi thành block
     */
    private boolean canConvertToBlock(String material) {
        // Danh sách các vật liệu có thể đổi thành block
        // Có thể mở rộng danh sách này tùy theo nhu cầu
        String[] convertibleMaterials = {
            "IRON_INGOT", "GOLD_INGOT", "DIAMOND", "EMERALD", "COAL", "REDSTONE", "LAPIS_LAZULI",
            "COPPER_INGOT", "NETHERITE_INGOT", "QUARTZ", "AMETHYST_SHARD"
        };
        
        for (String convertible : convertibleMaterials) {
            if (material.toUpperCase().contains(convertible)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Kiểm tra xem vật liệu có thể đổi thành phôi không
     * @param material Tên vật liệu
     * @return true nếu có thể đổi thành phôi
     */
    private boolean canConvertToIngot(String material) {
        // Danh sách các vật liệu có thể đổi thành phôi
        String[] convertibleMaterials = {
            "IRON_BLOCK", "GOLD_BLOCK", "DIAMOND_BLOCK", "EMERALD_BLOCK", "COAL_BLOCK", 
            "REDSTONE_BLOCK", "LAPIS_BLOCK", "COPPER_BLOCK", "NETHERITE_BLOCK", 
            "QUARTZ_BLOCK", "AMETHYST_BLOCK"
        };
        
        for (String convertible : convertibleMaterials) {
            if (material.toUpperCase().contains(convertible)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Lấy tên block tương ứng với phôi
     * @param materialData Tên vật liệu
     * @return Tên block tương ứng hoặc null nếu không tìm thấy
     */
    private String getBlockMaterial(String materialData) {
        String material = materialData.split(";")[0].toUpperCase();
        
        // Map các loại phôi với block tương ứng
        if (material.contains("IRON_INGOT")) return "IRON_BLOCK;0";
        if (material.contains("GOLD_INGOT")) return "GOLD_BLOCK;0";
        if (material.contains("DIAMOND")) return "DIAMOND_BLOCK;0";
        if (material.contains("EMERALD")) return "EMERALD_BLOCK;0";
        if (material.contains("COAL")) return "COAL_BLOCK;0";
        if (material.contains("REDSTONE")) return "REDSTONE_BLOCK;0";
        if (material.contains("LAPIS_LAZULI")) return "LAPIS_BLOCK;0";
        
        // Thêm hỗ trợ cho các loại phôi mới (1.16+)
        if (material.contains("COPPER_INGOT")) return "COPPER_BLOCK;0";
        if (material.contains("NETHERITE_INGOT")) return "NETHERITE_BLOCK;0";
        
        // Các vật liệu khác có thể đổi thành block
        if (material.contains("QUARTZ")) return "QUARTZ_BLOCK;0";
        if (material.contains("AMETHYST_SHARD")) return "AMETHYST_BLOCK;0";
        
        return null;
    }
    
    /**
     * Lấy tên phôi tương ứng với block
     * @param materialData Tên vật liệu
     * @return Tên phôi tương ứng hoặc null nếu không tìm thấy
     */
    private String getIngotMaterial(String materialData) {
        String material = materialData.split(";")[0].toUpperCase();
        
        // Map các loại block với phôi tương ứng
        if (material.contains("IRON_BLOCK")) return "IRON_INGOT;0";
        if (material.contains("GOLD_BLOCK")) return "GOLD_INGOT;0";
        if (material.contains("DIAMOND_BLOCK")) return "DIAMOND;0";
        if (material.contains("EMERALD_BLOCK")) return "EMERALD;0";
        if (material.contains("COAL_BLOCK")) return "COAL;0";
        if (material.contains("REDSTONE_BLOCK")) return "REDSTONE;0";
        if (material.contains("LAPIS_BLOCK")) return "LAPIS_LAZULI;0";
        
        // Thêm hỗ trợ cho các loại block mới (1.16+)
        if (material.contains("COPPER_BLOCK")) return "COPPER_INGOT;0";
        if (material.contains("NETHERITE_BLOCK")) return "NETHERITE_INGOT;0";
        
        // Các khối khác có thể chuyển thành vật liệu
        if (material.contains("QUARTZ_BLOCK")) return "QUARTZ;0";
        if (material.contains("AMETHYST_BLOCK")) return "AMETHYST_SHARD;0";
        
        return null;
    }

    public String getMaterialData() {
        return materialData;
    }

    public Player getPlayer() {
        return p;
    }

    public String getMaterial() {
        return material;
    }

    public Integer getAmount() {
        return amount;
    }
    
    public boolean isReverseConversion() {
        return isReverseConversion;
    }
} 