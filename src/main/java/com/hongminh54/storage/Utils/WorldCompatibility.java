package com.hongminh54.storage.compatibility;

import org.bukkit.Bukkit;
import org.bukkit.Chunk;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;

import java.util.concurrent.CompletableFuture;

/**
 * Lớp hỗ trợ tương thích World & Chunk operations cho Minecraft 1.12.2 - 1.21.x
 * Xử lý chunk loading, world operations, và các tính năng liên quan
 */
public class WorldCompatibility {
    
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_17_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(17);
    
    /**
     * Load chunk một cách an toàn
     * 
     * @param world World chứa chunk
     * @param chunkX Tọa độ X của chunk
     * @param chunkZ Tọa độ Z của chunk
     * @return true nếu load thành công
     */
    public static boolean loadChunk(World world, int chunkX, int chunkZ) {
        if (world == null) {
            return false;
        }
        
        try {
            // Thử load chunk và kiểm tra kết quả
            world.loadChunk(chunkX, chunkZ);
            return world.isChunkLoaded(chunkX, chunkZ);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể load chunk: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Load chunk async một cách an toàn
     * 
     * @param world World chứa chunk
     * @param chunkX Tọa độ X của chunk
     * @param chunkZ Tọa độ Z của chunk
     * @return CompletableFuture<Boolean>
     */
    public static CompletableFuture<Boolean> loadChunkAsync(World world, int chunkX, int chunkZ) {
        if (world == null) {
            return CompletableFuture.completedFuture(false);
        }
        
        // Chạy sync load trong async task cho tất cả phiên bản
        return ServerCompatibility.supplyAsync(() -> loadChunk(world, chunkX, chunkZ));
    }
    
    /**
     * Kiểm tra chunk có được load không
     * 
     * @param world World chứa chunk
     * @param chunkX Tọa độ X của chunk
     * @param chunkZ Tọa độ Z của chunk
     * @return true nếu chunk đã được load
     */
    public static boolean isChunkLoaded(World world, int chunkX, int chunkZ) {
        if (world == null) {
            return false;
        }
        
        try {
            return world.isChunkLoaded(chunkX, chunkZ);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Unload chunk một cách an toàn
     * 
     * @param world World chứa chunk
     * @param chunkX Tọa độ X của chunk
     * @param chunkZ Tọa độ Z của chunk
     * @return true nếu unload thành công
     */
    public static boolean unloadChunk(World world, int chunkX, int chunkZ) {
        if (world == null) {
            return false;
        }
        
        try {
            if (IS_PRE_113) {
                // Phiên bản 1.12.2: sử dụng unloadChunk với save parameter
                return world.unloadChunk(chunkX, chunkZ, true);
            } else {
                // Phiên bản 1.13+: sử dụng unloadChunk
                return world.unloadChunk(chunkX, chunkZ);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể unload chunk: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Lấy block một cách an toàn
     * 
     * @param world World
     * @param x Tọa độ X
     * @param y Tọa độ Y
     * @param z Tọa độ Z
     * @return Block hoặc null nếu có lỗi
     */
    public static Block getBlockSafely(World world, int x, int y, int z) {
        if (world == null) {
            return null;
        }
        
        try {
            // Kiểm tra tọa độ Y hợp lệ
            if (IS_1_17_OR_HIGHER) {
                // Minecraft 1.17+: Y từ -64 đến 319
                if (y < world.getMinHeight() || y > world.getMaxHeight()) {
                    return null;
                }
            } else {
                // Minecraft cũ hơn: Y từ 0 đến 255
                if (y < 0 || y > 255) {
                    return null;
                }
            }
            
            return world.getBlockAt(x, y, z);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy block: " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * Kiểm tra tọa độ Y có hợp lệ không
     * 
     * @param world World
     * @param y Tọa độ Y
     * @return true nếu Y hợp lệ
     */
    public static boolean isValidY(World world, int y) {
        if (world == null) {
            return false;
        }
        
        try {
            if (IS_1_17_OR_HIGHER) {
                return y >= world.getMinHeight() && y <= world.getMaxHeight();
            } else {
                return y >= 0 && y <= 255;
            }
        } catch (Exception e) {
            // Fallback cho phiên bản cũ
            return y >= 0 && y <= 255;
        }
    }
    
    /**
     * Lấy min height của world
     * 
     * @param world World
     * @return Min height
     */
    public static int getMinHeight(World world) {
        if (world == null) {
            return 0;
        }
        
        try {
            if (IS_1_17_OR_HIGHER) {
                return world.getMinHeight();
            } else {
                return 0;
            }
        } catch (Exception e) {
            return 0;
        }
    }
    
    /**
     * Lấy max height của world
     * 
     * @param world World
     * @return Max height
     */
    public static int getMaxHeight(World world) {
        if (world == null) {
            return 255;
        }
        
        try {
            if (IS_1_17_OR_HIGHER) {
                return world.getMaxHeight();
            } else {
                return 255;
            }
        } catch (Exception e) {
            return 255;
        }
    }
    
    /**
     * Teleport player một cách an toàn
     * 
     * @param player Player cần teleport
     * @param location Location đích
     * @return true nếu teleport thành công
     */
    public static boolean teleportSafely(Player player, Location location) {
        if (player == null || location == null) {
            return false;
        }
        
        try {
            // Đảm bảo chunk được load trước khi teleport
            World world = location.getWorld();
            if (world != null) {
                int chunkX = location.getBlockX() >> 4;
                int chunkZ = location.getBlockZ() >> 4;
                
                if (!isChunkLoaded(world, chunkX, chunkZ)) {
                    loadChunk(world, chunkX, chunkZ);
                }
            }
            
            return player.teleport(location);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể teleport player: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Kiểm tra location có an toàn để teleport không
     * 
     * @param location Location cần kiểm tra
     * @return true nếu an toàn
     */
    public static boolean isSafeLocation(Location location) {
        if (location == null || location.getWorld() == null) {
            return false;
        }
        
        try {
            World world = location.getWorld();
            int x = location.getBlockX();
            int y = location.getBlockY();
            int z = location.getBlockZ();
            
            // Kiểm tra Y hợp lệ
            if (!isValidY(world, y)) {
                return false;
            }
            
            // Kiểm tra block tại vị trí và phía trên
            Block block = getBlockSafely(world, x, y, z);
            Block blockAbove = getBlockSafely(world, x, y + 1, z);
            
            if (block == null || blockAbove == null) {
                return false;
            }
            
            // Kiểm tra không phải block rắn
            return !block.getType().isSolid() && !blockAbove.getType().isSolid();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Lấy spawn location của world một cách an toàn
     * 
     * @param world World
     * @return Spawn location hoặc null nếu có lỗi
     */
    public static Location getSpawnLocation(World world) {
        if (world == null) {
            return null;
        }
        
        try {
            return world.getSpawnLocation();
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy spawn location: " + e.getMessage());
            }
            return null;
        }
    }
}
