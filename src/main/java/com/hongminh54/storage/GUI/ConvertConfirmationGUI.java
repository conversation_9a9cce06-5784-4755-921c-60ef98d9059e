package com.hongminh54.storage.GUI;

import java.util.Arrays;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.Action.ConvertBlock;
import com.hongminh54.storage.GUI.manager.IGUI;
import com.hongminh54.storage.GUI.manager.InteractiveItem;
import com.hongminh54.storage.Manager.ItemManager;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.AdvancedCompatibility;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.GUIText;
import com.hongminh54.storage.Utils.MaterialCompatibility;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Utils.SoundManager;

/**
 * GUI xác nhận chuyển đổi block/phôi
 */
public class ConvertConfirmationGUI implements IGUI {

    private final Player player;
    private final String material;
    private final int amount;
    private final boolean isReverseConversion;
    private final FileConfiguration config;
    private final NMSAssistant nmsAssistant;

    public ConvertConfirmationGUI(Player player, String material, int amount, boolean isReverseConversion) {
        this.player = player;
        this.material = material;
        this.amount = amount;
        this.isReverseConversion = isReverseConversion;
        this.config = File.getGUIConfig("convert_confirmation");
        this.nmsAssistant = new NMSAssistant();
    }

    @NotNull
    @Override
    public Inventory getInventory() {
        // Tạo inventory với kích thước từ config
        int size = config.getInt("size", 3) * 9;
        String title = config.getString("title", "&8Xác nhận chuyển đổi");
        title = title.replace("#player#", player.getName());
        
        Inventory inventory = Bukkit.createInventory(this, size, GUIText.format(title));
        
        // Phát âm thanh khi mở
        try {
            String openSound = config.getString("open_sound", "BLOCK_CHEST_OPEN:1.0:1.0");
            SoundManager.playSoundFromConfig(player, openSound);
        } catch (Exception e) {
            // Bỏ qua lỗi âm thanh
        }
        
        // Thêm các item trang trí
        addDecorativeItems(inventory);
        
        // Thêm thông tin chuyển đổi
        addConversionInfo(inventory);
        
        // Thêm nút xác nhận và hủy
        addControlButtons(inventory);
        
        return inventory;
    }
    
    /**
     * Thêm các item trang trí
     */
    private void addDecorativeItems(Inventory inventory) {
        // Lấy cấu hình item trang trí
        String decorateSlots = config.getString("items.decorates.slot", "0,1,2,6,7,8,9,17,18,19,20,24,25,26");
        String[] slots = decorateSlots.split(",");
        
        Material decorateMaterial = getCompatibleDecorativeMaterial();
        ItemStack decorateItem = ItemManager.createItem(
            decorateMaterial,
            config.getString("items.decorates.name", "&7 "),
            Arrays.asList(config.getString("items.decorates.lore", "&7 "))
        );
        
        for (String slotStr : slots) {
            try {
                int slot = Integer.parseInt(slotStr.trim());
                if (slot >= 0 && slot < inventory.getSize()) {
                    // Tạo item trang trí không thể tương tác (không có NBT tag)
                    ItemStack safeDecorateItem = decorateItem.clone();
                    inventory.setItem(slot, safeDecorateItem);
                }
            } catch (NumberFormatException e) {
                // Bỏ qua slot không hợp lệ
            }
        }
    }
    
    /**
     * Thêm thông tin chuyển đổi
     */
    private void addConversionInfo(Inventory inventory) {
        String materialName = getDisplayMaterialName(material);

        // Lấy số lượng hiện có
        int currentAmount = MineManager.getPlayerBlock(player, material);

        // Tính toán số lượng sẽ chuyển đổi
        int convertAmount = (amount > 0) ? amount : currentAmount;

        // Lấy material hiển thị từ config hoặc từ material đang chuyển đổi
        Material displayMaterial = getConversionInfoMaterial();

        String infoTitle;
        String conversionDescription;
        int resultAmount;

        if (isReverseConversion) {
            // Block -> Phôi
            infoTitle = File.getMessage().getString("user.action.convert.confirmation_title", "&eXác nhận chuyển đổi");
            conversionDescription = File.getMessage().getString("user.action.convert.confirmation_block_to_ingot",
                "&aChuyển &f#amount# &ablock &f#material# &athành &f#result# &aphôi");
            resultAmount = convertAmount * 9; // 1 block = 9 phôi
        } else {
            // Phôi -> Block
            infoTitle = File.getMessage().getString("user.action.convert.confirmation_title", "&eXác nhận chuyển đổi");
            conversionDescription = File.getMessage().getString("user.action.convert.confirmation_ingot_to_block",
                "&aChuyển &f#amount# &aphôi &f#material# &athành &f#result# &ablock");
            resultAmount = convertAmount / 9; // 9 phôi = 1 block
        }

        // Thay thế placeholder trong description với kiểm tra null
        conversionDescription = safeReplaceDescription(conversionDescription, convertAmount, materialName, resultAmount);

        // Lấy title từ config hoặc sử dụng default
        String configTitle = config.getString("items.conversion_info.name", infoTitle);

        // Tạo ItemStack với đầy đủ config từ YAML
        ItemStack infoItem = createConversionInfoItem(
            displayMaterial,
            configTitle,
            Arrays.asList(
                "&7",
                "&fVật liệu: &b" + materialName,
                conversionDescription,
                "&fSố lượng hiện có: &e" + currentAmount,
                "&fSố lượng chuyển đổi: &c" + convertAmount,
                "&fKết quả nhận được: &a" + resultAmount,
                "&7",
                "&7Bạn có chắc chắn muốn thực hiện?"
            )
        );

        // Đặt item vào slot trung tâm với validation
        int infoSlot = config.getInt("items.conversion_info.slot", 13);
        if (infoSlot < 0 || infoSlot >= inventory.getSize()) {
            infoSlot = 13; // Fallback về slot mặc định
        }
        inventory.setItem(infoSlot, infoItem);
    }
    
    /**
     * Thêm nút xác nhận và hủy
     */
    private void addControlButtons(Inventory inventory) {
        // Nút xác nhận
        String confirmName = File.getMessage().getString("user.action.convert.confirmation_confirm", "&a&lXác nhận");
        String confirmLore = config.getString("items.confirm.lore", "&eClick để xác nhận chuyển đổi");

        ItemStack confirmItem = ItemManager.createItem(
            Material.EMERALD_BLOCK,
            confirmName,
            Arrays.asList(confirmLore)
        );
        
        int confirmSlot = config.getInt("items.confirm.slot", 11);
        // Đảm bảo slot hợp lệ
        if (confirmSlot < 0 || confirmSlot >= inventory.getSize()) {
            confirmSlot = 11; // Fallback về slot mặc định
        }

        InteractiveItem confirmButton = new InteractiveItem(confirmItem, confirmSlot).onClick((p, clickType) -> {
            // Phát âm thanh xác nhận
            try {
                String confirmSound = config.getString("confirm_sound", "BLOCK_ANVIL_USE:1.0:1.2");
                SoundManager.playSoundFromConfig(p, confirmSound);
            } catch (Exception e) {
                // Bỏ qua lỗi âm thanh
            }
            
            // Thực hiện chuyển đổi
            p.closeInventory();
            new ConvertBlock(p, material, amount, isReverseConversion).doAction();
            
            // Mở lại GUI convert block sau khi hoàn thành
            Bukkit.getScheduler().runTaskLater(com.hongminh54.storage.Storage.getStorage(), () -> {
                if (p.isOnline()) {
                    p.openInventory(new ConvertBlockGUI(p).getInventory());
                }
            }, 2L);
        });
        
        inventory.setItem(confirmButton.getSlot(), confirmButton);
        
        // Nút hủy
        String cancelName = File.getMessage().getString("user.action.convert.confirmation_cancel", "&c&lHủy");
        String cancelLore = config.getString("items.cancel.lore", "&eClick để hủy và quay lại");

        ItemStack cancelItem = ItemManager.createItem(
            Material.BARRIER,
            cancelName,
            Arrays.asList(cancelLore)
        );
        
        int cancelSlot = config.getInt("items.cancel.slot", 15);
        // Đảm bảo slot hợp lệ
        if (cancelSlot < 0 || cancelSlot >= inventory.getSize()) {
            cancelSlot = 15; // Fallback về slot mặc định
        }

        InteractiveItem cancelButton = new InteractiveItem(cancelItem, cancelSlot).onClick((p, clickType) -> {
            // Phát âm thanh hủy
            try {
                String cancelSound = config.getString("cancel_sound", "UI_BUTTON_CLICK:0.5:1.0");
                SoundManager.playSoundFromConfig(p, cancelSound);
            } catch (Exception e) {
                // Bỏ qua lỗi âm thanh
            }
            
            // Quay lại GUI convert block
            p.closeInventory();
            Bukkit.getScheduler().runTaskLater(com.hongminh54.storage.Storage.getStorage(), () -> {
                if (p.isOnline()) {
                    p.openInventory(new ConvertBlockGUI(p).getInventory());
                }
            }, 1L);
        });
        
        inventory.setItem(cancelButton.getSlot(), cancelButton);
    }

    /**
     * Tạo ItemStack cho conversion_info với đầy đủ config từ YAML
     * @param material Material để hiển thị
     * @param displayName Tên hiển thị
     * @param lore Danh sách lore
     * @return ItemStack đã được config đầy đủ
     */
    private ItemStack createConversionInfoItem(Material material, String displayName, java.util.List<String> lore) {
        try {
            // Tạo ItemStack cơ bản
            ItemStack item = ItemManager.createItem(material, displayName, lore);
            ItemMeta meta = item.getItemMeta();
            if (meta == null) return item;

            // Áp dụng config từ YAML
            String basePath = "items.conversion_info";

            // Amount
            int amount = config.getInt(basePath + ".amount", 1);
            item.setAmount(amount);

            // Unbreakable (chỉ từ 1.13+)
            if (!MaterialCompatibility.isPre113()) {
                boolean unbreakable = config.getBoolean(basePath + ".unbreakable", false);
                meta.setUnbreakable(unbreakable);
            }

            // Custom Model Data (chỉ từ 1.14+)
            applyCustomModelData(meta, basePath + ".custom-model-data");

            // Enchants
            applyEnchants(meta, basePath + ".enchants");

            // Flags
            applyFlags(meta, basePath + ".flags");

            item.setItemMeta(meta);
            return item;
        } catch (Exception e) {
            com.hongminh54.storage.Storage.getStorage().getLogger().warning("Lỗi khi tạo conversion_info item: " + e.getMessage());
            return ItemManager.createItem(material, displayName, lore);
        }
    }

    /**
     * Lấy Material cho conversion_info từ config hoặc từ material đang chuyển đổi
     * @return Material tương thích để hiển thị
     */
    private Material getConversionInfoMaterial() {
        try {
            // Thử lấy material từ config trước
            String configMaterial = config.getString("items.conversion_info.material");
            if (configMaterial != null && !configMaterial.isEmpty()) {
                Material material = MaterialCompatibility.getMaterialSafely(configMaterial);
                if (material != null) {
                    return material;
                }
            }

            // Fallback: sử dụng material từ parameter đang chuyển đổi
            Material material = MaterialCompatibility.getMaterialSafely(this.material);
            return material != null ? material : Material.STONE;
        } catch (Exception e) {
            com.hongminh54.storage.Storage.getStorage().getLogger().warning("Không thể lấy material cho conversion_info: " + e.getMessage());
            return Material.STONE; // Fallback cuối cùng
        }
    }

    /**
     * Lấy Material tương thích với phiên bản hiện tại
     * Hỗ trợ format "MATERIAL;DATA" cho 1.12.2 và "MATERIAL" cho 1.13+
     *
     * @param materialString Chuỗi material (có thể có data)
     * @return Material tương thích hoặc STONE nếu không tìm thấy
     */
    private Material getCompatibleMaterial(String materialString) {
        try {
            // Sử dụng MaterialCompatibility để xử lý material an toàn
            Material material = MaterialCompatibility.getMaterialSafely(materialString);
            return material != null ? material : Material.STONE;
        } catch (Exception e) {
            com.hongminh54.storage.Storage.getStorage().getLogger().warning("Không tìm thấy material: " + materialString);
            return Material.STONE; // Fallback
        }
    }

    /**
     * Tạo ItemStack tương thích đa phiên bản
     * Sử dụng getCompatibleMaterial() để lấy Material tương thích
     *
     * @param materialString Chuỗi material (có thể có data cho 1.12.2)
     * @param displayName Tên hiển thị
     * @param lore Danh sách lore
     * @return ItemStack tương thích
     */
    private ItemStack createVersionCompatibleItem(String materialString, String displayName, java.util.List<String> lore) {
        try {
            // Lấy Material tương thích
            Material compatibleMaterial = getCompatibleMaterial(materialString);

            // Tạo ItemStack với Material đã tương thích
            return ItemManager.createItem(compatibleMaterial, displayName, lore);
        } catch (Exception e) {
            // Fallback về item STONE nếu có lỗi
            return ItemManager.createItem(Material.STONE, displayName, lore);
        }
    }

    /**
     * Áp dụng Custom Model Data cho ItemMeta (chỉ từ 1.14+)
     * @param meta ItemMeta để áp dụng
     * @param configPath Đường dẫn config
     */
    private void applyCustomModelData(ItemMeta meta, String configPath) {
        try {
            // Kiểm tra phiên bản có hỗ trợ Custom Model Data không (1.14+)
            if (MaterialCompatibility.isPre113() || nmsAssistant.isVersionLessThan(14)) {
                return; // Không hỗ trợ trong phiên bản cũ
            }

            // Đọc custom model data từ config
            if (config.contains(configPath)) {
                int customModelData = config.getInt(configPath, -1);
                if (customModelData > 0) {
                    // Sử dụng AdvancedCompatibility để set custom model data an toàn
                    AdvancedCompatibility.setCustomModelData(meta, customModelData);

                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info("Đã áp dụng CustomModelData " + customModelData + " cho " + configPath);
                    }
                }
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi áp dụng custom model data: " + e.getMessage());
        }
    }

    /**
     * Áp dụng enchants từ config cho ItemMeta
     * @param meta ItemMeta để áp dụng
     * @param configPath Đường dẫn config
     */
    private void applyEnchants(ItemMeta meta, String configPath) {
        try {
            if (config.contains(configPath)) {
                org.bukkit.configuration.ConfigurationSection enchantsSection = config.getConfigurationSection(configPath);
                if (enchantsSection != null) {
                    for (String enchantName : enchantsSection.getKeys(false)) {
                        int level = enchantsSection.getInt(enchantName, 1);
                        java.util.Optional<com.cryptomorin.xseries.XEnchantment> enchantment =
                            com.cryptomorin.xseries.XEnchantment.matchXEnchantment(enchantName);
                        if (enchantment.isPresent() && enchantment.get().getEnchant() != null) {
                            meta.addEnchant(enchantment.get().getEnchant(), level, true);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi áp dụng enchants: " + e.getMessage());
        }
    }

    /**
     * Áp dụng flags từ config cho ItemMeta
     * @param meta ItemMeta để áp dụng
     * @param configPath Đường dẫn config
     */
    private void applyFlags(ItemMeta meta, String configPath) {
        try {
            if (config.contains(configPath)) {
                org.bukkit.configuration.ConfigurationSection flagsSection = config.getConfigurationSection(configPath);
                if (flagsSection != null) {
                    for (String flagName : flagsSection.getKeys(false)) {
                        boolean apply = flagsSection.getBoolean(flagName, false);
                        if ("ALL".equalsIgnoreCase(flagName) && apply) {
                            // Áp dụng tất cả flags
                            for (org.bukkit.inventory.ItemFlag flag : org.bukkit.inventory.ItemFlag.values()) {
                                try {
                                    meta.addItemFlags(flag);
                                } catch (Exception e) {
                                    // Bỏ qua flag không hỗ trợ trong phiên bản này
                                }
                            }
                        } else if (apply) {
                            try {
                                org.bukkit.inventory.ItemFlag flag = org.bukkit.inventory.ItemFlag.valueOf(flagName.toUpperCase());
                                meta.addItemFlags(flag);
                            } catch (IllegalArgumentException e) {
                                // Bỏ qua flag không hợp lệ
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi áp dụng flags: " + e.getMessage());
        }
    }

    /**
     * Lấy tên hiển thị của material từ config hoặc fallback
     * Hỗ trợ đa phiên bản và xử lý material có data
     *
     * @param materialString Chuỗi material
     * @return Tên hiển thị của material
     */
    private String getDisplayMaterialName(String materialString) {
        // Thử lấy từ config trước
        String configName = File.getConfig().getString("items." + materialString);
        if (configName != null && !configName.isEmpty()) {
            return configName;
        }

        // Fallback: lấy tên từ material name
        String materialName = materialString.split(";")[0];

        // Chuyển đổi tên material thành dạng dễ đọc
        return materialName.toLowerCase()
                .replace("_", " ")
                .replace("block", "")
                .trim();
    }

    /**
     * Lấy Material trang trí tương thích với phiên bản hiện tại
     * Sử dụng BLACK_STAINED_GLASS_PANE cho 1.13+ và STAINED_GLASS_PANE cho 1.12.2
     *
     * @return Material trang trí tương thích
     */
    private Material getCompatibleDecorativeMaterial() {
        try {
            // Thử BLACK_STAINED_GLASS_PANE trước (1.13+)
            return Material.valueOf("BLACK_STAINED_GLASS_PANE");
        } catch (IllegalArgumentException e) {
            try {
                // Fallback về STAINED_GLASS_PANE (1.12.2)
                return Material.valueOf("STAINED_GLASS_PANE");
            } catch (IllegalArgumentException e2) {
                // Fallback cuối cùng
                return Material.valueOf("GLASS_PANE");
            }
        }
    }

    /**
     * Thay thế placeholder trong description một cách an toàn
     * Kiểm tra null và cung cấp giá trị mặc định
     *
     * @param description Mô tả gốc
     * @param convertAmount Số lượng chuyển đổi
     * @param materialName Tên vật liệu
     * @param resultAmount Số lượng kết quả
     * @return Mô tả đã được thay thế placeholder
     */
    private String safeReplaceDescription(String description, int convertAmount, String materialName, int resultAmount) {
        if (description == null) {
            description = "&aChuyển đổi &f#amount# &a#material# &athành &f#result#";
        }

        if (materialName == null) {
            materialName = "Unknown";
        }

        return description
            .replace("#amount#", String.valueOf(convertAmount))
            .replace("#material#", materialName)
            .replace("#result#", String.valueOf(resultAmount));
    }
}
