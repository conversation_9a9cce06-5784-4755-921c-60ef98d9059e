package com.hongminh54.storage.Action;

import java.util.Optional;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.PlayerInventory;
import org.jetbrains.annotations.Contract;
import org.jetbrains.annotations.NotNull;

import com.cryptomorin.xseries.XMaterial;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;

public class Deposit {
    private final Player p;
    private final String material;
    private final Long amount;

    @Contract(pure = true)
    public Deposit(Player p, @NotNull String material, Long amount) {
        this.p = p;
        String material_data = material.replace(":", ";");
        // Chỉ giữ lại cách xử lý cho 1.16.5+
        this.material = material_data.split(";")[0];
        this.amount = amount;
    }

    public String getMaterialData() {
        if (!material.contains(";")) {
            return material + ";0";
        } else return material;
    }

    public void doAction() {
        // Ki<PERSON>m tra tính nhất quán dữ liệu trước khi thao tác
        if (!MineManager.ensureDataConsistency(getPlayer())) {
            // Nếu có vấn đề, tải lại dữ liệu
            MineManager.loadPlayerData(getPlayer());
        }

        ItemStack item = getItemStack();
        if (item != null) {
            int amount = getPlayerAmount();
            if (getAmount() > 0) {
                if (amount >= getAmount()) {
                    processDeposit(Math.toIntExact(getAmount()));
                } else {
                    String message = File.getMessage().getString("user.not_enough_items");
                    if (message == null) {
                        message = "&cBạn không có đủ &f#amount# &c#material#!";
                        Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.not_enough_items' trong file message.yml");
                    }
                    getPlayer().sendMessage(Chat.colorize(message
                            .replace("<amount>", String.valueOf(amount))
                            .replace("#amount#", String.valueOf(amount))
                            .replace("#material#", getMaterialData())));
                }
            } else {
                // Đặt tất cả
                processDeposit(amount);
            }
        }
    }

    /**
     * Xử lý việc gửi khoáng sản vào kho đơn giản
     * @param requestedAmount Số lượng muốn gửi
     */
    private void processDeposit(int requestedAmount) {
        try {
            // Kiểm tra giới hạn kho
            int oldData = MineManager.getPlayerBlock(getPlayer(), getMaterialData());
            int maxStorage = MineManager.getMaxBlock(getPlayer());
            int availableSpace = maxStorage - oldData;

            // Tính toán số lượng thực tế có thể gửi
            int actualDepositAmount = Math.min(availableSpace, requestedAmount);
            
            if (actualDepositAmount <= 0) {
                // Kho đã đầy
                String message = File.getMessage().getString("user.action.deposit.full_storage");
                if (message == null) {
                    message = "&cKho của bạn đã đầy! Không thể gửi thêm &f#amount# #material#&c.";
                    Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.action.deposit.full_storage' trong file message.yml");
                }
                
                String materialName = File.getConfig().getString("items." + getMaterialData());
                if (materialName == null) {
                    materialName = getMaterialData();
                    Storage.getStorage().getLogger().warning("Thiếu chuỗi 'items." + getMaterialData() + "' trong file config.yml");
                }
                
                getPlayer().sendMessage(Chat.colorize(message
                        .replace("#amount#", String.valueOf(requestedAmount))
                        .replace("#material#", materialName)
                        .replace("#player#", getPlayer().getName())
                        .replace("#item_amount#", String.valueOf(oldData))
                        .replace("#max_storage#", String.valueOf(maxStorage))));
                
                return;
            }

            // Thêm vào kho
            boolean success = MineManager.addBlockAmount(getPlayer(), getMaterialData(), actualDepositAmount);

            if (success) {
                // Xóa vật phẩm từ túi đồ người chơi
                removeItems(actualDepositAmount);

                // Lưu dữ liệu
                MineManager.savePlayerData(getPlayer());

                // Gửi thông báo thành công
                String message = File.getMessage().getString("user.action.deposit.deposit_item");
                if (message == null) {
                    message = "&aĐã gửi &f#amount# #material# &avào kho. Hiện tại có &f#item_amount#&a/#max_storage#";
                    Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.action.deposit.deposit_item' trong file message.yml");
                }

                String materialName = File.getConfig().getString("items." + getMaterialData());
                if (materialName == null) {
                    materialName = getMaterialData();
                    Storage.getStorage().getLogger().warning("Thiếu chuỗi 'items." + getMaterialData() + "' trong file config.yml");
                }

                int newAmount = MineManager.getPlayerBlock(getPlayer(), getMaterialData());
                getPlayer().sendMessage(Chat.colorize(message
                        .replace("#amount#", String.valueOf(actualDepositAmount))
                        .replace("#material#", materialName)
                        .replace("#player#", getPlayer().getName())
                        .replace("#item_amount#", String.valueOf(newAmount))
                        .replace("#max_storage#", String.valueOf(maxStorage))));
            } else {
                // Thông báo lỗi đơn giản
                String message = File.getMessage().getString("user.error");
                if (message == null) {
                    message = "&cĐã xảy ra lỗi khi xử lý yêu cầu của bạn.";
                }
                getPlayer().sendMessage(Chat.colorize(message));
            }
        } catch (Exception e) {
            // Xử lý ngoại lệ đơn giản
            Storage.getStorage().getLogger().severe("Lỗi khi gửi khoáng sản: " + e.getMessage());

            String errorMessage = File.getMessage().getString("user.error");
            if (errorMessage == null) {
                errorMessage = "&cĐã xảy ra lỗi khi xử lý yêu cầu của bạn.";
            }
            getPlayer().sendMessage(Chat.colorize(errorMessage));
        }
    }

    public ItemStack getItemStack() {
        Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(material);
        return xMaterial.map(XMaterial::parseItem).orElse(null);
    }

    public int getPlayerAmount() {
        final PlayerInventory inv = getPlayer().getInventory();
        final ItemStack[] items = inv.getContents();
        int c = 0;
        if (getItemStack() != null) {
            for (final ItemStack is : items) {
                if (is != null) {
                    if (is.isSimilar(getItemStack())) {
                        c += is.getAmount();
                    }
                }
            }
        }
        return c;
    }

    public void removeItems(long amount) {
        if (amount <= 0) return;
        
        final PlayerInventory inv = getPlayer().getInventory();
        final ItemStack[] items = inv.getContents();
        int c = 0;
        for (int i = 0; i < items.length; ++i) {
            final ItemStack is = items[i];
            if (is != null) {
                if (getItemStack() != null) {
                    if (is.isSimilar(getItemStack())) {
                        if (c + is.getAmount() > amount) {
                            final long canDelete = amount - c;
                            is.setAmount((int) (is.getAmount() - canDelete));
                            items[i] = is;
                            break;
                        }
                        c += is.getAmount();
                        items[i] = null;
                    }
                }
            }
        }
        inv.setContents(items);
        getPlayer().updateInventory();
    }

    public Player getPlayer() {
        return p;
    }

    public String getMaterial() {
        return material;
    }

    public Long getAmount() {
        return amount;
    }
}
