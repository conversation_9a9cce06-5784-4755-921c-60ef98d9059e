package com.hongminh54.storage.Utils;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.logging.Level;

import com.hongminh54.storage.Storage;

/**
 * Lớp này cung cấp các phương thức để ghi lỗi vào cơ sở dữ liệu
 */
public class ErrorLogger {
    private static final String ERROR_TABLE = "storage_errors";
    
    /**
     * Khởi tạo lớp ErrorLogger và tạo bảng lỗi nếu chưa tồn tại
     */
    public static void initialize() {
        createErrorTable();
    }
    
    /**
     * Tạo bảng lỗi nếu chưa tồn tại
     */
    private static void createErrorTable() {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = Storage.db.getConnection();
            String createTable = "CREATE TABLE IF NOT EXISTS " + ERROR_TABLE + " (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
                "player_name TEXT, " +
                "error_type TEXT, " +
                "error_info TEXT, " +
                "timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ")";
            
            ps = conn.prepareStatement(createTable);
            ps.executeUpdate();
            
            Storage.getStorage().getLogger().info("Đã khởi tạo bảng ghi lỗi");
        } catch (SQLException e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Không thể tạo bảng ghi lỗi: " + e.getMessage(), e);
        } finally {
            try {
                if (ps != null) ps.close();
                if (conn != null) Storage.db.returnConnection(conn);
            } catch (SQLException e) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi đóng kết nối: " + e.getMessage(), e);
            }
        }
    }
    
    /**
     * Ghi lỗi vào cơ sở dữ liệu
     * @param playerName Tên người chơi liên quan đến lỗi
     * @param errorType Loại lỗi
     * @param errorInfo Thông tin chi tiết về lỗi
     */
    public static void logError(String playerName, String errorType, String errorInfo) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = Storage.db.getConnection();
            String sql = "INSERT INTO " + ERROR_TABLE + " (player_name, error_type, error_info, timestamp) VALUES (?, ?, ?, ?)";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, playerName);
            ps.setString(2, errorType);
            ps.setString(3, errorInfo);
            ps.setTimestamp(4, new Timestamp(System.currentTimeMillis()));
            
            ps.executeUpdate();
            
            if (Storage.getStorage().getConfig().getBoolean("settings.debug_mode", false)) {
                Storage.getStorage().getLogger().info("Đã ghi lỗi vào DB: " + errorType + " - " + playerName);
            }
        } catch (SQLException e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Không thể ghi lỗi vào cơ sở dữ liệu: " + e.getMessage(), e);
        } finally {
            try {
                if (ps != null) ps.close();
                if (conn != null) Storage.db.returnConnection(conn);
            } catch (SQLException e) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi đóng kết nối: " + e.getMessage(), e);
            }
        }
    }
    
    /**
     * Xóa lỗi cũ hơn một khoảng thời gian nhất định
     * @param days Số ngày để giữ lại lỗi
     */
    public static void cleanupOldErrors(int days) {
        Connection conn = null;
        PreparedStatement ps = null;
        
        try {
            conn = Storage.db.getConnection();
            String sql = "DELETE FROM " + ERROR_TABLE + " WHERE timestamp < datetime('now', '-" + days + " days')";
            
            ps = conn.prepareStatement(sql);
            int deleted = ps.executeUpdate();
            
            if (deleted > 0) {
                Storage.getStorage().getLogger().info("Đã xóa " + deleted + " lỗi cũ hơn " + days + " ngày");
            }
        } catch (SQLException e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Không thể xóa lỗi cũ: " + e.getMessage(), e);
        } finally {
            try {
                if (ps != null) ps.close();
                if (conn != null) Storage.db.returnConnection(conn);
            } catch (SQLException e) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi đóng kết nối: " + e.getMessage(), e);
            }
        }
    }
} 