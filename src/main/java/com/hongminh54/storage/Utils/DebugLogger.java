package com.hongminh54.storage.Utils;

import java.util.logging.Level;
import com.hongminh54.storage.Storage;

/**
 * Utility class để quản lý debug logging tập trung
 * Chỉ hiển thị log khi debug_logging được bật trong config.yml
 */
public class DebugLogger {
    
    /**
     * Kiểm tra xem debug logging có được bật không
     * @return true nếu debug_logging được bật trong config
     */
    public static boolean isDebugEnabled() {
        try {
            return Storage.getStorage().getConfig().getBoolean("debug_logging", false);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Ghi log debug với level INFO
     * @param message Thông điệp cần ghi log
     */
    public static void info(String message) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().info("[DEBUG] " + message);
        }
    }
    
    /**
     * Ghi log debug với level WARNING
     * @param message Thông điệp cần ghi log
     */
    public static void warning(String message) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().warning("[DEBUG] " + message);
        }
    }
    
    /**
     * Ghi log debug với level SEVERE
     * @param message Thông điệp cần ghi log
     */
    public static void severe(String message) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().severe("[DEBUG] " + message);
        }
    }
    
    /**
     * Ghi log debug với level INFO và exception
     * @param message Thông điệp cần ghi log
     * @param throwable Exception cần ghi log
     */
    public static void info(String message, Throwable throwable) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().log(Level.INFO, "[DEBUG] " + message, throwable);
        }
    }
    
    /**
     * Ghi log debug với level WARNING và exception
     * @param message Thông điệp cần ghi log
     * @param throwable Exception cần ghi log
     */
    public static void warning(String message, Throwable throwable) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().log(Level.WARNING, "[DEBUG] " + message, throwable);
        }
    }
    
    /**
     * Ghi log debug với level SEVERE và exception
     * @param message Thông điệp cần ghi log
     * @param throwable Exception cần ghi log
     */
    public static void severe(String message, Throwable throwable) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "[DEBUG] " + message, throwable);
        }
    }
    
    /**
     * Ghi log debug với custom level
     * @param level Level của log
     * @param message Thông điệp cần ghi log
     */
    public static void log(Level level, String message) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().log(level, "[DEBUG] " + message);
        }
    }
    
    /**
     * Ghi log debug với custom level và exception
     * @param level Level của log
     * @param message Thông điệp cần ghi log
     * @param throwable Exception cần ghi log
     */
    public static void log(Level level, String message, Throwable throwable) {
        if (isDebugEnabled()) {
            Storage.getStorage().getLogger().log(level, "[DEBUG] " + message, throwable);
        }
    }
}
