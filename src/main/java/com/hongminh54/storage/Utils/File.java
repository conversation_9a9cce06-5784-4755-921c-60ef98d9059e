package com.hongminh54.storage.Utils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;

import com.hongminh54.storage.Manager.SpecialMaterialManager;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.tchristofferson.configupdater.ConfigUpdater;

import net.xconfig.bukkit.model.SimpleConfigurationManager;

public class File {

    private static FileConfiguration message;
    private static FileConfiguration config;
    private static FileConfiguration events;
    private static FileConfiguration specialOres;
    private static FileConfiguration enchants;

    // Cache các cấu hình để tăng hiệu suất
    private static FileConfiguration configCache = null;
    private static FileConfiguration messageCache = null;
    private static FileConfiguration guiStorageCache = null;
    private static FileConfiguration itemStorageCache = null;
    private static FileConfiguration specialOresCache = null;
    private static FileConfiguration enchantsCache = null;
    
    // Sử dụng Map để theo dõi thời điểm làm mới cho từng loại file
    private static final Map<String, Long> lastRefreshTimes = new HashMap<>();
    private static long CONFIG_CACHE_DURATION = 30000; // 30 giây
    
    // Các khóa cho Map lastRefreshTimes
    private static final String CONFIG_KEY = "config.yml";
    private static final String MESSAGE_KEY = "message.yml";
    private static final String GUI_STORAGE_KEY = "GUI/storage.yml";
    private static final String ITEM_STORAGE_KEY = "GUI/items.yml";
    private static final String SPECIAL_ORES_KEY = "special_material.yml";
    private static final String ENCHANTS_KEY = "enchants.yml";

    public static SimpleConfigurationManager getFileSetting() {
        return SimpleConfigurationManager.get();
    }

    public static FileConfiguration getConfig() {
        // Trả về cache nếu còn hiệu lực
        if (configCache != null && !shouldRefreshConfig(CONFIG_KEY)) {
            return configCache;
        }
        
        // Lấy cấu hình mới và cập nhật cache
        try {
            // Thay vì lấy từ SimpleConfigurationManager, đọc trực tiếp file để giữ lại comment
            java.io.File configFile = new java.io.File(Storage.getStorage().getDataFolder(), "config.yml");
            if (configFile.exists()) {
                // Sử dụng InputStreamReader với UTF-8 encoding để xử lý đúng các ký tự Unicode
                java.io.InputStreamReader reader = new java.io.InputStreamReader(
                    new java.io.FileInputStream(configFile), StandardCharsets.UTF_8);
                org.bukkit.configuration.file.YamlConfiguration yamlConfig = new org.bukkit.configuration.file.YamlConfiguration();
                yamlConfig.load(reader);
                configCache = yamlConfig;
                reader.close();
                
                // Chỉ hiển thị log khi ở chế độ debug
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("Đã tải config.yml thành công!");
                }
            } else {
                // Nếu không có file, tạo file mặc định
                Storage.getStorage().saveDefaultConfig();
                configFile = new java.io.File(Storage.getStorage().getDataFolder(), "config.yml");
                
                // Đọc lại file với reader để giữ lại comment và xử lý UTF-8
                java.io.InputStreamReader reader = new java.io.InputStreamReader(
                    new java.io.FileInputStream(configFile), StandardCharsets.UTF_8);
                org.bukkit.configuration.file.YamlConfiguration yamlConfig = new org.bukkit.configuration.file.YamlConfiguration();
                yamlConfig.load(reader);
                configCache = yamlConfig;
                reader.close();
                
                Storage.getStorage().getLogger().warning("Đã tạo file config.yml mặc định");
            }
            
            updateRefreshTime(CONFIG_KEY);
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi tải config.yml: " + e.getMessage());
            e.printStackTrace();
            // Nếu có lỗi, thử lấy từ SimpleConfigurationManager hoặc tạo cấu hình trống
            configCache = getFileSetting().get("config.yml");
            if (configCache == null) {
                configCache = new YamlConfiguration();
            }
        }
        
        return configCache;
    }

    public static FileConfiguration getMessage() {
        // Trả về cache nếu còn hiệu lực
        if (messageCache != null && !shouldRefreshConfig(MESSAGE_KEY)) {
            return messageCache;
        }
        
        // Lấy cấu hình mới và cập nhật cache
        try {
            // Đọc trực tiếp file để giữ lại comment
            java.io.File file = new java.io.File(Storage.getStorage().getDataFolder(), "message.yml");
            
            if (!file.exists()) {
                Storage.getStorage().saveResource("message.yml", false);
                Storage.getStorage().getLogger().info("Đã tạo file message.yml mới");
            }
            
            if (file.exists()) {
                // Sử dụng InputStreamReader với UTF-8 encoding để xử lý đúng các ký tự Unicode
                java.io.InputStreamReader reader = new java.io.InputStreamReader(
                    new java.io.FileInputStream(file), StandardCharsets.UTF_8);
                org.bukkit.configuration.file.YamlConfiguration yamlConfig = new org.bukkit.configuration.file.YamlConfiguration();
                yamlConfig.load(reader);
                messageCache = yamlConfig;
                reader.close();
                
                // Chỉ hiển thị log khi ở chế độ debug
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("Đã tải file message.yml thành công!");
                }
            } else {
                // Nếu vẫn không tồn tại sau khi thử tạo, sử dụng SimpleConfigurationManager
                messageCache = getFileSetting().get("message.yml");
                if (messageCache == null) {
                    messageCache = new YamlConfiguration();
                    Storage.getStorage().getLogger().warning("Không thể tạo file message.yml, sử dụng cấu hình trống");
                }
            }
            
            updateRefreshTime(MESSAGE_KEY);
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi tải message.yml: " + e.getMessage());
            // Fallback nếu có lỗi
            messageCache = getFileSetting().get("message.yml");
            if (messageCache == null) {
                messageCache = new YamlConfiguration();
            }
        }
        
        return messageCache;
    }

    public static FileConfiguration getGUIStorage() {
        long currentTime = System.currentTimeMillis();
        
        // Trả về cache nếu còn hiệu lực
        Long lastRefreshTime = lastRefreshTimes.get(GUI_STORAGE_KEY);
        if (guiStorageCache != null && lastRefreshTime != null && currentTime - lastRefreshTime < CONFIG_CACHE_DURATION) {
            return guiStorageCache;
        }
        
        // Lấy cấu hình mới và cập nhật cache
        guiStorageCache = getFileSetting().get("GUI/storage.yml");
        updateRefreshTime(GUI_STORAGE_KEY);
        return guiStorageCache;
    }

    public static FileConfiguration getItemStorage() {
        long currentTime = System.currentTimeMillis();
        
        // Trả về cache nếu còn hiệu lực
        Long lastRefreshTime = lastRefreshTimes.get(ITEM_STORAGE_KEY);
        if (itemStorageCache != null && lastRefreshTime != null && currentTime - lastRefreshTime < CONFIG_CACHE_DURATION) {
            return itemStorageCache;
        }
        
        // Lấy cấu hình mới và cập nhật cache
        itemStorageCache = getFileSetting().get("GUI/items.yml");
        updateRefreshTime(ITEM_STORAGE_KEY);
        return itemStorageCache;
    }

    /**
     * Lấy cấu hình GUI với tên được chỉ định
     * @param name Tên file GUI (không bao gồm đuôi .yml)
     * @return FileConfiguration của file GUI, hoặc cấu hình trống nếu không tìm thấy
     */
    public static FileConfiguration getGUIConfig(String name) {
        try {
            // Đảm bảo file GUI đã được load
            String fileName = "GUI/" + name + ".yml";
            FileConfiguration config = getFileSetting().get(fileName);

            if (config == null) {
                // Thử tải lại file nếu chưa có
                Storage.getStorage().getLogger().warning("GUI file not found: " + fileName + ", attempting to reload...");
                getFileSetting().build("", false, fileName);
                config = getFileSetting().get(fileName);

                if (config == null) {
                    // Nếu vẫn không có, tạo cấu hình trống và log lỗi
                    Storage.getStorage().getLogger().severe("Failed to load GUI file: " + fileName + ". Using empty configuration.");
                    config = new YamlConfiguration();
                } else {
                    Storage.getStorage().getLogger().info("Successfully reloaded GUI file: " + fileName);
                }
            }

            return config;
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Error loading GUI config '" + name + "': " + e.getMessage());
            e.printStackTrace();
            // Trả về cấu hình trống nếu có lỗi
            return new YamlConfiguration();
        }
    }

    public static FileConfiguration getSpecialMaterials() {
        // Kiểm tra cache đã được tải chưa hoặc đã quá thời gian refresh
        if (specialOresCache == null || shouldRefreshConfig(SPECIAL_ORES_KEY)) {
            try {
                // Không sử dụng SimpleConfigurationManager mà quản lý file trực tiếp
                        java.io.File file = new java.io.File(Storage.getStorage().getDataFolder(), "special_material.yml");

                        if (!file.exists()) {
                            Storage.getStorage().saveResource("special_material.yml", false);
                    Storage.getStorage().getLogger().info("Đã tạo file special_material.yml mới");
                }
                
                if (file.exists()) {
                    // Sử dụng InputStreamReader với UTF-8 encoding để xử lý đúng các ký tự Unicode
                    java.io.InputStreamReader reader = new java.io.InputStreamReader(
                        new java.io.FileInputStream(file), StandardCharsets.UTF_8);
                    org.bukkit.configuration.file.YamlConfiguration yamlConfig = new org.bukkit.configuration.file.YamlConfiguration();
                    yamlConfig.load(reader);
                    specialOresCache = yamlConfig;
                    reader.close();
                    
                    // Chỉ hiển thị log khi ở chế độ debug
                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info("Đã tải file special_ores.yml thành công");
                    }
                } else {
                    // Nếu vẫn không tồn tại sau khi thử tạo, tạo một cấu hình trống
                    specialOresCache = new YamlConfiguration();
                    Storage.getStorage().getLogger().warning("Không thể tạo file special_ores.yml, sử dụng cấu hình trống");
                }
                
                updateRefreshTime(SPECIAL_ORES_KEY);
            } catch (Exception e) {
                Storage.getStorage().getLogger().severe("Lỗi khi tải file special_ores.yml: " + e.getMessage());
                e.printStackTrace();
                
                // Đảm bảo luôn có một cấu hình, ngay cả khi có lỗi
                if (specialOresCache == null) {
                    specialOresCache = new YamlConfiguration();
                }
            }
        }
        
        return specialOresCache;
    }

    public static FileConfiguration getEnchants() {
        // Kiểm tra cache đã được tải chưa hoặc đã quá thời gian refresh
        if (enchantsCache == null || shouldRefreshConfig(ENCHANTS_KEY)) {
            try {
                // Quản lý file trực tiếp
                java.io.File file = new java.io.File(Storage.getStorage().getDataFolder(), "enchants.yml");

                if (!file.exists()) {
                    Storage.getStorage().saveResource("enchants.yml", false);
                    Storage.getStorage().getLogger().info("Đã tạo file enchants.yml mới");
                }

                if (file.exists()) {
                    // Sử dụng InputStreamReader với UTF-8 encoding để xử lý đúng các ký tự Unicode
                    java.io.InputStreamReader reader = new java.io.InputStreamReader(
                        new java.io.FileInputStream(file), StandardCharsets.UTF_8);
                    org.bukkit.configuration.file.YamlConfiguration yamlConfig = new org.bukkit.configuration.file.YamlConfiguration();
                    yamlConfig.load(reader);
                    enchantsCache = yamlConfig;
                    reader.close();

                    // Chỉ hiển thị log khi ở chế độ debug
                    if (Storage.getStorage().isDebug()) {
                        Storage.getStorage().getLogger().info("Đã tải file enchants.yml thành công");
                    }
                } else {
                    // Nếu vẫn không tồn tại sau khi thử tạo, tạo một cấu hình trống
                    enchantsCache = new YamlConfiguration();
                    Storage.getStorage().getLogger().warning("Không thể tạo file enchants.yml, sử dụng cấu hình trống");
                }

                updateRefreshTime(ENCHANTS_KEY);
            } catch (Exception e) {
                Storage.getStorage().getLogger().severe("Lỗi khi tải file enchants.yml: " + e.getMessage());
                e.printStackTrace();

                // Đảm bảo luôn có một cấu hình, ngay cả khi có lỗi
                if (enchantsCache == null) {
                    enchantsCache = new YamlConfiguration();
                }
            }
        }

        return enchantsCache;
    }

    public static void loadFiles() {
        getFileSetting().build("", false, "config.yml", "message.yml");
        loadEvents();
        // Không gọi loadSpecialMaterials() nữa mà gọi getSpecialMaterials() để load file
        getSpecialMaterials();
        // Tải file enchants.yml
        getEnchants();
        loadDefaultConfig();

        // Khởi tạo cache
        configCache = getFileSetting().get("config.yml");
        messageCache = getFileSetting().get("message.yml");
        // specialOresCache và enchantsCache đã được khởi tạo trong các phương thức get tương ứng
        updateRefreshTime(CONFIG_KEY);
        updateRefreshTime(MESSAGE_KEY);
        updateRefreshTime(GUI_STORAGE_KEY);
        updateRefreshTime(ITEM_STORAGE_KEY);
        updateRefreshTime(SPECIAL_ORES_KEY);
        updateRefreshTime(ENCHANTS_KEY);
    }

    public static void reloadFiles() {
        // Làm mới cache để buộc đọc lại từ đĩa
        invalidateAllCaches();
        
        // Lưu dữ liệu người chơi trước khi tải lại
        for (Player p : Bukkit.getOnlinePlayers()) {
            MineManager.savePlayerData(p);
        }
        
        // Reload các file GUI thông qua SimpleConfigurationManager
        getFileSetting().build("", false, "GUI/storage.yml", "GUI/items.yml", "GUI/stats.yml", "GUI/leaderboard.yml", "GUI/convert_block.yml", "GUI/convert_confirmation.yml", "GUI/player_search.yml", "GUI/transfer.yml", "GUI/transfer_storage.yml");
        
        // Tải lại file events.yml
        loadEvents();
        
        // Tải lại các file thông qua phương thức get riêng để sử dụng logic đọc file giữ lại comment
        config = getConfig();
        message = getMessage();
        specialOres = getSpecialMaterials();
        enchants = getEnchants();

        // Đảm bảo tải lại SpecialMaterialManager
        SpecialMaterialManager.reload();
        
        // Cập nhật cache mới với dữ liệu đã tải lại
        guiStorageCache = getFileSetting().get("GUI/storage.yml");
        itemStorageCache = getFileSetting().get("GUI/items.yml");
        
        // Tải lại dữ liệu người chơi
        for (Player p : Bukkit.getOnlinePlayers()) {
            MineManager.loadPlayerData(p);
        }
        
        Storage.getStorage().getLogger().info("Đã tải lại tất cả file cấu hình và cập nhật cache");
    }

    public static void loadGUI() {
        getFileSetting().build("", false, "GUI/storage.yml", "GUI/items.yml", "GUI/stats.yml", "GUI/leaderboard.yml", "GUI/convert_block.yml", "GUI/convert_confirmation.yml", "GUI/player_search.yml", "GUI/transfer.yml", "GUI/transfer_storage.yml");

        // Kiểm tra xem các file đã được tải thành công chưa
        checkGUIFilesExist();
    }
    
    /**
     * Kiểm tra xem các file GUI cần thiết đã tồn tại chưa
     */
    private static void checkGUIFilesExist() {
        String[] guiFiles = {"storage.yml", "items.yml", "stats.yml", "leaderboard.yml", "convert_block.yml", "convert_confirmation.yml", "player_search.yml", "transfer.yml", "transfer_storage.yml"};

        for (String file : guiFiles) {
            FileConfiguration config = getFileSetting().get("GUI/" + file);
            if (config == null) {
                Storage.getStorage().getLogger().warning("Cannot find GUI file: GUI/" + file);
                // Thử tải lại file
                getFileSetting().build("", false, "GUI/" + file);

                // Kiểm tra lại sau khi thử tải
                if (getFileSetting().get("GUI/" + file) == null) {
                    Storage.getStorage().getLogger().severe("Failed to load GUI file: GUI/" + file + ". This may cause errors!");
                } else {
                    Storage.getStorage().getLogger().info("Successfully loaded GUI file: GUI/" + file);
                }
            }
        }
    }

    public static void saveFiles() {
        // Lưu các file chính với cách giữ lại comment
        saveConfigWithComments();
        saveMessageWithComments();
        saveSpecialMaterials();
        saveEnchants();

        // Các file khác vẫn sử dụng SimpleConfigurationManager
        getFileSetting().save("GUI/storage.yml", "GUI/items.yml", "GUI/stats.yml", "GUI/leaderboard.yml", "GUI/convert_block.yml", "GUI/convert_confirmation.yml", "GUI/player_search.yml", "GUI/transfer.yml", "GUI/transfer_storage.yml");
        saveEvents();
    }

    public static boolean saveConfigWithComments() {
        try {
            java.io.File file = new java.io.File(Storage.getStorage().getDataFolder(), "config.yml");
            
            // Đảm bảo file tồn tại trước khi lưu
            if (!file.exists() && configCache != null) {
                file.getParentFile().mkdirs();
                file.createNewFile();
            }
            
            if (configCache == null) {
                Storage.getStorage().getLogger().warning("Không thể lưu file config.yml vì không có dữ liệu trong cache");
                return false;
            }
            
            // Sử dụng phương thức save thông thường
            configCache.save(file);
            Storage.getStorage().getLogger().info("Đã lưu file config.yml thành công");
            return true;
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi lưu file config.yml: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    public static void updateConfig() {
        getFileSetting().save("config.yml");
        java.io.File configFile = new java.io.File(Storage.getStorage().getDataFolder(), "config.yml");
        FileConfiguration defaultConfig = YamlConfiguration.loadConfiguration(new InputStreamReader(Objects.requireNonNull(Storage.getStorage().getResource("config.yml")), StandardCharsets.UTF_8));
        FileConfiguration currentConfig = YamlConfiguration.loadConfiguration(configFile);
        int default_configVersion = defaultConfig.getInt("config_version");
        int current_configVersion = defaultConfig.contains("config_version") ? defaultConfig.getInt("config_version") : 0;
        if (default_configVersion > current_configVersion || default_configVersion < current_configVersion) {
            List<String> default_whitelist_fortune = defaultConfig.getStringList("whitelist_fortune");
            List<String> current_whitelist_fortune = currentConfig.getStringList("whitelist_fortune");
            List<String> default_blacklist_world = defaultConfig.getStringList("blacklist_world");
            List<String> current_blacklist_world = currentConfig.getStringList("blacklist_world");
            Storage.getStorage().getLogger().log(Level.WARNING, "Your config is updating...");
            if (current_whitelist_fortune.isEmpty()) {
                getConfig().set("whitelist_fortune", default_whitelist_fortune);
                getFileSetting().save("config.yml");
            }
            if (current_blacklist_world.isEmpty()) {
                getConfig().set("blacklist_world", default_blacklist_world);
                getFileSetting().save("config.yml");
            }
            try {
                ConfigUpdater.update(Storage.getStorage(), "config.yml", configFile, "items", "blocks", "worth");
                Storage.getStorage().getLogger().log(Level.WARNING, "Your config have been updated successful");
            } catch (IOException e) {
                Storage.getStorage().getLogger().log(Level.WARNING, "Can not update config by it self, please backup and rename your config then restart to get newest config!!");
                e.printStackTrace();
            }
            getFileSetting().reload("config.yml");
        }
    }

    public static void updateMessage() {
        getFileSetting().save("message.yml");
        java.io.File configFile = new java.io.File(Storage.getStorage().getDataFolder(), "message.yml");
        FileConfiguration defaultConfig = YamlConfiguration.loadConfiguration(new InputStreamReader(Objects.requireNonNull(Storage.getStorage().getResource("message.yml")), StandardCharsets.UTF_8));
        FileConfiguration currentConfig = YamlConfiguration.loadConfiguration(configFile);
        int default_configVersion = defaultConfig.getInt("message_version");
        int current_configVersion = defaultConfig.contains("message_version") ? defaultConfig.getInt("message_version") : 0;
        if (default_configVersion > current_configVersion || default_configVersion < current_configVersion) {
            Storage.getStorage().getLogger().log(Level.WARNING, "Your message is updating...");
            List<String> default_admin_help = defaultConfig.getStringList("admin.help");
            List<String> default_user_help = defaultConfig.getStringList("user.help");
            List<String> current_admin_help = currentConfig.getStringList("admin.help");
            List<String> current_user_help = currentConfig.getStringList("user.help");
            if (default_admin_help.size() != current_admin_help.size()) {
                getConfig().set("admin.help", default_admin_help);
                getFileSetting().save("message.yml");
            }
            if (default_user_help.size() != current_user_help.size()) {
                getConfig().set("user.help", default_user_help);
                getFileSetting().save("message.yml");
            }
            try {
                ConfigUpdater.update(Storage.getStorage(), "message.yml", configFile);
                Storage.getStorage().getLogger().log(Level.WARNING, "Your message have been updated successful");
            } catch (IOException e) {
                Storage.getStorage().getLogger().log(Level.WARNING, "Can not update message by it self, please backup and rename your message then restart to get newest message!!");
                e.printStackTrace();
            }
            getFileSetting().reload("message.yml");
        }
    }

    /**
     * Lấy file cấu hình sự kiện
     * @return FileConfiguration của events.yml
     */
    public static FileConfiguration getEvents() {
        if (events == null) {
            loadEvents();
        }
        return events;
    }
    
    /**
     * Tải file events.yml
     */
    public static void loadEvents() {
        try {
            // Tạo file events.yml nếu chưa tồn tại
            java.io.File eventsFile = new java.io.File(Storage.getStorage().getDataFolder(), "events.yml");
            if (!eventsFile.exists()) {
                Storage.getStorage().saveResource("events.yml", false);
            }
            events = YamlConfiguration.loadConfiguration(eventsFile);
        } catch (Exception e) {
            e.printStackTrace();
            // Nếu có lỗi, tạo một cấu hình trống
            events = new YamlConfiguration();
        }
    }
    
    /**
     * Lưu file events.yml
     */
    public static void saveEvents() {
        try {
            if (events != null) {
                java.io.File eventsFile = new java.io.File(Storage.getStorage().getDataFolder(), "events.yml");
                events.save(eventsFile);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Storage.getStorage().getLogger().severe("Cannot save events.yml file!");
        }
    }

    /**
     * Tải cấu hình mặc định từ plugin JAR
     */
    private static void loadDefaultConfig() {
        Storage plugin = Storage.getStorage();
        plugin.saveDefaultConfig();
        config = plugin.getConfig();
        
        // Thêm các cấu hình mặc định nếu chưa có
        if (!config.contains("settings.transfer_percentage")) {
            config.set("settings.transfer_percentage", 25);
            plugin.getLogger().info("Đã thêm cấu hình mặc định cho tỷ lệ chuyển tài nguyên: 25%");
        }
        
        if (!config.contains("settings.max_particle_count")) {
            config.set("settings.max_particle_count", 15);
            plugin.getLogger().info("Đã thêm cấu hình mặc định cho số hạt hiệu ứng tối đa: 15");
        }
        
        if (!config.contains("settings.large_transfer_threshold")) {
            config.set("settings.large_transfer_threshold", 100);
            plugin.getLogger().info("Đã thêm cấu hình mặc định cho ngưỡng giao dịch lớn: 100");
        }
        
        if (!config.contains("settings.max_history_display")) {
            config.set("settings.max_history_display", 50);
            plugin.getLogger().info("Đã thêm cấu hình mặc định cho số lượng lịch sử hiển thị tối đa: 50");
        }
        
        if (!config.contains("settings.search_timeout")) {
            config.set("settings.search_timeout", 30);
            plugin.getLogger().info("Đã thêm cấu hình mặc định cho thời gian chờ tìm kiếm: 30 giây");
        }

        if (!config.contains("settings.max_player_name_length")) {
            config.set("settings.max_player_name_length", 16);
            plugin.getLogger().info("Đã thêm cấu hình mặc định cho độ dài tên người chơi tối đa: 16");
        }

        // Thêm các cấu hình mới được bổ sung vào settings
        if (!config.contains("settings.effects_enabled")) {
            config.set("settings.effects_enabled", true);
            plugin.getLogger().info("Đã thêm cấu hình mặc định cho hiệu ứng âm thanh và hạt: true");
        }

        if (!config.contains("settings.auto_pickup")) {
            config.set("settings.auto_pickup", true);
            plugin.getLogger().info("Đã thêm cấu hình mặc định cho tự động nhặt tài nguyên: true");
        }

        if (!config.contains("settings.config_cache_duration")) {
            config.set("settings.config_cache_duration", 30000);
            plugin.getLogger().info("Đã thêm cấu hình mặc định cho thời gian cache cấu hình: 30000ms");
        }
        
        // Thêm cấu hình mặc định cho hiệu ứng nếu chưa có
        addDefaultSoundEffect("effects.transfer_success.sender_sound", "ENTITY_PLAYER_LEVELUP:0.5:1.2");
        addDefaultSoundEffect("effects.transfer_success.receiver_sound", "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.0");
        addDefaultSoundEffect("effects.transfer_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
        addDefaultSoundEffect("effects.permission_fail.sound", "ENTITY_VILLAGER_NO:1.0:1.0");
        addDefaultSoundEffect("effects.search_open.sound", "BLOCK_NOTE_BLOCK_PLING:0.5:1.2");
        addDefaultSoundEffect("effects.search_success.sound", "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.0");
        addDefaultSoundEffect("effects.search_fail.sound", "ENTITY_VILLAGER_NO:0.5:1.0");
        addDefaultSoundEffect("effects.search_cancel.sound", "ENTITY_VILLAGER_NO:0.5:1.0");
        addDefaultSoundEffect("effects.storage_open.sound", "BLOCK_CHEST_OPEN:0.5:1.0");
        addDefaultSoundEffect("effects.gui_click.sound", "UI_BUTTON_CLICK:0.5:1.0");
        addDefaultSoundEffect("effects.history_view.sound", "BLOCK_NOTE_BLOCK_PLING:0.5:1.2");
        
        // Thêm cấu hình mặc định cho hiệu ứng hạt nếu chưa có
        addDefaultParticleEffect("effects.transfer_success.sender_particle", "VILLAGER_HAPPY:0.5:0.5:0.5:0.1:10");
        addDefaultParticleEffect("effects.transfer_success.receiver_particle", "VILLAGER_HAPPY:0.5:0.5:0.5:0.1:10");
        addDefaultParticleEffect("effects.search_success.particle", "VILLAGER_HAPPY:0.5:0.5:0.5:0.1:10");
        addDefaultParticleEffect("effects.large_transfer.sender_particle", "SPELL_WITCH:0.2:0.2:0.2:0.05:10");
        addDefaultParticleEffect("effects.large_transfer.receiver_particle", "TOTEM:0.5:0.5:0.5:0.1:10");
        addDefaultParticleEffect("effects.collect.particle", "VILLAGER_HAPPY:0.3:0.3:0.3:0.05:8");
        
        // Lưu cấu hình nếu có thay đổi
        plugin.saveConfig();
    }

    /**
     * Thêm cấu hình âm thanh mặc định nếu chưa có
     * @param path Đường dẫn cấu hình
     * @param defaultValue Giá trị mặc định (định dạng "SOUND:VOLUME:PITCH")
     */
    private static void addDefaultSoundEffect(String path, String defaultValue) {
        Storage plugin = Storage.getStorage();
        if (!config.contains(path)) {
            config.set(path, defaultValue);
            plugin.getLogger().info("Đã thêm cấu hình mặc định cho âm thanh: " + path);
        }
    }

    /**
     * Thêm cấu hình hiệu ứng hạt mặc định nếu chưa có
     * @param path Đường dẫn cấu hình
     * @param defaultValue Giá trị mặc định (định dạng "PARTICLE:OFFSETX:OFFSETY:OFFSETZ:SPEED:COUNT")
     */
    private static void addDefaultParticleEffect(String path, String defaultValue) {
        Storage plugin = Storage.getStorage();
        if (!config.contains(path)) {
            config.set(path, defaultValue);
            plugin.getLogger().info("Đã thêm cấu hình mặc định cho hiệu ứng hạt: " + path);
        }
    }

    /**
     * Tải cấu hình khoáng sản đặc biệt
     * @deprecated Phương thức này đã được thay thế bởi getSpecialMaterials(), không nên sử dụng trực tiếp nữa
     */
    @Deprecated
    public static void loadSpecialOres() {
        try {
            java.io.File file = new java.io.File(Storage.getStorage().getDataFolder(), "special_material.yml");

            if (!file.exists()) {
                Storage.getStorage().saveResource("special_material.yml", false);
                Storage.getStorage().getLogger().info("Đã tạo file special_material.yml mới");
            }
            
            if (file.exists()) {
                specialOresCache = YamlConfiguration.loadConfiguration(file);
                Storage.getStorage().getLogger().info("Đã tải file special_material.yml thành công");
            } else {
                // Nếu vẫn không tồn tại sau khi thử tạo, tạo một cấu hình trống
                specialOresCache = new YamlConfiguration();
                Storage.getStorage().getLogger().warning("Không thể tạo file special_material.yml, sử dụng cấu hình trống");
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi tải file special_material.yml: " + e.getMessage());
            e.printStackTrace();
            
            // Đảm bảo luôn có một cấu hình, ngay cả khi có lỗi
            if (specialOresCache == null) {
                specialOresCache = new YamlConfiguration();
            }
        }
    }
    
    /**
     * Lưu cấu hình khoáng sản đặc biệt
     * @return true nếu lưu thành công
     */
    public static boolean saveSpecialMaterials() {
        try {
            java.io.File file = new java.io.File(Storage.getStorage().getDataFolder(), "special_material.yml");
            
            // Đảm bảo file tồn tại trước khi lưu
            if (!file.exists() && specialOresCache != null) {
                file.getParentFile().mkdirs();
                file.createNewFile();
            }
            
            if (specialOresCache == null) {
                Storage.getStorage().getLogger().warning("Không thể lưu file special_material.yml vì không có dữ liệu trong cache");
                return false;
            }
            
            // Lưu file với UTF-8 encoding
            try {
                // Sử dụng phương thức save thông thường - YamlConfiguration sẽ tự quản lý encoding
                specialOresCache.save(file);
                
                Storage.getStorage().getLogger().info("Đã lưu file special_material.yml thành công");
                
                // Mở và đọc file đã lưu để hiển thị vào log nếu trong chế độ debug
                if (Storage.getStorage().isDebug()) {
                    String fileEncoding = System.getProperty("file.encoding");
                    Storage.getStorage().getLogger().info("File encoding: " + fileEncoding);
                    
                    // Đọc file đã lưu với UTF-8
                    java.io.InputStreamReader reader = new java.io.InputStreamReader(
                        new java.io.FileInputStream(file), StandardCharsets.UTF_8);
                    char[] buffer = new char[1024];
                    StringBuilder content = new StringBuilder();
                    int read;
                    while ((read = reader.read(buffer)) != -1) {
                        content.append(buffer, 0, read);
                    }
                    reader.close();
                    
                    // Log vài dòng đầu tiên để kiểm tra
                    String[] lines = content.toString().split("\n");
                    int linesToShow = Math.min(5, lines.length);
                    Storage.getStorage().getLogger().info("Nội dung file (5 dòng đầu):");
                    for (int i = 0; i < linesToShow; i++) {
                        Storage.getStorage().getLogger().info(lines[i]);
                    }
                }
                
                return true;
            } catch (Exception e) {
                Storage.getStorage().getLogger().severe("Lỗi khi lưu file special_ores.yml: " + e.getMessage());
                e.printStackTrace();
                return false;
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi lưu file special_ores.yml: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Làm mới cache file special_ores.yml để tải lại từ đĩa
     * Phương thức này sẽ đặt cache về null và xóa thời gian làm mới
     * để lần gọi getSpecialMaterials() tiếp theo sẽ đọc lại từ đĩa
     */
    public static void invalidateSpecialMaterialsCache() {
        specialOresCache = null;
        lastRefreshTimes.remove(SPECIAL_ORES_KEY); // Xóa thời gian làm mới để buộc tải lại
        Storage.getStorage().getLogger().info("Đã làm mới cache special_ores.yml để tải lại từ đĩa trong lần truy cập tiếp theo");
    }

    /**
     * Lưu cấu hình enchants
     * @return true nếu lưu thành công
     */
    public static boolean saveEnchants() {
        try {
            java.io.File file = new java.io.File(Storage.getStorage().getDataFolder(), "enchants.yml");

            // Đảm bảo file tồn tại trước khi lưu
            if (!file.exists() && enchantsCache != null) {
                file.getParentFile().mkdirs();
                file.createNewFile();
            }

            if (enchantsCache == null) {
                Storage.getStorage().getLogger().warning("Không thể lưu file enchants.yml vì không có dữ liệu trong cache");
                return false;
            }

            // Lưu file với UTF-8 encoding
            enchantsCache.save(file);
            Storage.getStorage().getLogger().info("Đã lưu file enchants.yml thành công");
            return true;
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi lưu file enchants.yml: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Làm mới cache file enchants.yml để tải lại từ đĩa
     */
    public static void invalidateEnchantsCache() {
        enchantsCache = null;
        lastRefreshTimes.remove(ENCHANTS_KEY); // Xóa thời gian làm mới để buộc tải lại
        Storage.getStorage().getLogger().info("Đã làm mới cache enchants.yml để tải lại từ đĩa trong lần truy cập tiếp theo");
    }

    /**
     * Làm mới cache file config.yml để tải lại từ đĩa
     * Phương thức này sẽ đặt cache về null và xóa thời gian làm mới
     * để lần gọi getConfig() tiếp theo sẽ đọc lại từ đĩa
     */
    public static void invalidateConfigCache() {
        configCache = null;
        lastRefreshTimes.remove(CONFIG_KEY); // Xóa thời gian làm mới để buộc tải lại
        Storage.getStorage().getLogger().info("Đã làm mới cache config.yml để tải lại từ đĩa trong lần truy cập tiếp theo");
    }

    /**
     * Làm mới cache file message.yml để tải lại từ đĩa
     */
    public static void invalidateMessageCache() {
        messageCache = null;
        lastRefreshTimes.remove(MESSAGE_KEY); // Xóa thời gian làm mới để buộc tải lại
        Storage.getStorage().getLogger().info("Đã làm mới cache message.yml để tải lại từ đĩa trong lần truy cập tiếp theo");
    }
    
    /**
     * Lưu file message.yml với cách giữ lại comment
     * @return true nếu lưu thành công
     */
    public static boolean saveMessageWithComments() {
        try {
            java.io.File file = new java.io.File(Storage.getStorage().getDataFolder(), "message.yml");
            
            // Đảm bảo file tồn tại trước khi lưu
            if (!file.exists() && messageCache != null) {
                file.getParentFile().mkdirs();
                file.createNewFile();
            }
            
            if (messageCache == null) {
                Storage.getStorage().getLogger().warning("Không thể lưu file message.yml vì không có dữ liệu trong cache");
                return false;
            }
            
            // Lưu với phương thức thông thường
            messageCache.save(file);
            Storage.getStorage().getLogger().info("Đã lưu file message.yml thành công");
            return true;
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi lưu file message.yml: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Đặt thời lượng cache cho các file cấu hình
     * @param durationMillis Thời lượng cache tính bằng mili giây
     */
    public static void setCacheDuration(long durationMillis) {
        if (durationMillis >= 5000) { // Đảm bảo thời gian cache tối thiểu là 5 giây
            CONFIG_CACHE_DURATION = durationMillis;
            Storage.getStorage().getLogger().info("Đã đặt thời gian cache cấu hình: " + durationMillis + "ms");
        } else {
            Storage.getStorage().getLogger().warning("Thời gian cache quá ngắn, đặt về giá trị mặc định 30000ms");
            CONFIG_CACHE_DURATION = 30000;
        }
    }
    
    /**
     * Kiểm tra xem cấu hình có nên được tải lại hay không dựa trên thời gian
     * @param fileKey Khóa xác định loại file
     * @return true nếu nên tải lại
     */
    private static boolean shouldRefreshConfig(String fileKey) {
        long currentTime = System.currentTimeMillis();
        Long lastRefreshTime = lastRefreshTimes.get(fileKey);
        return (lastRefreshTime == null || currentTime - lastRefreshTime >= CONFIG_CACHE_DURATION);
    }

    /**
     * Cập nhật thời điểm làm mới cho một loại file
     * @param fileKey Khóa xác định loại file
     */
    private static void updateRefreshTime(String fileKey) {
        lastRefreshTimes.put(fileKey, System.currentTimeMillis());
    }

    /**
     * Làm mới tất cả các cache để tải lại từ đĩa trong lần truy cập tiếp theo
     * Phương thức này sẽ xóa tất cả các cache và thời gian làm mới
     */
    public static void invalidateAllCaches() {
        // Xóa các cache
        configCache = null;
        messageCache = null;
        guiStorageCache = null;
        itemStorageCache = null;
        specialOresCache = null;
        enchantsCache = null;

        // Xóa các thời gian làm mới
        lastRefreshTimes.clear();

        // Log thông báo
        if (Storage.getStorage().isDebug()) {
            Storage.getStorage().getLogger().info("Đã làm mới tất cả cache để tải lại từ đĩa trong lần truy cập tiếp theo");
        }
    }
}
