package com.hongminh54.storage.Utils;

import org.bukkit.Bukkit;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;

import java.util.Collection;
import java.util.concurrent.CompletableFuture;

/**
 * Lớp hỗ trợ tương thích Server API cho Minecraft 1.12.2 - 1.21.x
 * Xử lý các vấn đề tương thích với server operations, async tasks, events, v.v.
 */
public class ServerCompatibility {
    
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_20_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(20);
    
    /**
     * <PERSON><PERSON>y danh sách người chơi online một cách an toàn
     * 
     * @return Collection của Player
     */
    public static Collection<? extends Player> getOnlinePlayers() {
        try {
            return Bukkit.getOnlinePlayers();
        } catch (Exception e) {
            // Fallback với reflection cho phiên bản rất cũ
            return getOnlinePlayersReflection();
        }
    }
    
    /**
     * Lấy TPS của server một cách an toàn
     * 
     * @return TPS hiện tại hoặc 20.0 nếu không thể lấy
     */
    public static double getServerTPS() {
        try {
            // Thử sử dụng Bukkit API trước (1.17+)
            try {
                java.lang.reflect.Method getTpsMethod = Bukkit.class.getMethod("getTPS");
                double[] tps = (double[]) getTpsMethod.invoke(null);
                return tps[0]; // TPS trong 1 phút qua
            } catch (Exception e) {
                // Fallback: sử dụng reflection cho phiên bản cũ
                return getServerTPSReflection();
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy TPS: " + e.getMessage());
            }
            return 20.0; // Giả định TPS tốt
        }
    }
    
    /**
     * Chạy task async một cách an toàn
     * 
     * @param runnable Task cần chạy
     * @return BukkitTask hoặc null nếu có lỗi
     */
    public static BukkitTask runAsync(Runnable runnable) {
        if (runnable == null) {
            return null;
        }
        
        try {
            return Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), runnable);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể chạy async task: " + e.getMessage());
            }
            // Fallback: chạy sync
            return Bukkit.getScheduler().runTask(Storage.getStorage(), runnable);
        }
    }
    
    /**
     * Chạy task sync một cách an toàn
     * 
     * @param runnable Task cần chạy
     * @return BukkitTask hoặc null nếu có lỗi
     */
    public static BukkitTask runSync(Runnable runnable) {
        if (runnable == null) {
            return null;
        }
        
        try {
            return Bukkit.getScheduler().runTask(Storage.getStorage(), runnable);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể chạy sync task: " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * Chạy task với delay một cách an toàn
     * 
     * @param runnable Task cần chạy
     * @param delay Delay tính bằng ticks
     * @return BukkitTask hoặc null nếu có lỗi
     */
    public static BukkitTask runTaskLater(Runnable runnable, long delay) {
        if (runnable == null) {
            return null;
        }
        
        try {
            return Bukkit.getScheduler().runTaskLater(Storage.getStorage(), runnable, delay);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể chạy delayed task: " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * Chạy task lặp lại một cách an toàn
     * 
     * @param runnable Task cần chạy
     * @param delay Delay ban đầu tính bằng ticks
     * @param period Khoảng thời gian lặp lại tính bằng ticks
     * @return BukkitTask hoặc null nếu có lỗi
     */
    public static BukkitTask runTaskTimer(Runnable runnable, long delay, long period) {
        if (runnable == null) {
            return null;
        }
        
        try {
            return Bukkit.getScheduler().runTaskTimer(Storage.getStorage(), runnable, delay, period);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể chạy timer task: " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * Call event một cách an toàn
     * 
     * @param event Event cần call
     */
    public static void callEvent(Event event) {
        if (event == null) {
            return;
        }
        
        try {
            Bukkit.getPluginManager().callEvent(event);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể call event: " + e.getMessage());
            }
        }
    }
    
    /**
     * Broadcast message một cách an toàn
     * 
     * @param message Message cần broadcast
     */
    public static void broadcastMessage(String message) {
        if (message == null || message.isEmpty()) {
            return;
        }
        
        try {
            Bukkit.broadcastMessage(Chat.colorize(message));
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể broadcast message: " + e.getMessage());
            }
        }
    }
    
    /**
     * Lấy world một cách an toàn
     * 
     * @param worldName Tên world
     * @return World hoặc null nếu không tìm thấy
     */
    public static World getWorld(String worldName) {
        if (worldName == null || worldName.isEmpty()) {
            return null;
        }
        
        try {
            return Bukkit.getWorld(worldName);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy world: " + worldName);
            }
            return null;
        }
    }
    
    /**
     * Kiểm tra xem có phải main thread không
     * 
     * @return true nếu đang ở main thread
     */
    public static boolean isPrimaryThread() {
        try {
            return Bukkit.isPrimaryThread();
        } catch (Exception e) {
            // Fallback: giả định đang ở main thread
            return true;
        }
    }
    
    /**
     * Chạy task trên main thread nếu cần
     * 
     * @param runnable Task cần chạy
     */
    public static void ensureMainThread(Runnable runnable) {
        if (runnable == null) {
            return;
        }
        
        if (isPrimaryThread()) {
            // Đã ở main thread, chạy trực tiếp
            try {
                runnable.run();
            } catch (Exception e) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Lỗi khi chạy task trên main thread: " + e.getMessage());
                }
            }
        } else {
            // Không ở main thread, schedule về main thread
            runSync(runnable);
        }
    }
    
    /**
     * Tạo CompletableFuture cho async operation
     * 
     * @param supplier Supplier function
     * @param <T> Kiểu dữ liệu trả về
     * @return CompletableFuture
     */
    public static <T> CompletableFuture<T> supplyAsync(java.util.function.Supplier<T> supplier) {
        if (supplier == null) {
            return CompletableFuture.completedFuture(null);
        }
        
        CompletableFuture<T> future = new CompletableFuture<>();
        
        runAsync(() -> {
            try {
                T result = supplier.get();
                future.complete(result);
            } catch (Exception e) {
                future.completeExceptionally(e);
            }
        });
        
        return future;
    }
    
    /**
     * Lấy danh sách người chơi bằng reflection (fallback method)
     */
    @SuppressWarnings("unchecked")
    private static Collection<? extends Player> getOnlinePlayersReflection() {
        try {
            Object result = Bukkit.class.getMethod("getOnlinePlayers").invoke(null);
            if (result instanceof Collection) {
                return (Collection<? extends Player>) result;
            } else if (result instanceof Player[]) {
                return java.util.Arrays.asList((Player[]) result);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy online players: " + e.getMessage());
            }
        }
        return java.util.Collections.emptyList();
    }
    
    /**
     * Lấy TPS bằng reflection (fallback method)
     */
    private static double getServerTPSReflection() {
        try {
            String version = Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];
            Class<?> minecraftServerClass = Class.forName("net.minecraft.server." + version + ".MinecraftServer");
            Object minecraftServer = minecraftServerClass.getMethod("getServer").invoke(null);
            
            // Lấy field recentTps
            java.lang.reflect.Field recentTpsField = minecraftServerClass.getField("recentTps");
            double[] recentTps = (double[]) recentTpsField.get(minecraftServer);
            
            return recentTps[0]; // TPS trong 1 phút qua
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy TPS bằng reflection: " + e.getMessage());
            }
            return 20.0;
        }
    }
    
    /**
     * Hủy task một cách an toàn
     * 
     * @param task Task cần hủy
     */
    public static void cancelTask(BukkitTask task) {
        if (task == null) {
            return;
        }
        
        try {
            if (!task.isCancelled()) {
                task.cancel();
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể hủy task: " + e.getMessage());
            }
        }
    }
    
    /**
     * Kiểm tra xem server có đang shutdown không
     * 
     * @return true nếu server đang shutdown
     */
    public static boolean isServerShuttingDown() {
        try {
            // Kiểm tra xem scheduler có còn hoạt động không
            if (Bukkit.getScheduler() == null) {
                return true;
            }

            // Thử kiểm tra server status bằng reflection
            try {
                java.lang.reflect.Method isRunningMethod = Bukkit.getServer().getClass().getMethod("isRunning");
                return !(Boolean) isRunningMethod.invoke(Bukkit.getServer());
            } catch (Exception e) {
                // Nếu không có method isRunning, giả định server đang chạy
                return false;
            }
        } catch (Exception e) {
            return true; // Giả định server đang shutdown nếu có lỗi
        }
    }
}
