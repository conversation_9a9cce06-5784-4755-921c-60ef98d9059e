package com.hongminh54.storage.Utils;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import com.hongminh54.storage.Storage;
import com.hongminh54.storage.compatibility.MaterialCompatibility;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * Utility class để xử lý player head/skull với skin texture
 * Tương thích với Minecraft 1.12.2 - 1.21.x
 */
public class SkullUtils {
    
    // Cache để tối ưu hiệu suất
    private static final Map<String, ItemStack> skullCache = new ConcurrentHashMap<>();
    private static final long CACHE_EXPIRE_TIME = 300000; // 5 phút
    private static final Map<String, Long> cacheTimestamps = new ConcurrentHashMap<>();
    
    /**
     * Tạo player head với skin texture
     * @param playerName Tên người chơi
     * @return ItemStack player head với skin
     */
    public static ItemStack createPlayerHead(String playerName) {
        return createPlayerHead(playerName, null, null);
    }
    
    /**
     * Tạo player head với skin texture và custom display name
     * @param playerName Tên người chơi
     * @param displayName Tên hiển thị (null để sử dụng tên mặc định)
     * @param lore Lore cho item (null để không có lore)
     * @return ItemStack player head với skin
     */
    public static ItemStack createPlayerHead(String playerName, String displayName, java.util.List<String> lore) {
        if (playerName == null || playerName.isEmpty()) {
            return createFallbackHead("Unknown", displayName, lore);
        }
        
        // Kiểm tra cache
        String cacheKey = playerName.toLowerCase();
        if (isValidCache(cacheKey)) {
            ItemStack cached = skullCache.get(cacheKey);
            if (cached != null) {
                ItemStack cloned = cached.clone();
                // Cập nhật display name và lore nếu được cung cấp
                if (displayName != null || lore != null) {
                    updateItemMeta(cloned, displayName != null ? displayName : playerName, lore);
                }
                return cloned;
            }
        }
        
        try {
            // Tạo skull item tương thích đa phiên bản
            ItemStack head = createSkullItem();
            
            // Thiết lập skin texture
            ItemMeta meta = head.getItemMeta();
            if (meta instanceof SkullMeta) {
                SkullMeta skullMeta = (SkullMeta) meta;
                setSkullOwner(skullMeta, playerName);
                
                // Thiết lập display name và lore
                String finalDisplayName = displayName != null ? displayName : playerName;
                updateItemMeta(head, finalDisplayName, lore);
                
                head.setItemMeta(skullMeta);
            }
            
            // Lưu vào cache
            skullCache.put(cacheKey, head.clone());
            cacheTimestamps.put(cacheKey, System.currentTimeMillis());
            
            return head;
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi tạo player head cho " + playerName + ": " + e.getMessage());
            return createFallbackHead(playerName, displayName, lore);
        }
    }
    
    /**
     * Tạo skull item tương thích đa phiên bản
     * @return ItemStack skull cơ bản
     */
    private static ItemStack createSkullItem() {
        if (MaterialCompatibility.isPre113()) {
            // Phiên bản 1.12.2 sử dụng SKULL_ITEM với data=3
            return new ItemStack(Material.valueOf("SKULL_ITEM"), 1, (byte) 3);
        } else {
            // Phiên bản 1.13+ sử dụng PLAYER_HEAD
            return new ItemStack(Material.valueOf("PLAYER_HEAD"), 1);
        }
    }
    
    /**
     * Thiết lập owner cho skull meta với tương thích đa phiên bản
     * @param skullMeta SkullMeta cần thiết lập
     * @param playerName Tên người chơi
     */
    private static void setSkullOwner(SkullMeta skullMeta, String playerName) {
        try {
            // Thử sử dụng setOwner() trước (tương thích với cả phiên bản cũ và mới)
            skullMeta.setOwner(playerName);
            
            // Kiểm tra xem có set thành công không
            String currentOwner = skullMeta.getOwner();
            if (currentOwner != null && currentOwner.equals(playerName)) {
                return; // Thành công
            }
            
            // Nếu setOwner() không hoạt động, thử setOwningPlayer() cho phiên bản mới
            try {
                OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(playerName);
                if (offlinePlayer != null) {
                    // Sử dụng reflection để gọi setOwningPlayer an toàn
                    java.lang.reflect.Method setOwningPlayerMethod = skullMeta.getClass()
                        .getMethod("setOwningPlayer", OfflinePlayer.class);
                    setOwningPlayerMethod.invoke(skullMeta, offlinePlayer);
                }
            } catch (Exception reflectionException) {
                // Bỏ qua lỗi reflection, có thể phiên bản không hỗ trợ
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().info("setOwningPlayer không khả dụng cho " + playerName);
                }
            }
        } catch (Exception e) {
            // Log lỗi nhưng không throw exception để không làm crash plugin
            Storage.getStorage().getLogger().warning("Không thể thiết lập owner cho skull " + playerName + ": " + e.getMessage());
        }
    }
    
    /**
     * Cập nhật ItemMeta với display name và lore
     * @param item ItemStack cần cập nhật
     * @param displayName Tên hiển thị
     * @param lore Lore
     */
    private static void updateItemMeta(ItemStack item, String displayName, java.util.List<String> lore) {
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            if (displayName != null) {
                meta.setDisplayName(Chat.colorize("&e" + displayName));
            }
            if (lore != null) {
                meta.setLore(lore);
            }
            item.setItemMeta(meta);
        }
    }
    
    /**
     * Tạo item dự phòng khi không thể tạo player head
     * @param playerName Tên người chơi
     * @param displayName Tên hiển thị
     * @param lore Lore
     * @return ItemStack dự phòng
     */
    private static ItemStack createFallbackHead(String playerName, String displayName, java.util.List<String> lore) {
        try {
            // Thử tạo skull cơ bản trước
            ItemStack fallback = createSkullItem();
            
            String finalDisplayName = displayName != null ? displayName : playerName;
            java.util.List<String> finalLore = lore != null ? lore : java.util.Collections.singletonList(Chat.colorize("&c(Không thể tải skin)"));
            
            updateItemMeta(fallback, finalDisplayName, finalLore);
            return fallback;
        } catch (Exception e) {
            // Nếu không thể tạo skull, sử dụng EMERALD làm item dự phòng cuối cùng
            ItemStack emeraldFallback = new ItemStack(Material.EMERALD);
            String finalDisplayName = displayName != null ? displayName : playerName;
            java.util.List<String> finalLore = lore != null ? lore : java.util.Collections.singletonList(Chat.colorize("&c(Lỗi tải đầu người chơi)"));
            
            updateItemMeta(emeraldFallback, finalDisplayName, finalLore);
            return emeraldFallback;
        }
    }
    
    /**
     * Kiểm tra cache có hợp lệ không
     * @param cacheKey Key của cache
     * @return true nếu cache hợp lệ
     */
    private static boolean isValidCache(String cacheKey) {
        if (!skullCache.containsKey(cacheKey) || !cacheTimestamps.containsKey(cacheKey)) {
            return false;
        }
        
        long timestamp = cacheTimestamps.get(cacheKey);
        return (System.currentTimeMillis() - timestamp) < CACHE_EXPIRE_TIME;
    }
    
    /**
     * Làm sạch cache cũ để tối ưu bộ nhớ
     */
    public static void cleanupCache() {
        long currentTime = System.currentTimeMillis();
        cacheTimestamps.entrySet().removeIf(entry -> {
            if ((currentTime - entry.getValue()) > CACHE_EXPIRE_TIME) {
                skullCache.remove(entry.getKey());
                return true;
            }
            return false;
        });
    }
    
    /**
     * Xóa toàn bộ cache
     */
    public static void clearCache() {
        skullCache.clear();
        cacheTimestamps.clear();
    }
}
