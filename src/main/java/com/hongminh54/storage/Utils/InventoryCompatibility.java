package com.hongminh54.storage.Utils;

import org.bukkit.inventory.Inventory;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.InventoryView;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;

/**
 * Lớp hỗ trợ tương thích InventoryView cho Minecraft 1.12.2 - 1.21.x
 * Xử lý vấn đề IncompatibleClassChangeError khi InventoryView thay đổi từ class sang interface
 */
public class InventoryCompatibility {
    
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_1_21_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(21);
    
    /**
     * Lấy top inventory một cách an toàn từ InventoryClickEvent
     * Xử lý tương thích gi<PERSON>a các phiên bản Minecraft
     * 
     * @param event InventoryClickEvent
     * @return Top inventory hoặc null nếu có lỗi
     */
    public static Inventory getTopInventorySafely(InventoryClickEvent event) {
        if (event == null) {
            return null;
        }
        
        try {
            // Phương pháp trực tiếp - hoạt động với hầu hết phiên bản
            InventoryView view = event.getView();
            return view.getTopInventory();
        } catch (IncompatibleClassChangeError e) {
            // Xử lý lỗi tương thích cho phiên bản 1.21+
            // IncompatibleClassChangeError: InventoryView thay đổi từ class sang interface
            // NoSuchMethodError cũng được bắt vì nó là subclass của IncompatibleClassChangeError
            return getTopInventoryWithReflection(event);
        } catch (Exception e) {
            // Xử lý các lỗi khác
            Storage.getStorage().getLogger().warning("Lỗi không xác định khi lấy top inventory: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Lấy bottom inventory một cách an toàn từ InventoryClickEvent
     *
     * @param event InventoryClickEvent
     * @return Bottom inventory hoặc null nếu có lỗi
     */
    public static Inventory getBottomInventorySafely(InventoryClickEvent event) {
        if (event == null) {
            return null;
        }

        try {
            InventoryView view = event.getView();
            return view.getBottomInventory();
        } catch (IncompatibleClassChangeError e) {
            // Xử lý lỗi tương thích cho phiên bản 1.21+
            return getBottomInventoryWithReflection(event);
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi không xác định khi lấy bottom inventory: " + e.getMessage());
            return null;
        }
    }

    /**
     * Lấy top inventory một cách an toàn từ InventoryDragEvent
     *
     * @param event InventoryDragEvent
     * @return Top inventory hoặc null nếu có lỗi
     */
    public static Inventory getTopInventorySafely(InventoryDragEvent event) {
        if (event == null) {
            return null;
        }

        try {
            InventoryView view = event.getView();
            return view.getTopInventory();
        } catch (IncompatibleClassChangeError e) {
            // Xử lý lỗi tương thích cho phiên bản 1.21+
            return getTopInventoryWithReflectionDrag(event);
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi không xác định khi lấy top inventory từ drag event: " + e.getMessage());
            return null;
        }
    }

    /**
     * Lấy inventory một cách an toàn từ InventoryCloseEvent
     *
     * @param event InventoryCloseEvent
     * @return Inventory hoặc null nếu có lỗi
     */
    public static Inventory getInventorySafely(InventoryCloseEvent event) {
        if (event == null) {
            return null;
        }

        try {
            return event.getInventory();
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi không xác định khi lấy inventory từ close event: " + e.getMessage());
            return null;
        }
    }

    /**
     * Kiểm tra xem clicked inventory có phải là top inventory không
     * 
     * @param event InventoryClickEvent
     * @return true nếu clicked inventory là top inventory
     */
    public static boolean isClickedInventoryTop(InventoryClickEvent event) {
        if (event == null) {
            return false;
        }
        
        try {
            Inventory topInventory = getTopInventorySafely(event);
            Inventory clickedInventory = event.getClickedInventory();
            
            if (topInventory == null || clickedInventory == null) {
                return false;
            }
            
            // So sánh trực tiếp
            return clickedInventory.equals(topInventory);
        } catch (Exception e) {
            // Fallback: so sánh bằng title và size
            return compareInventoriesByProperties(event);
        }
    }
    
    /**
     * Sử dụng reflection để lấy top inventory (fallback method)
     */
    private static Inventory getTopInventoryWithReflection(InventoryClickEvent event) {
        try {
            Object view = event.getView();
            java.lang.reflect.Method getTopMethod = view.getClass().getMethod("getTopInventory");
            return (Inventory) getTopMethod.invoke(view);
        } catch (Exception ex) {
            Storage.getStorage().getLogger().warning("Không thể lấy top inventory bằng reflection: " + ex.getMessage());
            return null;
        }
    }
    
    /**
     * Sử dụng reflection để lấy bottom inventory (fallback method)
     */
    private static Inventory getBottomInventoryWithReflection(InventoryClickEvent event) {
        try {
            Object view = event.getView();
            java.lang.reflect.Method getBottomMethod = view.getClass().getMethod("getBottomInventory");
            return (Inventory) getBottomMethod.invoke(view);
        } catch (Exception ex) {
            Storage.getStorage().getLogger().warning("Không thể lấy bottom inventory bằng reflection: " + ex.getMessage());
            return null;
        }
    }

    /**
     * Sử dụng reflection để lấy top inventory từ InventoryDragEvent (fallback method)
     */
    private static Inventory getTopInventoryWithReflectionDrag(InventoryDragEvent event) {
        try {
            Object view = event.getView();
            java.lang.reflect.Method getTopMethod = view.getClass().getMethod("getTopInventory");
            return (Inventory) getTopMethod.invoke(view);
        } catch (Exception ex) {
            Storage.getStorage().getLogger().warning("Không thể lấy top inventory từ drag event bằng reflection: " + ex.getMessage());
            return null;
        }
    }
    
    /**
     * So sánh inventory bằng cách kiểm tra properties (fallback method)
     */
    private static boolean compareInventoriesByProperties(InventoryClickEvent event) {
        try {
            Inventory topInventory = getTopInventorySafely(event);
            Inventory clickedInventory = event.getClickedInventory();
            
            if (topInventory == null || clickedInventory == null) {
                return false;
            }
            
            // So sánh size (không dùng title vì không có sẵn trong tất cả phiên bản)
            return topInventory.getSize() == clickedInventory.getSize();
        } catch (Exception e) {
            Storage.getStorage().getLogger().warning("Lỗi khi so sánh inventory properties: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Lấy title của inventory một cách an toàn bằng reflection
     *
     * @param inventory Inventory cần lấy title
     * @return Title của inventory hoặc empty string nếu có lỗi
     */
    public static String getInventoryTitleSafely(Inventory inventory) {
        if (inventory == null) {
            return "";
        }

        try {
            // Sử dụng reflection để lấy title vì method này không có trong tất cả phiên bản
            java.lang.reflect.Method getTitleMethod = inventory.getClass().getMethod("getTitle");
            Object title = getTitleMethod.invoke(inventory);
            return title != null ? title.toString() : "";
        } catch (Exception e) {
            // Một số phiên bản cũ có thể không hỗ trợ getTitle()
            return "";
        }
    }
}
