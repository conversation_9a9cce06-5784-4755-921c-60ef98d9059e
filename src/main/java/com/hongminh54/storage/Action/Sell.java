package com.hongminh54.storage.Action;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.Number;

public class Sell {
    private final Player p;
    private final String material;
    private final Integer amount;
    private final FileConfiguration config;

    public Sell(Player p, @NotNull String material, Integer amount) {
        this.p = p;
        String material_data = material.replace(":", ";");
        NMSAssistant nms = new NMSAssistant();
        if (nms.isVersionGreaterThanOrEqualTo(13)) {
            this.material = material_data.split(";")[0];
        } else {
            if (Number.getInteger(material_data.split(";")[1]) > 0) {
                this.material = material.replace(";", ":");
            } else {
                this.material = material_data.split(";")[0];
            }
        }
        this.amount = amount;
        config = File.getConfig();
    }

    public void doAction() {
        // Kiểm tra tính nhất quán dữ liệu trước khi thao tác
        if (!MineManager.ensureDataConsistency(p)) {
            // Nếu có vấn đề, tải lại dữ liệu
            MineManager.loadPlayerData(p);
        }

        try {
            int currentAmount = MineManager.getPlayerBlock(p, getMaterialData());
                                
            // Kiểm tra số lượng trong kho
            if (currentAmount <= 0) {
                String message = File.getMessage().getString("user.not_enough_items");
                if (message == null) {
                    message = "&cBạn không có đủ khoáng sản trong kho! Hiện có: &f<amount>";
                    Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.not_enough_items' trong file message.yml");
                }
                p.sendMessage(Chat.colorize(message.replace("<amount>", "0")));
                return;
            }

            // Xử lý bán theo số lượng cụ thể hoặc tất cả
            int sellAmount = (this.amount > 0) ? this.amount : currentAmount;

            // Kiểm tra nếu người chơi có đủ số lượng để bán
            if (currentAmount < sellAmount) {
                String message = File.getMessage().getString("user.not_enough_items");
                if (message == null) {
                    message = "&cBạn không có đủ khoáng sản trong kho! Hiện có: &f<amount>";
                    Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.not_enough_items' trong file message.yml");
                }
                p.sendMessage(Chat.colorize(message.replace("<amount>", String.valueOf(currentAmount))));
                return;
            }

            // Thực hiện bán khoáng sản đơn giản
            if (MineManager.removeBlockAmount(p, getMaterialData(), sellAmount)) {
                // Xử lý bán vật phẩm và nhận tiền
                ConfigurationSection section = config.getConfigurationSection("worth");
                if (section != null) {
                    List<String> sell_list = new ArrayList<>(section.getKeys(false));
                    if (sell_list.contains(getMaterialData())) {
                        double worth = section.getDouble(getMaterialData());
                        if (worth > 0) {
                            double money = worth * sellAmount;
                            String money_round_up = roundWithDecimalFormat(money);
                            double m_ru = Double.parseDouble(money_round_up);

                            // Ghi nhận thống kê bán
                            com.hongminh54.storage.Utils.StatsManager.recordSell(p, sellAmount);

                            // Lưu dữ liệu
                            MineManager.savePlayerData(p);

                            // Thực thi lệnh để người chơi nhận tiền
                            runCommand(m_ru);
                            
                            String sellMessage = File.getMessage().getString("user.action.sell.sell_item");
                            if (sellMessage == null) {
                                sellMessage = "&aĐã bán &f#amount# #material# &avới giá &f#money#&a. Hiện tại có &f#item_amount#&a/#max_storage#";
                                Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.action.sell.sell_item' trong file message.yml");
                            }
                            
                            String materialName = File.getConfig().getString("items." + getMaterialData());
                            if (materialName == null) {
                                materialName = getMaterialData();
                                Storage.getStorage().getLogger().warning("Thiếu chuỗi 'items." + getMaterialData() + "' trong file config.yml");
                            }
                            
                            int newAmount = MineManager.getPlayerBlock(p, getMaterialData());
                            p.sendMessage(Chat.colorize(sellMessage
                                    .replace("#amount#", String.valueOf(sellAmount))
                                    .replace("#material#", materialName)
                                    .replace("#player#", p.getName())
                                    .replace("#money#", String.valueOf(m_ru))
                                    .replace("#item_amount#", String.valueOf(newAmount))
                                    .replace("#max_storage#", String.valueOf(MineManager.getMaxBlock(p)))));
                        } else {
                            // Vật phẩm không thể bán - khôi phục
                            MineManager.addBlockAmount(p, getMaterialData(), sellAmount);
                            MineManager.savePlayerData(p);

                            String cannotSellMessage = File.getMessage().getString("user.action.sell.can_not_sell");
                            if (cannotSellMessage == null) {
                                cannotSellMessage = "&cKhông thể bán vật phẩm này!";
                                Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.action.sell.can_not_sell' trong file message.yml");
                            }
                            p.sendMessage(Chat.colorize(cannotSellMessage));
                        }
                    } else {
                        // Vật phẩm không có trong danh sách bán - khôi phục
                        MineManager.addBlockAmount(p, getMaterialData(), sellAmount);
                        MineManager.savePlayerData(p);

                        String cannotSellMessage = File.getMessage().getString("user.action.sell.can_not_sell");
                        if (cannotSellMessage == null) {
                            cannotSellMessage = "&cKhông thể bán vật phẩm này!";
                            Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.action.sell.can_not_sell' trong file message.yml");
                        }
                        p.sendMessage(Chat.colorize(cannotSellMessage));
                    }
                } else {
                    // Không có danh sách bán - khôi phục
                    MineManager.addBlockAmount(p, getMaterialData(), sellAmount);
                    MineManager.savePlayerData(p);

                    String cannotSellMessage = File.getMessage().getString("user.action.sell.can_not_sell");
                    if (cannotSellMessage == null) {
                        cannotSellMessage = "&cKhông thể bán vật phẩm này!";
                        Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.action.sell.can_not_sell' trong file message.yml");
                    }
                    p.sendMessage(Chat.colorize(cannotSellMessage));
                }
            } else {
                // Thông báo lỗi đơn giản
                String errorMessage = File.getMessage().getString("user.error");
                if (errorMessage == null) {
                    errorMessage = "&cĐã xảy ra lỗi khi xử lý yêu cầu của bạn.";
                }
                p.sendMessage(Chat.colorize(errorMessage));
            }
        } catch (Exception e) {
            // Xử lý ngoại lệ đơn giản
            Storage.getStorage().getLogger().severe("Lỗi khi bán khoáng sản: " + e.getMessage());

            String errorMessage = File.getMessage().getString("user.error");
            if (errorMessage == null) {
                errorMessage = "&cĐã xảy ra lỗi khi xử lý yêu cầu của bạn.";
            }
            p.sendMessage(Chat.colorize(errorMessage));
        }
    }

    public void runCommand(Double money) {
        config.getStringList("sell").forEach(cmd -> {
            String cmd_2 = cmd.replace("#money#", roundWithDecimalFormat(money))
                    .replace("#player#", p.getName());
            new BukkitRunnable() {
                @Override
                public void run() {
                    Storage.getStorage().getServer().dispatchCommand(Storage.getStorage().getServer().getConsoleSender(), cmd_2);
                }
            }.runTask(Storage.getStorage());
        });
    }

    public String roundWithDecimalFormat(double d) {
        String nf = File.getConfig().getString("number_format");
        DecimalFormat df;
        if (nf != null) {
            df = new DecimalFormat(nf);
        } else {
            df = new DecimalFormat("#.##");
        }
        return df.format(d);
    }

    public String getMaterialData() {
        if (!material.contains(";")) {
            return material + ";0";
        } else return material;
    }

    public Player getPlayer() {
        return p;
    }

    public String getMaterial() {
        return material;
    }

    public Integer getAmount() {
        return amount;
    }

    public FileConfiguration getConfig() {
        return config;
    }
}
