package com.hongminh54.storage.GUI.manager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.block.Action;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import com.hongminh54.storage.GUI.GUI;

import de.tr7zw.changeme.nbtapi.NBTItem;

public class InteractiveItem extends ItemStack {
    /**
     * The slot of the item in the GUI. Optional, but recommended. Default to -1.
     */
    private final int slot;

    private BiConsumer<Player, ClickType> clickCallback;
    private Consumer<Player> leftClickCallback;
    private Consumer<Player> rightClickCallback;

    public InteractiveItem(Material material, int slot, String displayName, String... lore) {
        super(material);

        this.slot = slot;

        ItemMeta meta = this.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(displayName);
            meta.setLore(Arrays.asList(lore));

            this.setItemMeta(meta);
        }

        createMapping();
    }

    public InteractiveItem(Material material, int slot) {
        super(material);

        this.slot = slot;

        createMapping();
    }

    public InteractiveItem(ItemStack itemStack, int slot) {
        super(itemStack);

        this.slot = slot;

        createMapping();
    }

    public InteractiveItem(ItemStack itemStack, int slot, boolean enchanted, List<String> actions, String id) {
        super(itemStack);

        this.slot = slot;
        
        if (enchanted) {
            // Sử dụng tương thích đa phiên bản cho enchantment
            Enchantment sharpness = com.hongminh54.storage.Utils.MaterialCompatibility.getCompatibleEnchantment("SHARPNESS", "DAMAGE_ALL");
            this.addUnsafeEnchantment(sharpness, 1);
            this.addItemFlags(ItemFlag.HIDE_ENCHANTS);
        }
        
        createMapping();
    }

    public InteractiveItem(Material material) {
        super(material);

        this.slot = -1;

        createMapping();
    }

    private void createMapping() {
        UUID uuid = UUID.randomUUID();
        GUI.getItemMapper().put(uuid, this);

        NBTItem nbtItem = new NBTItem(this);
        nbtItem.setUUID("storage:id", uuid);
        this.setItemMeta(nbtItem.getItem().getItemMeta());
    }

    public String getDisplayName() {
        ItemMeta meta = this.getItemMeta();

        if (meta != null) return meta.getDisplayName();

        return null;
    }

    public void setDisplayName(String displayName) {
        ItemMeta meta = this.getItemMeta();

        if (meta != null) {
            meta.setDisplayName(displayName);

            this.setItemMeta(meta);
        }
    }

    public List<String> getLore() {
        ItemMeta meta = this.getItemMeta();

        if (meta != null) {
            return meta.getLore();
        }

        return new ArrayList<>();
    }

    public void setLore(List<String> lore) {
        ItemMeta meta = this.getItemMeta();

        if (meta != null) {
            meta.setLore(lore);
            this.setItemMeta(meta);
        }
    }

    public void setLore(String... lore) {
        ItemMeta meta = this.getItemMeta();

        if (meta != null) {
            if (lore == null || lore.length == 0) {
                meta.setLore(null);
            } else {
                List<String> loreList = new ArrayList<>();

                for (String line : lore)
                    loreList.addAll(Arrays.asList(line.split("\n")));

                meta.setLore(loreList);
            }

            this.setItemMeta(meta);
        }
    }

    public void addItemFlags(ItemFlag... flags) {
        ItemMeta meta = this.getItemMeta();

        if (meta != null) {
            meta.addItemFlags(flags);
            this.setItemMeta(meta);
        }
    }

    public void removeItemFlags(ItemFlag... flags) {
        ItemMeta meta = this.getItemMeta();

        if (meta != null) {
            meta.removeItemFlags(flags);
            this.setItemMeta(meta);
        }
    }

    public Set<ItemFlag> getItemFlags() {
        ItemMeta meta = this.getItemMeta();

        return meta != null ? meta.getItemFlags() : new HashSet<>();
    }

    public void setGlow(boolean active) {
        Enchantment sharpness = com.hongminh54.storage.Utils.MaterialCompatibility.getCompatibleEnchantment("SHARPNESS", "DAMAGE_ALL");
        if (active) {
            this.addUnsafeEnchantment(sharpness, 1);
            this.addItemFlags(ItemFlag.HIDE_ENCHANTS);
        } else {
            this.removeEnchantment(sharpness);
            this.removeItemFlags(ItemFlag.HIDE_ENCHANTS);
        }
    }

    /**
     * Add enchantment glow effect to item
     */
    public void enchant() {
        Enchantment sharpness = com.hongminh54.storage.Utils.MaterialCompatibility.getCompatibleEnchantment("SHARPNESS", "DAMAGE_ALL");
        this.addUnsafeEnchantment(sharpness, 1);
        this.addItemFlags(ItemFlag.HIDE_ENCHANTS);
    }

    public void setSkullOwner(String owner) {
        ItemMeta meta = this.getItemMeta();

        if (!(meta instanceof SkullMeta)) return;

        try {
            SkullMeta skullMeta = (SkullMeta) meta;
            skullMeta.setOwner(owner);
            
            // Kiểm tra xem việc thiết lập chủ sở hữu có thành công không
            String currentOwner = skullMeta.getOwner();
            if (currentOwner == null || !currentOwner.equals(owner)) {
                // Có thể là phiên bản mới hơn, thử dùng phương thức khác
                try {
                    org.bukkit.OfflinePlayer offlinePlayer = org.bukkit.Bukkit.getOfflinePlayer(owner);
                    skullMeta.getClass().getMethod("setOwningPlayer", org.bukkit.OfflinePlayer.class)
                        .invoke(skullMeta, offlinePlayer);
                } catch (Exception ignored) {
                    // Bỏ qua nếu không có phương thức này
                }
            }
            
            this.setItemMeta(skullMeta);
        } catch (Exception e) {
            // Ghi log lỗi nếu cần
            com.hongminh54.storage.Storage.getStorage().getLogger()
                .warning("Không thể đặt chủ sở hữu đầu '" + owner + "': " + e.getMessage());
        }
    }

    //

    public InteractiveItem onClick(BiConsumer<Player, ClickType> consumer) {
        clickCallback = consumer;

        return this;
    }

    public InteractiveItem onLeftClick(Consumer<Player> consumer) {
        leftClickCallback = consumer;

        return this;
    }

    public InteractiveItem onRightClick(Consumer<Player> consumer) {
        rightClickCallback = consumer;

        return this;
    }

    // Handles InventoryClickEvent
    public void handleClick(Player player, ClickType clickType) {
        if ((clickType == ClickType.LEFT || clickType == ClickType.SHIFT_LEFT) && leftClickCallback != null)
            leftClickCallback.accept(player);
        else if ((clickType == ClickType.RIGHT || clickType == ClickType.SHIFT_RIGHT) && rightClickCallback != null)
            rightClickCallback.accept(player);

        if (clickCallback != null) clickCallback.accept(player, clickType);
    }

    // Handles PlayerInteractEvent
    public void handleClick(Player player, Action clickType) {
        if ((clickType == Action.LEFT_CLICK_AIR || clickType == Action.LEFT_CLICK_BLOCK) && leftClickCallback != null)
            leftClickCallback.accept(player);
        else if ((clickType == Action.RIGHT_CLICK_AIR || clickType == Action.RIGHT_CLICK_BLOCK) && rightClickCallback != null)
            rightClickCallback.accept(player);

        if (clickCallback != null)
            clickCallback.accept(player, clickType == Action.LEFT_CLICK_AIR || clickType == Action.LEFT_CLICK_BLOCK ? ClickType.LEFT : ClickType.RIGHT);
    }

    public int getSlot() {
        return slot;
    }

    /**
     * Get the actual ItemStack
     * @return ItemStack
     */
    public ItemStack getItem() {
        return this;
    }
}