package com.hongminh54.storage.NMS;

/**
 * Lớp hỗ trợ kiểm tra phiên bản NMS cho Minecraft 1.16.5 - 1.21.x
 */
public class NMSAssistant {

    /**
     * Method to get the NMS Version which stands for the current server-version.
     *
     * @return {@link NMSVersion}.
     */
    public NMSVersion getNMSVersion() {
        return new NMSVersion();
    }

    /**
     * Kiểm tra xem phiên bản máy chủ có lớn hơn phiên bản cho trước không.
     *
     * @param version phiên bản cần kiểm tra.
     * @return {@code true} nếu lớn hơn.
     */
    public boolean isVersionGreaterThan(int version) {
        return getNMSVersion().getMinor() > version;
    }

    /**
     * Kiểm tra xem phiên bản máy chủ có lớn hơn hoặc bằng phiên bản cho trước không.
     *
     * @param version phiên bản cần kiểm tra.
     * @return {@code true} nếu lớn hơn hoặc bằng.
     */
    public boolean isVersionGreaterThanOrEqualTo(int version) {
        return getNMSVersion().getMinor() >= version;
    }

    /**
     * Kiểm tra xem phiên bản máy chủ có nhỏ hơn phiên bản cho trước không.
     *
     * @param version phiên bản cần kiểm tra.
     * @return {@code true} nếu nhỏ hơn.
     */
    public boolean isVersionLessThan(int version) {
        return getNMSVersion().getMinor() < version;
    }

    /**
     * Kiểm tra xem phiên bản máy chủ có nhỏ hơn hoặc bằng phiên bản cho trước không.
     *
     * @param version phiên bản cần kiểm tra.
     * @return {@code true} nếu nhỏ hơn hoặc bằng.
     */
    public boolean isVersionLessThanOrEqualTo(int version) {
        return getNMSVersion().getMinor() <= version;
    }

    /**
     * Kiểm tra xem máy chủ có đang chạy phiên bản cụ thể này không.
     *
     * @param version phiên bản cần kiểm tra.
     * @return {@code true} nếu đúng.
     */
    public boolean isVersion(int version) {
        return getNMSVersion().getMinor() == version;
    }

    /**
     * Kiểm tra xem máy chủ có đang không chạy phiên bản cụ thể này không.
     *
     * @param version phiên bản cần kiểm tra.
     * @return {@code true} nếu không phải.
     */
    public boolean isNotVersion(int version) {
        return getNMSVersion().getMinor() != version;
    }
    
    /**
     * Kiểm tra xem phiên bản máy chủ có nằm trong khoảng 1.16.5 - 1.21.x không.
     *
     * @return {@code true} nếu phiên bản được hỗ trợ.
     */
    public boolean isSupportedVersion() {
        return getNMSVersion().isSupported();
    }
}