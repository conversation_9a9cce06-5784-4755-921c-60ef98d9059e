package com.hongminh54.storage.GUI;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;
import org.jetbrains.annotations.NotNull;

import com.cryptomorin.xseries.XEnchantment;
import com.cryptomorin.xseries.XMaterial;
import com.hongminh54.storage.GUI.manager.IGUI;
import com.hongminh54.storage.GUI.manager.InteractiveItem;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.AdvancedCompatibility;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.GUIText;
import com.hongminh54.storage.Utils.InventoryCompatibility;
import com.hongminh54.storage.Utils.LeaderboardManager;
import com.hongminh54.storage.Utils.LeaderboardManager.LeaderboardEntry;
import com.hongminh54.storage.Utils.MaterialCompatibility;
import com.hongminh54.storage.Utils.Number;
import com.hongminh54.storage.Utils.SoundManager;

public class LeaderboardGUI implements IGUI, Listener {
    private final Player p;
    private Inventory inventory;
    private final FileConfiguration fileConfig;
    private String currentType;
    private boolean listenerRegistered = false;

    private static final Map<String, Long> lastRefreshTimes = new HashMap<>();
    private static final long REFRESH_COOLDOWN = 2000; // 2 giây giữa các lần làm mới
    private static final int LEADERBOARD_DISPLAY_LIMIT = File.getConfig().getInt("settings.leaderboard_max_players", 20);
    
    // Cache player head để tăng hiệu suất
    private static final Map<String, ItemStack> playerHeadCache = new ConcurrentHashMap<>();
    private static final long PLAYER_HEAD_CACHE_DURATION = 1000 * 60 * 10; // 10 phút
    private static long lastPlayerHeadCacheCleanup = System.currentTimeMillis();

    public LeaderboardGUI(Player p) {
        this(p, LeaderboardManager.TYPE_MINED);
    }
    
    public LeaderboardGUI(Player p, String type) {
        this.p = p;
        this.fileConfig = File.getGUIConfig("leaderboard");
        this.currentType = type;
        
        // Tạo GUI với tối ưu hiệu suất
        createInventory();

        // Phát âm thanh mở GUI từ config
        playOpenSound();

        // Đăng ký Listener khi tạo GUI
        registerListener();
    }

    private void createInventory() {
        String title = GUIText.format(Objects.requireNonNull(fileConfig.getString("title"))
                .replace("#type#", LeaderboardManager.getTypeDisplayName(currentType)));
        int size = fileConfig.getInt("size") * 9;
        
        // Tạo inventory mới
        inventory = GUI.createInventory(p, size, title);
        
        // Lấy thời gian làm mới cuối cùng
        String playerKey = p.getName() + "_" + currentType;
        Long lastRefresh = lastRefreshTimes.get(playerKey);
        long currentTime = System.currentTimeMillis();
        
        // Nếu mới vừa làm mới gần đây, không cần truy vấn lại database
        if (lastRefresh != null && (currentTime - lastRefresh) < REFRESH_COOLDOWN) {
            // Chỉ hiển thị lại các item mà không truy vấn database
            fillItems(false);
        } else {
            // Cập nhật thời gian làm mới
            lastRefreshTimes.put(playerKey, currentTime);
            // Hiển thị với dữ liệu mới từ database
            fillItems(true);
        }
    }

    private void fillItems(boolean forceRefresh) {
        // Xóa các item cũ trong inventory
        inventory.clear();
        GUI.getInteractiveItems().clear();
        
        ConfigurationSection items = fileConfig.getConfigurationSection("items");
        if (items == null) return;

        // Lấy xếp hạng của người chơi hiện tại
        int playerRank = LeaderboardManager.getPlayerRank(p, currentType);

        for (String key : items.getKeys(false)) {
            ConfigurationSection item = items.getConfigurationSection(key);
            if (item == null) continue;

            // Xử lý trường hợp đặc biệt cho các mục xếp hạng
            if (key.equals("rank_item")) {
                // Hiển thị danh sách người chơi xếp hạng cao nhất
                // Giới hạn số lượng kết quả để tránh quá tải
                List<LeaderboardEntry> leaderboard;
                if (forceRefresh) {
                    // Chỉ lấy thông tin từ database khi cần làm mới
                    leaderboard = LeaderboardManager.getLeaderboard(currentType, LEADERBOARD_DISPLAY_LIMIT);
                } else {
                    // Sử dụng cache nếu không cần làm mới
                    leaderboard = LeaderboardManager.getCachedLeaderboard(currentType, LEADERBOARD_DISPLAY_LIMIT);
                }
                fillRankItems(item, leaderboard);
                continue;
            }
            
            // Xử lý các item thông thường
            Material material = XMaterial.matchXMaterial(item.getString("material")).get().parseMaterial();
            int amount = item.getInt("amount", 1);
            String slotStr = item.getString("slot");
            short data = (short) item.getInt("data", 0);
            
            // Xử lý nhiều slot
            List<Integer> slots = new ArrayList<>();
            if (slotStr != null && slotStr.contains(",")) {
                String[] slotArray = slotStr.split(",");
                for (String slot : slotArray) {
                    try {
                        slots.add(Integer.parseInt(slot.trim()));
                    } catch (NumberFormatException ignored) {
                        // Bỏ qua các giá trị không hợp lệ
                    }
                }
            } else {
                try {
                    slots.add(Integer.parseInt(slotStr));
                } catch (NumberFormatException ignored) {
                    // Bỏ qua nếu không phải số
                }
            }
            
            // Sử dụng GUIText để định dạng tên và mô tả
            String name = GUIText.format(item.getString("name", ""));
            List<String> lore = new ArrayList<>();

            for (String loreLine : item.getStringList("lore")) {
                // Thay thế placeholder
                loreLine = loreLine.replace("#player_rank#", playerRank > 0 ? String.valueOf(playerRank) : "Không xếp hạng")
                        .replace("#type#", LeaderboardManager.getTypeDisplayName(currentType));
                
                lore.add(GUIText.format(loreLine));
            }

            boolean enchanted = item.getBoolean("enchanted", false);
            boolean unbreakable = item.getBoolean("unbreakable", false);
            List<String> actions = item.getStringList("action");

            ItemStack itemStack = createCompatibleItemStack(material, amount, data);
            ItemMeta meta = itemStack.getItemMeta();
            
            if (meta != null) {
                meta.setDisplayName(name);
                meta.setLore(lore);
                
                // Thêm custom model data nếu được cấu hình và phiên bản hỗ trợ
                if (item.contains("custom-model-data") && !com.hongminh54.storage.Utils.MaterialCompatibility.isPre113()) {
                    int modelData = item.getInt("custom-model-data", -1);
                    if (modelData > 0) {
                        try {
                            meta.setCustomModelData(modelData);
                        } catch (NoSuchMethodError e) {
                            // Phiên bản không hỗ trợ CustomModelData
                        }
                    }
                }
                
                // Thêm các flags
                if (item.contains("flags")) {
                    ConfigurationSection flagsSection = item.getConfigurationSection("flags");
                    if (flagsSection != null) {
                        for (String flag_name : flagsSection.getKeys(false)) {
                            boolean apply = flagsSection.getBoolean(flag_name);
                            if (flag_name.equalsIgnoreCase("ALL") && apply) {
                                // Sử dụng tương thích đa phiên bản cho ItemFlag
                                ItemFlag potionFlag = com.hongminh54.storage.Utils.MaterialCompatibility.getCompatibleItemFlag("HIDE_ADDITIONAL_TOOLTIP", "HIDE_POTION_EFFECTS");
                                meta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE, ItemFlag.HIDE_ATTRIBUTES,
                                        ItemFlag.HIDE_DESTROYS, ItemFlag.HIDE_ENCHANTS,
                                        ItemFlag.HIDE_PLACED_ON, potionFlag);
                                break;
                            } else if (apply) {
                                try {
                                    meta.addItemFlags(ItemFlag.valueOf(flag_name));
                                } catch (IllegalArgumentException ignored) {
                                    // Bỏ qua flag không hợp lệ
                                }
                            }
                        }
                    }
                }

                // Xử lý enchantment
                if (enchanted) {
                    addGlowEffect(meta);
                }

                // Xử lý unbreakable (chỉ từ 1.13+)
                if (unbreakable && !MaterialCompatibility.isPre113()) {
                    try {
                        meta.setUnbreakable(true);
                    } catch (NoSuchMethodError e) {
                        // Phiên bản không hỗ trợ unbreakable
                    }
                }

                // Xử lý enchantments từ config
                processEnchantments(item, meta);

                itemStack.setItemMeta(meta);
            }

            // Tạo InteractiveItem và đặt vào tất cả các slot
            for (int slot : slots) {
                InteractiveItem interactiveItem = new InteractiveItem(itemStack, slot, enchanted, actions, key);
                if (enchanted) {
                    interactiveItem.enchant();
                }

                interactiveItem.onClick((player, clickType) -> {
                    // Phát âm thanh click từ config
                    playClickSound(player);

                    for (String action : actions) {
                        if (action.startsWith("[PLAYER_COMMAND]")) {
                            String command = action.substring("[PLAYER_COMMAND]".length()).trim();
                            player.closeInventory();
                            new BukkitRunnable() {
                                @Override
                                public void run() {
                                    player.performCommand(command);
                                }
                            }.runTask(Storage.getStorage());
                        } else if (action.startsWith("[CONSOLE_COMMAND]")) {
                            String command = action.substring("[CONSOLE_COMMAND]".length()).trim()
                                    .replace("{player}", player.getName());
                            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
                        } else if (action.startsWith("[CLOSE]")) {
                            player.closeInventory();
                        } else if (action.startsWith("[SWITCH_TYPE]")) {
                            String newType = action.substring("[SWITCH_TYPE]".length()).trim();
                            
                            // Chuyển đổi loại bảng xếp hạng
                            switch (newType) {
                                case "mined":
                                    currentType = LeaderboardManager.TYPE_MINED;
                                    break;
                                case "deposited":
                                    currentType = LeaderboardManager.TYPE_DEPOSITED;
                                    break;
                                case "withdrawn":
                                    currentType = LeaderboardManager.TYPE_WITHDRAWN;
                                    break;
                                case "sold":
                                    currentType = LeaderboardManager.TYPE_SOLD;
                                    break;
                            }
                            
                            // Tạo lại inventory với loại mới
                            String newTitle = GUIText.format(Objects.requireNonNull(fileConfig.getString("title"))
                                    .replace("#type#", LeaderboardManager.getTypeDisplayName(currentType)));
                            int newSize = fileConfig.getInt("size") * 9;
                            inventory = GUI.createInventory(p, newSize, newTitle);
                            fillItems(true);
                            player.openInventory(inventory);
                        }
                    }
                });

                inventory.setItem(slot, interactiveItem.getItem());
                GUI.getInteractiveItems().put(slot, interactiveItem);
            }
        }
    }
    
    /**
     * Hiển thị các mục xếp hạng
     */
    private void fillRankItems(ConfigurationSection section, List<LeaderboardEntry> leaderboard) {
        // Kiểm tra cấu hình có sử dụng đầu người chơi không
        boolean useSkull = section.getBoolean("use_player_head", false);
        
        // Sử dụng MaterialCompatibility để tương thích với 1.12.2
        Material material;
        if (useSkull) {
            if (com.hongminh54.storage.Utils.MaterialCompatibility.isPre113()) {
                material = Material.valueOf("SKULL_ITEM");
            } else {
                material = Material.valueOf("PLAYER_HEAD");
            }
        } else {
            material = XMaterial.matchXMaterial(section.getString("material", "PAPER")).get().parseMaterial();
        }
        
        int startSlot = section.getInt("start_slot");
        boolean enchanted = section.getBoolean("enchanted", false);
        
        String nameFormat = section.getString("name_format", "&f#rank#. &e#player#");
        List<String> loreFormat = section.getStringList("lore_format");
        
        // Hỗ trợ model data
        int customModelData = section.getInt("custom-model-data", -1);
        
        // Giới hạn số lượng người chơi hiển thị để tránh quá tải
        int displayLimit = Math.min(LEADERBOARD_DISPLAY_LIMIT, section.getInt("max_display", LEADERBOARD_DISPLAY_LIMIT));
        
        // Hiển thị thông báo "Đang tải..." nếu không có dữ liệu
        if (leaderboard.isEmpty()) {
            ItemStack loadingItem = new ItemStack(material);
            ItemMeta meta = loadingItem.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(GUIText.format("&eĐang tải dữ liệu..."));
                meta.setLore(Collections.singletonList(GUIText.format("&7Vui lòng đợi trong giây lát")));
                
                // Thêm custom model data nếu được cấu hình và phiên bản Minecraft hỗ trợ
                if (customModelData > 0 && !com.hongminh54.storage.Utils.MaterialCompatibility.isPre113()) {
                    try {
                        meta.setCustomModelData(customModelData);
                    } catch (NoSuchMethodError e) {
                        // Phiên bản Minecraft không hỗ trợ CustomModelData
                    }
                }
                
                loadingItem.setItemMeta(meta);
            }
            
            // Chỉ hiển thị một vài item "Đang tải..." để không tạo quá nhiều item
            for (int i = 0; i < Math.min(5, displayLimit); i++) {
                inventory.setItem(startSlot + i, loadingItem);
            }
            return;
        }
        
        // Xử lý flags
        List<ItemFlag> flags = new ArrayList<>();
        if (section.contains("flags")) {
            ConfigurationSection flagsSection = section.getConfigurationSection("flags");
            if (flagsSection != null) {
                for (String flag_name : flagsSection.getKeys(false)) {
                    boolean apply = flagsSection.getBoolean(flag_name);
                    if (flag_name.equalsIgnoreCase("ALL") && apply) {
                        try {
                            flags.add(ItemFlag.HIDE_ATTRIBUTES);
                            flags.add(ItemFlag.HIDE_DESTROYS);
                            flags.add(ItemFlag.HIDE_ENCHANTS);
                            flags.add(ItemFlag.HIDE_PLACED_ON);
                            // Sử dụng tương thích đa phiên bản cho ItemFlag
                            ItemFlag potionFlag = com.hongminh54.storage.Utils.MaterialCompatibility.getCompatibleItemFlag("HIDE_ADDITIONAL_TOOLTIP", "HIDE_POTION_EFFECTS");
                            flags.add(potionFlag);
                            
                            // HIDE_UNBREAKABLE chỉ có trong 1.13+
                            if (!com.hongminh54.storage.Utils.MaterialCompatibility.isPre113()) {
                                flags.add(ItemFlag.HIDE_UNBREAKABLE);
                            }
                        } catch (Exception e) {
                            // Bỏ qua lỗi với phiên bản cũ
                        }
                        break;
                    } else if (apply) {
                        try {
                            flags.add(ItemFlag.valueOf(flag_name));
                        } catch (IllegalArgumentException ignored) {
                            // Bỏ qua flag không hợp lệ
                        }
                    }
                }
            }
        }
        
        // Kiểm tra nếu có cấu hình model data riêng cho từng hạng
        ConfigurationSection rankModelDataSection = section.getConfigurationSection("rank-model-data");
        Map<Integer, Integer> rankModelData = new HashMap<>();
        if (rankModelDataSection != null) {
            for (String rankKey : rankModelDataSection.getKeys(false)) {
                try {
                    int rank = Integer.parseInt(rankKey);
                    int modelData = rankModelDataSection.getInt(rankKey);
                    rankModelData.put(rank, modelData);
                } catch (NumberFormatException ignored) {
                    // Bỏ qua nếu không phải số
                }
            }
        }
        
        // Hiển thị danh sách xếp hạng
        for (int i = 0; i < leaderboard.size() && i < displayLimit; i++) {
            LeaderboardEntry entry = leaderboard.get(i);
            int rank = i + 1;
            
            // Tính slot một cách thông minh để tránh đè lên viền
            int slot;
            
            // Sửa lại cách tính slot để hiển thị tất cả 20 người chơi
            // Hàng 1: slot 10-16 (7 người)
            // Hàng 2: slot 19-25 (7 người)
            // Hàng 3: slot 28-34 (7 người)
            if (i < 7) {
                // Đặt vào hàng đầu tiên (slot 10-16)
                slot = startSlot + i;
            } else if (i < 14) {
                // Đặt vào hàng thứ hai (slot 19-25)
                slot = startSlot + 9 + (i - 7);
            } else if (i < 21) {
                // Đặt vào hàng thứ ba (slot 28-34)
                slot = startSlot + 18 + (i - 14);
            } else {
                // Thêm hàng thứ tư nếu cần thiết
                slot = startSlot + 27 + (i - 21);
            }
            
            ItemStack itemStack;
            
            // Tạo item dựa trên loại (player head hoặc item thông thường)
            if (useSkull) {
                // Tạo player head
                itemStack = createPlayerHead(entry.getPlayerName());
            } else {
                // Tạo item thông thường
                itemStack = new ItemStack(material);
            }
            
            ItemMeta meta = itemStack.getItemMeta();
            
            if (meta != null) {
                // Tô màu cho item theo hạng
                String rankColor = "&f";
                if (rank == 1) rankColor = "&e"; // Hạng 1: Vàng
                else if (rank == 2) rankColor = "&7"; // Hạng 2: Bạc
                else if (rank == 3) rankColor = "&6"; // Hạng 3: Đồng
                
                // Đặt tên và mô tả cho item
                String name = nameFormat.replace("#rank#", rankColor + rank)
                        .replace("#player#", entry.getDisplayName());
                meta.setDisplayName(GUIText.format(name));
                
                List<String> lore = new ArrayList<>();
                for (String loreLine : loreFormat) {
                    // Sử dụng định dạng số mới
                    loreLine = loreLine.replace("#value#", Number.formatCompact(entry.getValue()))
                            .replace("#type#", LeaderboardManager.getTypeDisplayName(currentType));
                    lore.add(GUIText.format(loreLine));
                }
                meta.setLore(lore);
                
                // Thêm custom model data dựa trên thứ hạng hoặc model data mặc định
                if (!com.hongminh54.storage.Utils.MaterialCompatibility.isPre113()) {
                    try {
                        // Kiểm tra xem có cấu hình model data riêng cho thứ hạng này không
                        if (rankModelData.containsKey(rank)) {
                            meta.setCustomModelData(rankModelData.get(rank));
                        } 
                        // Nếu không có model data riêng cho hạng, sử dụng model data chung
                        else if (customModelData > 0) {
                            meta.setCustomModelData(customModelData);
                        }
                    } catch (NoSuchMethodError e) {
                        // Phiên bản Minecraft không hỗ trợ CustomModelData
                    }
                }
                
                // Thêm flags
                for (ItemFlag flag : flags) {
                    try {
                        meta.addItemFlags(flag);
                    } catch (Exception ex) {
                        // Bỏ qua flags không hỗ trợ trong phiên bản cũ
                    }
                }
                
                itemStack.setItemMeta(meta);
            }
            
            // Thêm vào GUI
            inventory.setItem(slot, itemStack);
            
            // Tạo InteractiveItem nếu cần enchanted effect hoặc action nào đó
            if (enchanted) {
                List<String> actions = new ArrayList<>();
                InteractiveItem interactiveItem = new InteractiveItem(itemStack, slot, enchanted, actions, "rank_" + rank);
                GUI.getInteractiveItems().put(slot, interactiveItem);
            }
        }
        
        // Nếu không có người chơi trong bảng xếp hạng, hiển thị thông báo
        if (leaderboard.isEmpty()) {
            ItemStack emptyItem = new ItemStack(Material.PAPER);
            ItemMeta meta = emptyItem.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(Chat.colorizewp("&cKhông có dữ liệu"));
                List<String> lore = new ArrayList<>();
                lore.add(Chat.colorizewp("&7Chưa có người chơi nào trong bảng xếp hạng này"));
                lore.add(Chat.colorizewp("&7Hãy là người đầu tiên xuất hiện ở đây!"));
                
                // Thêm custom model data nếu được cấu hình và phiên bản Minecraft hỗ trợ
                if (customModelData > 0 && !com.hongminh54.storage.Utils.MaterialCompatibility.isPre113()) {
                    try {
                        meta.setCustomModelData(customModelData);
                    } catch (NoSuchMethodError e) {
                        // Phiên bản Minecraft không hỗ trợ CustomModelData
                    }
                }
                
                meta.setLore(lore);
                emptyItem.setItemMeta(meta);
            }
            
            // Hiển thị thông báo ở các slot đầu tiên của mỗi hàng để dễ nhìn
            int[] emptySlots = {startSlot, startSlot + 9, startSlot + 18};
            for (int emptySlot : emptySlots) {
                inventory.setItem(emptySlot, emptyItem);
            }
        }
    }

    /**
     * Tạo đầu người chơi (player head) với tên người chơi và skin texture
     * Sử dụng SkullUtils để xử lý tương thích đa phiên bản và cache
     */
    private ItemStack createPlayerHead(String playerName) {
        // Kiểm tra và làm sạch cache nếu cần
        cleanupPlayerHeadCache();

        // Kiểm tra tham số đầu vào để tránh NullPointerException
        if (playerName == null || playerName.isEmpty()) {
            return createFallbackHead("Unknown");
        }

        // Kiểm tra cache local trước
        String cacheKey = playerName.toLowerCase();
        if (playerHeadCache.containsKey(cacheKey)) {
            return playerHeadCache.get(cacheKey);
        }

        try {
            // Sử dụng AdvancedCompatibility để tạo player head an toàn
            ItemStack head = AdvancedCompatibility.createPlayerHead(playerName);

            if (head != null) {
                // Thêm lore cho head
                ItemMeta meta = head.getItemMeta();
                if (meta != null) {
                    List<String> lore = new ArrayList<>();
                    lore.add(GUIText.format("&7Người chơi: &f" + playerName));
                    lore.add(GUIText.format("&8Skin được tải từ Mojang"));
                    meta.setLore(lore);
                    head.setItemMeta(meta);
                }

                // Lưu vào cache local
                playerHeadCache.put(cacheKey, head);
                return head;
            } else {
                // Fallback nếu AdvancedCompatibility trả về null
                return createFallbackHead(playerName);
            }
        } catch (Exception e) {
            // Xử lý lỗi và trả về item dự phòng
            Storage.getStorage().getLogger().warning("Lỗi khi tạo đầu người chơi cho " + playerName + ": " + e.getMessage());
            return createFallbackHead(playerName);
        }
    }
    
    /**
     * Tạo item dự phòng khi không thể tạo đầu người chơi
     * @param playerName Tên người chơi
     * @return ItemStack dự phòng
     */
    private ItemStack createFallbackHead(String playerName) {
        // Sử dụng EMERALD làm item dự phòng
        ItemStack fallback = new ItemStack(Material.EMERALD);
        ItemMeta meta = fallback.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(GUIText.format("&e" + playerName));
            List<String> lore = new ArrayList<>();
            lore.add(GUIText.format("&7Người chơi: &f" + playerName));
            lore.add(GUIText.format("&c(Không thể tải đầu người chơi)"));
            meta.setLore(lore);
            fallback.setItemMeta(meta);
        }

        // Lưu vào cache và trả về
        playerHeadCache.put(playerName.toLowerCase(), fallback);
        return fallback;
    }
    
    /**
     * Làm sạch cache đầu người chơi để tránh rò rỉ bộ nhớ
     * Chỉ chạy sau một khoảng thời gian nhất định
     */
    private static void cleanupPlayerHeadCache() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastPlayerHeadCacheCleanup > PLAYER_HEAD_CACHE_DURATION) {
            playerHeadCache.clear();
            lastPlayerHeadCacheCleanup = currentTime;

            // Không cần cleanup SkullUtils cache vì đã chuyển sang AdvancedCompatibility
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().info("Đã làm sạch player head cache trong LeaderboardGUI");
            }
        }
    }

    /**
     * Đăng ký listener cho GUI
     */
    private void registerListener() {
        if (!listenerRegistered) {
            Bukkit.getPluginManager().registerEvents(this, Storage.getStorage());
            listenerRegistered = true;
        }
    }
    
    /**
     * Hủy đăng ký listener
     */
    private void unregisterListener() {
        if (listenerRegistered) {
            HandlerList.unregisterAll(this);
            listenerRegistered = false;
        }
    }

    @Override
    public @NotNull Inventory getInventory() {
        return inventory;
    }
    
    /**
     * Xử lý sự kiện click vào inventory để ngăn lấy vật phẩm
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        // Sử dụng InventoryCompatibility để lấy top inventory một cách an toàn
        Inventory topInventory = InventoryCompatibility.getTopInventorySafely(event);
        if (topInventory == null || !topInventory.equals(inventory)) {
            return;
        }

        event.setCancelled(true);

        // Xử lý hành động click thông qua InteractiveItem
        int slot = event.getRawSlot();
        if (GUI.getInteractiveItems().containsKey(slot)) {
            InteractiveItem item = GUI.getInteractiveItems().get(slot);
            item.handleClick((Player) event.getWhoClicked(), event.getClick());
        }
    }
    
    /**
     * Xử lý sự kiện đóng inventory
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getInventory().equals(inventory)) {
            // Hủy đăng ký listener khi đóng giao diện
            new BukkitRunnable() {
                @Override
                public void run() {
                    unregisterListener();
                }
            }.runTaskLater(Storage.getStorage(), 1L);
        }
    }

    /**
     * Phát âm thanh mở GUI từ config
     */
    private void playOpenSound() {
        try {
            String openSound = fileConfig.getString("sounds.open", "BLOCK_NOTE_BLOCK_PLING:1.0:1.5");
            SoundManager.playSoundFromConfig(p, openSound);
        } catch (Exception e) {
            // Fallback âm thanh
            try {
                String fallbackSound = fileConfig.getString("sounds.open_fallback", "BLOCK_CHEST_OPEN:0.5:1.0");
                SoundManager.playSoundFromConfig(p, fallbackSound);
            } catch (Exception ex) {
                // Bỏ qua lỗi âm thanh
            }
        }
    }

    /**
     * Phát âm thanh click từ config
     */
    private void playClickSound(Player player) {
        try {
            String clickSound = fileConfig.getString("sounds.click", "UI_BUTTON_CLICK:0.5:1.0");
            SoundManager.playSoundFromConfig(player, clickSound);
        } catch (Exception e) {
            // Bỏ qua lỗi âm thanh
        }
    }

    /**
     * Tạo ItemStack tương thích đa phiên bản
     */
    private ItemStack createCompatibleItemStack(Material material, int amount, short data) {
        try {
            if (MaterialCompatibility.isPre113()) {
                return new ItemStack(material, amount, data);
            } else {
                return new ItemStack(material, amount);
            }
        } catch (Exception e) {
            // Fallback
            return new ItemStack(Material.PAPER, amount);
        }
    }

    /**
     * Thêm hiệu ứng phát sáng cho item
     */
    private void addGlowEffect(ItemMeta meta) {
        try {
            org.bukkit.enchantments.Enchantment glowEnchant = MaterialCompatibility.getCompatibleEnchantment("UNBREAKING", "DURABILITY");
            meta.addEnchant(glowEnchant, 1, true);
            meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
        } catch (Exception e) {
            // Bỏ qua lỗi enchantment
        }
    }

    /**
     * Xử lý enchantments từ config
     */
    private void processEnchantments(ConfigurationSection item, ItemMeta meta) {
        if (item.contains("enchants")) {
            ConfigurationSection enchantsSection = item.getConfigurationSection("enchants");
            if (enchantsSection != null) {
                for (String enchantName : enchantsSection.getKeys(false)) {
                    try {
                        int level = enchantsSection.getInt(enchantName, 1);
                        Optional<XEnchantment> enchantment = XEnchantment.matchXEnchantment(enchantName);
                        if (enchantment.isPresent() && enchantment.get().getEnchant() != null) {
                            meta.addEnchant(enchantment.get().getEnchant(), level, true);
                        }
                    } catch (Exception e) {
                        // Bỏ qua enchantment không hợp lệ
                    }
                }
            }
        }
    }

    /**
     * Xóa toàn bộ cache đầu người chơi
     * Có thể được gọi khi plugin vô hiệu hóa
     */
    public static void clearHeadCache() {
        playerHeadCache.clear();
        lastPlayerHeadCacheCleanup = System.currentTimeMillis();
    }
}