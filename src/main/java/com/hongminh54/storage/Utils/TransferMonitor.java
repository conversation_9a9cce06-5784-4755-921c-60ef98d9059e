package com.hongminh54.storage.Utils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitTask;

import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;

/**
 * Theo dõi và giám sát các hoạt động chuyển kho.
 * Lớp này giúp phát hiện các vấn đề không nhất quán dữ liệu và cố gắng khắc phục chúng.
 */
public class TransferMonitor {
    private static final Map<String, TransferSession> activeSessions = new ConcurrentHashMap<>();
    private static final Map<String, List<String>> transferErrors = new ConcurrentHashMap<>();
    private static final SimpleDateFormat LOG_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    // Thiết lập thời gian sống cho các phiên chuyển kho (mili giây)
    private static final long SESSION_TIMEOUT = 300000; // 5 phút
    
    // Nhiệm vụ định kỳ để dọn dẹp các phiên hết hạn
    private static BukkitTask cleanupTask;
    
    /**
     * Khởi tạo và thiết lập hệ thống giám sát chuyển kho
     */
    public static void initialize() {
        // Lên lịch dọn dẹp các phiên chuyển kho hết hạn
        scheduleCleanup();
        
        Storage.getStorage().getLogger().info("Hệ thống giám sát chuyển kho đã khởi tạo");
    }
    
    /**
     * Bắt đầu một phiên chuyển mới
     * @param sender Người gửi
     * @param receiver Người nhận
     * @param resources Tài nguyên cần chuyển
     * @return ID của phiên chuyển
     */
    public static String startTransferSession(Player sender, Player receiver, Map<String, Integer> resources) {
        if (sender == null || receiver == null || resources == null || resources.isEmpty()) {
            return null;
        }
        
        String sessionId = "transfer_" + sender.getName() + "_to_" + receiver.getName() + "_" + System.currentTimeMillis();
        
        // Lưu trữ dữ liệu ban đầu
        Map<String, Integer> senderOriginalData = new HashMap<>();
        Map<String, Integer> receiverOriginalData = new HashMap<>();
        
        for (String material : resources.keySet()) {
            senderOriginalData.put(material, MineManager.getPlayerBlock(sender, material));
            receiverOriginalData.put(material, MineManager.getPlayerBlock(receiver, material));
        }
        
        // Tạo và lưu phiên chuyển mới
        TransferSession session = new TransferSession(
            sessionId,
            sender.getName(),
            receiver.getName(),
            new HashMap<>(resources),
            senderOriginalData,
            receiverOriginalData,
            System.currentTimeMillis()
        );
        
        activeSessions.put(sessionId, session);
        
        Storage.getStorage().getLogger().info("Phiên chuyển kho mới: " + sessionId);
        return sessionId;
    }
    
    /**
     * Kết thúc một phiên chuyển và kiểm tra kết quả
     * @param sessionId ID phiên
     * @param transferredResources Các tài nguyên đã chuyển thành công
     * @return true nếu kết thúc thành công
     */
    public static boolean endTransferSession(String sessionId, Map<String, Integer> transferredResources) {
        TransferSession session = activeSessions.get(sessionId);
        if (session == null) {
            Storage.getStorage().getLogger().warning("Không tìm thấy phiên chuyển kho: " + sessionId);
            return false;
        }
        
        try {
            // Đánh dấu phiên đã hoàn thành
            session.setCompleted(true);
            session.setTransferredResources(transferredResources);
            session.setEndTime(System.currentTimeMillis());
            
            // Kiểm tra tính nhất quán của dữ liệu
            boolean isConsistent = verifyTransferConsistency(session);
            
            if (!isConsistent) {
                handleInconsistentTransfer(session);
                Storage.getStorage().getLogger().warning("Phát hiện không nhất quán dữ liệu trong phiên " + sessionId);
                return false;
            }
            
            Storage.getStorage().getLogger().info("Phiên chuyển kho " + sessionId + " kết thúc thành công");
            return true;
        } catch (Exception e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi kết thúc phiên chuyển kho: " + e.getMessage(), e);
            logTransferError(sessionId, "exception_end_session", e.getMessage());
            return false;
        }
    }
    
    /**
     * Kiểm tra tính nhất quán dữ liệu sau khi chuyển kho
     * @param session Phiên chuyển kho
     * @return true nếu dữ liệu nhất quán
     */
    private static boolean verifyTransferConsistency(TransferSession session) {
        try {
            Player sender = Bukkit.getPlayer(session.getSenderName());
            Player receiver = Bukkit.getPlayer(session.getReceiverName());
            
            // Nếu cả hai người chơi không còn online, không thể kiểm tra
            if (sender == null && receiver == null) {
                logTransferError(session.getSessionId(), "players_offline", "Cả hai người chơi đã offline");
                return false;
            }
            
            Map<String, Integer> transferredResources = session.getTransferredResources();
            boolean isConsistent = true;
            
            // Kiểm tra tính nhất quán cho từng tài nguyên
            for (Map.Entry<String, Integer> entry : transferredResources.entrySet()) {
                String material = entry.getKey();
                int transferAmount = entry.getValue();
                
                // Kỳ vọng dữ liệu sau khi chuyển
                int expectedSenderAmount = Math.max(0, session.getSenderOriginalData().getOrDefault(material, 0) - transferAmount);
                int expectedReceiverAmount = session.getReceiverOriginalData().getOrDefault(material, 0) + transferAmount;
                
                // Dữ liệu thực tế
                int actualSenderAmount = (sender != null) ? MineManager.getPlayerBlock(sender, material) : -1;
                int actualReceiverAmount = (receiver != null) ? MineManager.getPlayerBlock(receiver, material) : -1;
                
                // Kiểm tra nếu người chơi online
                if (sender != null && actualSenderAmount != expectedSenderAmount) {
                    isConsistent = false;
                    logTransferError(session.getSessionId(), "sender_inconsistency", 
                        material + ": expected=" + expectedSenderAmount + ", actual=" + actualSenderAmount);
                }
                
                if (receiver != null && actualReceiverAmount != expectedReceiverAmount) {
                    isConsistent = false;
                    logTransferError(session.getSessionId(), "receiver_inconsistency", 
                        material + ": expected=" + expectedReceiverAmount + ", actual=" + actualReceiverAmount);
                }
            }
            
            // Ghi nhật ký kết quả kiểm tra
            if (isConsistent) {
                Storage.getStorage().getLogger().info("Phiên " + session.getSessionId() + " dữ liệu nhất quán");
            } else {
                Storage.getStorage().getLogger().warning("Phiên " + session.getSessionId() + " phát hiện không nhất quán dữ liệu");
                // Lưu lỗi vào cơ sở dữ liệu
                recordTransferError(session);
            }
            
            return isConsistent;
        } catch (Exception e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi kiểm tra tính nhất quán: " + e.getMessage(), e);
            logTransferError(session.getSessionId(), "exception_verify", e.getMessage());
            return false;
        }
    }
    
    /**
     * Xử lý khi phát hiện dữ liệu không nhất quán
     * @param session Phiên chuyển kho
     */
    private static void handleInconsistentTransfer(TransferSession session) {
        try {
            Player sender = Bukkit.getPlayer(session.getSenderName());
            Player receiver = Bukkit.getPlayer(session.getReceiverName());
            
            // Ghi log sự kiện
            Storage.getStorage().getLogger().warning("Đang sửa chữa dữ liệu không nhất quán cho phiên " + session.getSessionId());
            
            // Sửa chữa dữ liệu nếu người chơi vẫn online
            if (sender != null) {
                fixPlayerData(sender, session, true);
            }
            
            if (receiver != null) {
                fixPlayerData(receiver, session, false);
            }
            
            // Ghi log hoàn thành
            Storage.getStorage().getLogger().info("Đã hoàn thành sửa chữa dữ liệu cho phiên " + session.getSessionId());
        } catch (Exception e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi sửa chữa dữ liệu không nhất quán: " + e.getMessage(), e);
            logTransferError(session.getSessionId(), "exception_fixing", e.getMessage());
        }
    }
    
    /**
     * Sửa chữa dữ liệu của người chơi sau khi phát hiện không nhất quán
     * @param player Người chơi cần sửa chữa
     * @param session Phiên chuyển kho
     * @param isSender true nếu người chơi là người gửi
     */
    private static void fixPlayerData(Player player, TransferSession session, boolean isSender) {
        Map<String, Integer> transferredResources = session.getTransferredResources();
        
        // Cho mỗi tài nguyên đã chuyển
        for (Map.Entry<String, Integer> entry : transferredResources.entrySet()) {
            String material = entry.getKey();
            int transferAmount = entry.getValue();
            
            // Lấy giá trị dự kiến và thực tế
            int expectedAmount;
            if (isSender) {
                expectedAmount = Math.max(0, session.getSenderOriginalData().getOrDefault(material, 0) - transferAmount);
            } else {
                expectedAmount = session.getReceiverOriginalData().getOrDefault(material, 0) + transferAmount;
            }
            
            int actualAmount = MineManager.getPlayerBlock(player, material);
            
            // Sửa chữa nếu có khác biệt
            if (actualAmount != expectedAmount) {
                Storage.getStorage().getLogger().info(
                    "Đang sửa dữ liệu cho " + player.getName() + 
                    ", " + material + ": " + actualAmount + " -> " + expectedAmount
                );
                
                // Đặt giá trị đúng
                MineManager.setBlock(player, material, expectedAmount);
            }
        }
        
        // Lưu dữ liệu đã sửa
        try {
            MineManager.savePlayerData(player);
        } catch (Exception e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, 
                "Lỗi khi lưu dữ liệu đã sửa cho " + player.getName() + ": " + e.getMessage(), e);
        }
    }
    
    /**
     * Lưu lỗi chuyển kho vào cơ sở dữ liệu
     * @param session Phiên chuyển kho
     */
    private static void recordTransferError(TransferSession session) {
        String playerInfo = session.getSenderName() + " -> " + session.getReceiverName();
        String errorInfo = "Session: " + session.getSessionId() + 
            ", Materials: " + session.getTransferredResources().toString();
        
        // Sử dụng ErrorLogger để ghi lỗi
        ErrorLogger.logError(playerInfo, "transfer_inconsistency", errorInfo);
    }
    
    /**
     * Ghi log lỗi trong quá trình chuyển kho
     * @param sessionId ID phiên
     * @param errorType Loại lỗi
     * @param message Thông báo lỗi
     */
    private static void logTransferError(String sessionId, String errorType, String message) {
        String timestamp = LOG_DATE_FORMAT.format(new Date());
        String errorLog = timestamp + " [" + errorType + "] " + message;
        
        // Lưu lỗi vào bộ nhớ
        transferErrors.computeIfAbsent(sessionId, k -> new ArrayList<>()).add(errorLog);
        
        // Ghi log
        Storage.getStorage().getLogger().warning("Lỗi chuyển kho [" + sessionId + "]: " + errorLog);
    }
    
    /**
     * Lên lịch dọn dẹp các phiên chuyển kho hết hạn
     */
    private static void scheduleCleanup() {
        // Hủy nhiệm vụ hiện tại nếu có
        if (cleanupTask != null && !cleanupTask.isCancelled()) {
            cleanupTask.cancel();
        }
        
        // Lên lịch dọn dẹp mỗi 15 phút
        cleanupTask = Bukkit.getScheduler().runTaskTimerAsynchronously(
            Storage.getStorage(),
            () -> cleanupExpiredSessions(),
            20 * 60 * 5, // Chạy sau 5 phút
            20 * 60 * 15  // Lặp lại mỗi 15 phút
        );
    }
    
    /**
     * Dọn dẹp các phiên chuyển kho hết hạn
     */
    private static void cleanupExpiredSessions() {
        long now = System.currentTimeMillis();
        int expiredCount = 0;
        
        // Kiểm tra và xóa các phiên hết hạn
        for (Iterator<Map.Entry<String, TransferSession>> it = activeSessions.entrySet().iterator(); it.hasNext();) {
            Map.Entry<String, TransferSession> entry = it.next();
            TransferSession session = entry.getValue();
            
            // Nếu phiên đã hoàn thành và quá thời gian sống hoặc chưa hoàn thành nhưng đã quá hạn
            boolean isExpired = (session.isCompleted() && now - session.getEndTime() > SESSION_TIMEOUT)
                || (!session.isCompleted() && now - session.getStartTime() > SESSION_TIMEOUT * 2);
            
            if (isExpired) {
                // Lưu lại lỗi nếu phiên không hoàn thành
                if (!session.isCompleted()) {
                    logTransferError(session.getSessionId(), "session_timeout", "Phiên chuyển kho không hoàn thành trong thời gian cho phép");
                    // Ghi nhận lỗi vào cơ sở dữ liệu
                    recordTransferError(session);
                }
                
                // Xóa phiên
                it.remove();
                expiredCount++;
            }
        }
        
        if (expiredCount > 0) {
            Storage.getStorage().getLogger().info("Đã dọn dẹp " + expiredCount + " phiên chuyển kho hết hạn");
        }
    }
    
    /**
     * Kiểm tra xem phiên chuyển kho có đang hoạt động không
     * @param sessionId ID phiên
     * @return true nếu phiên đang hoạt động
     */
    public static boolean isSessionActive(String sessionId) {
        return activeSessions.containsKey(sessionId) && !activeSessions.get(sessionId).isCompleted();
    }
    
    /**
     * Lấy danh sách lỗi của phiên chuyển kho
     * @param sessionId ID phiên
     * @return Danh sách lỗi
     */
    public static List<String> getSessionErrors(String sessionId) {
        return transferErrors.getOrDefault(sessionId, Collections.emptyList());
    }
    
    /**
     * Lớp lưu trữ thông tin của một phiên chuyển kho
     */
    public static class TransferSession {
        private final String sessionId;
        private final String senderName;
        private final String receiverName;
        private final Map<String, Integer> requestedResources;
        private final Map<String, Integer> senderOriginalData;
        private final Map<String, Integer> receiverOriginalData;
        private Map<String, Integer> transferredResources;
        private final long startTime;
        private long endTime;
        private boolean completed;
        
        public TransferSession(String sessionId, String senderName, String receiverName,
                Map<String, Integer> requestedResources, Map<String, Integer> senderOriginalData,
                Map<String, Integer> receiverOriginalData, long startTime) {
            this.sessionId = sessionId;
            this.senderName = senderName;
            this.receiverName = receiverName;
            this.requestedResources = requestedResources;
            this.senderOriginalData = senderOriginalData;
            this.receiverOriginalData = receiverOriginalData;
            this.startTime = startTime;
            this.transferredResources = new HashMap<>();
            this.completed = false;
        }
        
        // Getters and setters
        public String getSessionId() {
            return sessionId;
        }
        
        public String getSenderName() {
            return senderName;
        }
        
        public String getReceiverName() {
            return receiverName;
        }
        
        public Map<String, Integer> getRequestedResources() {
            return requestedResources;
        }
        
        public Map<String, Integer> getSenderOriginalData() {
            return senderOriginalData;
        }
        
        public Map<String, Integer> getReceiverOriginalData() {
            return receiverOriginalData;
        }
        
        public Map<String, Integer> getTransferredResources() {
            return transferredResources;
        }
        
        public void setTransferredResources(Map<String, Integer> transferredResources) {
            this.transferredResources = transferredResources;
        }
        
        public long getStartTime() {
            return startTime;
        }
        
        public long getEndTime() {
            return endTime;
        }
        
        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }
        
        public boolean isCompleted() {
            return completed;
        }
        
        public void setCompleted(boolean completed) {
            this.completed = completed;
        }
    }
} 