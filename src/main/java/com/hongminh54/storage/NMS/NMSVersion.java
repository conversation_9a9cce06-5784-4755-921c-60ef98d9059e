package com.hongminh54.storage.NMS;

import java.util.Objects;

import org.bukkit.Bukkit;

/**
 * Lớp x<PERSON>c định phiên bản NMS cho Minecraft 1.12.2 - 1.21.x
 */
public class NMSVersion {

    /**
     * The major version of Minecraft.
     *
     * Usually, and probably always going to be... '1'.
     * </p>
     */
    private final int major;

    /**
     * The minor version of Minecraft.
     *
     * The minor version, for example '12' or '16' or most-recently '21'.
     * </p>
     */
    private final int minor;

    /**
     * The revision, 1.16.5 = 'R0' etc. etc.
     */
    private final int revision;

    /**
     * Constructor to initialise the NMSVersion data-type.
     *
     * Initialises the {@link #major}, {@link #minor} and {@link #revision} variables,
     * for usage with the {@link NMSAssistant} or by Ponder Developers.
     * </p>
     */
    public NMSVersion() {
        String version = Bukkit.getServer().getBukkitVersion();
        version = version.split("-")[0];
        final String[] versionDetails = version.split("\\.");
        major = Integer.parseInt(versionDetails[0]); // Always probably going to be '1'.
        minor = Integer.parseInt(versionDetails[1]); // 12/16/18/7/8 etc. etc.
        revision = versionDetails.length == 3 ? Integer.parseInt(versionDetails[2]) : 0;
        
        // Kiểm tra phiên bản hỗ trợ
        if (minor < 12) {
            Bukkit.getLogger().warning("Plugin này yêu cầu phiên bản Minecraft 1.12.2 trở lên!");
            Bukkit.getLogger().warning("Phiên bản hiện tại: 1." + minor + "." + revision);
            Bukkit.getLogger().warning("Vui lòng nâng cấp lên phiên bản Minecraft mới hơn để sử dụng plugin này.");
        }
    }

    public int getMajor() {
        return major;
    }

    public int getMinor() {
        return minor;
    }

    public int getRevision() {
        return revision;
    }
    
    /**
     * Kiểm tra xem phiên bản hiện tại có được hỗ trợ không (1.12.2 - 1.21.x)
     * @return true nếu phiên bản được hỗ trợ
     */
    public boolean isSupported() {
        // Kiểm tra phiên bản chính
        if (minor > 12) {
            // 1.13+, 1.14+, 1.15+, 1.16+, 1.17+, 1.18+, 1.19+, 1.20+, 1.21+
            return minor <= 21; // Hỗ trợ đến 1.21.x
        } else if (minor == 12) {
            // Chỉ hỗ trợ 1.12.2 trở lên
            return revision >= 2;
        }
        
        // Không hỗ trợ phiên bản nhỏ hơn 1.12.2
        return false;
    }

    @Override
    public String toString() {
        return "v" + getMajor() + "_" + getMinor() + "_R" + getRevision();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        NMSVersion that = (NMSVersion) o;
        return major == that.major && minor == that.minor && revision == that.revision;
    }

    @Override
    public int hashCode() {
        return Objects.hash(major, minor, revision);
    }
}
