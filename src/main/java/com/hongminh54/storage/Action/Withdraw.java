package com.hongminh54.storage.Action;

import java.util.Optional;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.PlayerInventory;
import org.jetbrains.annotations.NotNull;

import com.cryptomorin.xseries.XMaterial;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;

public class Withdraw {

    private final Player p;
    private final String material;
    private final String materialData;
    private final Integer amount;

    public Withdraw(Player p, @NotNull String material, Integer amount) {
        this.p = p;
        String material_data = material.replace(":", ";");
        if (material_data.contains(";")) materialData = material_data;
        else materialData = material_data + ";0";
        this.material = material_data.split(";")[0];
        this.amount = amount;
    }

    /**
     * Thêm vật phẩm vào kho đồ của ng<PERSON><PERSON><PERSON> ch<PERSON>i với x<PERSON> lý stacking thông minh
     * @param player <PERSON><PERSON><PERSON><PERSON> ch<PERSON> nh<PERSON>n vật phẩm
     * @param itemStack Loại vật phẩm
     * @param amount Số lượng
     */
    public static void addItemToInventory(Player player, ItemStack itemStack, int amount) {
        PlayerInventory inventory = player.getInventory();
        itemStack.setAmount(amount);
        int remainingAmount = amount;
        for (ItemStack item : inventory.getContents()) {
            if (item != null && item.getType() == itemStack.getType() && item.isSimilar(itemStack)) {
                int spaceLeft = item.getMaxStackSize() - item.getAmount();
                if (spaceLeft > 0) {
                    int toAdd = Math.min(spaceLeft, remainingAmount);
                    item.setAmount(item.getAmount() + toAdd);
                    remainingAmount -= toAdd;

                    if (remainingAmount <= 0) {
                        break;
                    }
                }
            }
        }
        if (remainingAmount > 0) {
            itemStack.setAmount(remainingAmount);
            inventory.addItem(itemStack);
        }
    }

    public void doAction() {
        // Kiểm tra tính nhất quán dữ liệu trước khi thao tác
        if (!MineManager.ensureDataConsistency(p)) {
            // Nếu có vấn đề, tải lại dữ liệu
            MineManager.loadPlayerData(p);
        }

        try {
        Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(material);
        if (xMaterial.isPresent()) {
            ItemStack itemStack = xMaterial.get().parseItem();
            if (itemStack != null) {
                    int currentAmount = MineManager.getPlayerBlock(p, getMaterialData());
                    
                    // Kiểm tra số lượng trong kho
                    if (currentAmount <= 0) {
                        String message = File.getMessage().getString("user.not_enough_items");
                        if (message == null) {
                            message = "&cBạn không có đủ khoáng sản trong kho! Hiện có: &f<amount>";
                            Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.not_enough_items' trong file message.yml");
                        }
                        p.sendMessage(Chat.colorize(message.replace("<amount>", "0")));
                        return;
                    }

                    // Xử lý rút theo số lượng cụ thể
                    if (getAmount() > 0) {
                        processWithdrawSpecificAmount(itemStack, currentAmount);
                    } else {
                        // Xử lý rút tất cả
                        processWithdrawAllItems(itemStack, currentAmount);
                    }
                } else {
                    Storage.getStorage().getLogger().warning("Không thể tạo ItemStack từ vật liệu: " + material);
                }
            } else {
                Storage.getStorage().getLogger().warning("Không thể khớp XMaterial cho: " + material);
            }
        } catch (Exception e) {
            Storage.getStorage().getLogger().severe("Lỗi khi rút khoáng sản: " + e.getMessage());

            String errorMessage = File.getMessage().getString("user.error");
            if (errorMessage == null) {
                errorMessage = "&cĐã xảy ra lỗi khi xử lý yêu cầu của bạn.";
            }
            p.sendMessage(Chat.colorize(errorMessage));
        }
    }

    /**
     * Xử lý việc rút một số lượng cụ thể
     */
    private void processWithdrawSpecificAmount(ItemStack itemStack, int currentAmount) {
        // Nếu người chơi không có đủ vật phẩm
        if (currentAmount < getAmount()) {
            String message = File.getMessage().getString("user.not_enough_items");
            if (message == null) {
                message = "&cBạn không có đủ khoáng sản trong kho! Hiện có: &f<amount>";
                Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.not_enough_items' trong file message.yml");
            }
            p.sendMessage(Chat.colorize(message.replace("<amount>", String.valueOf(currentAmount))));
            return;
        }

        // Kiểm tra không gian trống trong kho đồ
        int freeSpace = calculateFreeSpace(itemStack);
        if (freeSpace < getAmount()) {
            String message = File.getMessage().getString("user.not_enough_slot");
            if (message == null) {
                message = "&cBạn không có đủ chỗ trống trong túi đồ! Cần: &f<slots> &cchỗ";
                Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.not_enough_slot' trong file message.yml");
            }
            p.sendMessage(Chat.colorize(message.replace("<slots>", String.valueOf(freeSpace))));
            return;
        }

        // Thực hiện rút vật phẩm đơn giản
        if (MineManager.removeBlockAmount(p, getMaterialData(), getAmount())) {
            // Thêm vật phẩm vào kho đồ
            addItemToInventory(p, itemStack, getAmount());

            // Lưu dữ liệu
            MineManager.savePlayerData(p);

            // Gửi thông báo thành công
            String message = File.getMessage().getString("user.action.withdraw.withdraw_item");
            if (message == null) {
                message = "&aĐã rút &f#amount# #material# &atừ kho. Hiện tại còn &f#item_amount#&a/#max_storage#";
                Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.action.withdraw.withdraw_item' trong file message.yml");
            }

            String materialName = File.getConfig().getString("items." + getMaterialData());
            if (materialName == null) {
                materialName = getMaterialData();
                Storage.getStorage().getLogger().warning("Thiếu chuỗi 'items." + getMaterialData() + "' trong file config.yml");
            }

            p.sendMessage(Chat.colorize(message
                    .replace("#amount#", String.valueOf(getAmount()))
                    .replace("#material#", materialName)
                    .replace("#player#", p.getName())
                    .replace("#item_amount#", String.valueOf(MineManager.getPlayerBlock(p, getMaterialData())))
                    .replace("#max_storage#", String.valueOf(MineManager.getMaxBlock(p)))));
        } else {
            // Thông báo lỗi đơn giản
            String errorMessage = File.getMessage().getString("user.error");
            if (errorMessage == null) {
                errorMessage = "&cĐã xảy ra lỗi khi xử lý yêu cầu của bạn.";
            }
            p.sendMessage(Chat.colorize(errorMessage));
        }
    }

    /**
     * Xử lý việc rút tất cả vật phẩm
     */
    private void processWithdrawAllItems(ItemStack itemStack, int currentAmount) {
        // Kiểm tra không gian trống trong kho đồ
        int freeSpace = calculateFreeSpace(itemStack);
        
        // Số lượng thực tế có thể rút
        int withdrawAmount = Math.min(currentAmount, freeSpace);
        
        if (withdrawAmount <= 0) {
            String message = File.getMessage().getString("user.not_enough_slot");
            if (message == null) {
                message = "&cBạn không có đủ chỗ trống trong túi đồ!";
                Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.not_enough_slot' trong file message.yml");
            }
            p.sendMessage(Chat.colorize(message));
            return;
        }

        // Thực hiện rút vật phẩm đơn giản
        if (MineManager.removeBlockAmount(p, getMaterialData(), withdrawAmount)) {
            // Thêm vật phẩm vào kho đồ
            addItemToInventory(p, itemStack, withdrawAmount);

            // Lưu dữ liệu
            MineManager.savePlayerData(p);

            // Thông báo thành công
            String message = File.getMessage().getString("user.action.withdraw.withdraw_item");
            if (message == null) {
                message = "&aĐã rút &f#amount# #material# &atừ kho. Hiện tại còn &f#item_amount#&a/#max_storage#";
                Storage.getStorage().getLogger().warning("Thiếu chuỗi 'user.action.withdraw.withdraw_item' trong file message.yml");
            }

            String materialName = File.getConfig().getString("items." + getMaterialData());
            if (materialName == null) {
                materialName = getMaterialData();
                Storage.getStorage().getLogger().warning("Thiếu chuỗi 'items." + getMaterialData() + "' trong file config.yml");
            }

            p.sendMessage(Chat.colorize(message
                    .replace("#amount#", String.valueOf(withdrawAmount))
                    .replace("#material#", materialName)
                    .replace("#player#", p.getName())
                    .replace("#item_amount#", String.valueOf(MineManager.getPlayerBlock(p, getMaterialData())))
                    .replace("#max_storage#", String.valueOf(MineManager.getMaxBlock(p)))));
        } else {
            // Thông báo lỗi đơn giản
            String errorMessage = File.getMessage().getString("user.error");
            if (errorMessage == null) {
                errorMessage = "&cĐã xảy ra lỗi khi xử lý yêu cầu của bạn.";
            }
            p.sendMessage(Chat.colorize(errorMessage));
        }
    }

    /**
     * Tính toán không gian trống có sẵn cho vật phẩm cụ thể
     * @param itemStack Vật phẩm cần kiểm tra
     * @return Số lượng vật phẩm có thể thêm vào
     */
    private int calculateFreeSpace(ItemStack itemStack) {
        int freeSpace = 0;
        for (int i = 0; i < p.getInventory().getStorageContents().length; i++) {
            ItemStack inventoryItem = p.getInventory().getItem(i);
            if (inventoryItem == null || inventoryItem.getType().equals(Material.AIR)) {
                freeSpace += itemStack.getMaxStackSize();
            } else if (inventoryItem.isSimilar(itemStack)) {
                freeSpace += inventoryItem.getMaxStackSize() - inventoryItem.getAmount();
            }
        }
        return freeSpace;
    }
    
    public String getMaterialData() {
        return materialData;
    }
    
    public Player getPlayer() {
        return p;
    }
    
    public String getMaterial() {
        return material;
    }
    
    public Integer getAmount() {
        return amount;
    }
}
