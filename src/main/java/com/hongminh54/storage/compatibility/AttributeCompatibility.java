package com.hongminh54.storage.compatibility;

import org.bukkit.attribute.Attribute;
import org.bukkit.attribute.AttributeInstance;
import org.bukkit.attribute.AttributeModifier;
import org.bukkit.entity.LivingEntity;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;

import java.util.Collection;
import java.util.UUID;

/**
 * Lớp hỗ trợ tương thích Attribute API cho Minecraft 1.12.2 - 1.21.x
 * Xử lý entity attributes, attribute modifiers, và attribute changes
 */
public class AttributeCompatibility {
    
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_16_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(16);
    private static final boolean IS_1_20_5_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(20) && 
                                                       nmsAssistant.getNMSVersion().getRevision() >= 5;
    
    /**
     * Lấy Attribute tương thích đa phiên bản
     * 
     * @param modernName Tên attribute phiên bản mới (1.16+)
     * @param legacyName Tên attribute phiên bản cũ (1.12.2-1.15)
     * @return Attribute tương thích
     */
    public static Attribute getCompatibleAttribute(String modernName, String legacyName) {
        try {
            if (IS_1_16_OR_HIGHER) {
                return Attribute.valueOf(modernName);
            } else {
                // Phiên bản cũ có thể không có Attribute enum
                return getAttributeByReflection(legacyName);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không tìm thấy Attribute: " + modernName + "/" + legacyName);
            }
            return null;
        }
    }
    
    /**
     * Lấy attribute instance một cách an toàn
     * 
     * @param entity Entity
     * @param attribute Attribute
     * @return AttributeInstance hoặc null nếu có lỗi
     */
    public static AttributeInstance getAttributeInstanceSafely(LivingEntity entity, Attribute attribute) {
        if (entity == null || attribute == null) {
            return null;
        }
        
        try {
            return entity.getAttribute(attribute);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy attribute instance: " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * Set base value cho attribute một cách an toàn
     * 
     * @param entity Entity
     * @param attribute Attribute
     * @param value Giá trị mới
     * @return true nếu set thành công
     */
    public static boolean setAttributeBaseValue(LivingEntity entity, Attribute attribute, double value) {
        AttributeInstance instance = getAttributeInstanceSafely(entity, attribute);
        if (instance == null) {
            return false;
        }
        
        try {
            instance.setBaseValue(value);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set attribute base value: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Lấy base value của attribute
     * 
     * @param entity Entity
     * @param attribute Attribute
     * @return Base value hoặc 0 nếu có lỗi
     */
    public static double getAttributeBaseValue(LivingEntity entity, Attribute attribute) {
        AttributeInstance instance = getAttributeInstanceSafely(entity, attribute);
        if (instance == null) {
            return 0.0;
        }
        
        try {
            return instance.getBaseValue();
        } catch (Exception e) {
            return 0.0;
        }
    }
    
    /**
     * Lấy value hiện tại của attribute (bao gồm modifiers)
     * 
     * @param entity Entity
     * @param attribute Attribute
     * @return Current value hoặc 0 nếu có lỗi
     */
    public static double getAttributeValue(LivingEntity entity, Attribute attribute) {
        AttributeInstance instance = getAttributeInstanceSafely(entity, attribute);
        if (instance == null) {
            return 0.0;
        }
        
        try {
            return instance.getValue();
        } catch (Exception e) {
            return 0.0;
        }
    }
    
    /**
     * Thêm attribute modifier một cách an toàn
     * 
     * @param entity Entity
     * @param attribute Attribute
     * @param modifier AttributeModifier
     * @return true nếu thêm thành công
     */
    public static boolean addAttributeModifier(LivingEntity entity, Attribute attribute, AttributeModifier modifier) {
        AttributeInstance instance = getAttributeInstanceSafely(entity, attribute);
        if (instance == null || modifier == null) {
            return false;
        }
        
        try {
            instance.addModifier(modifier);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể thêm attribute modifier: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Xóa attribute modifier một cách an toàn
     * 
     * @param entity Entity
     * @param attribute Attribute
     * @param modifier AttributeModifier
     * @return true nếu xóa thành công
     */
    public static boolean removeAttributeModifier(LivingEntity entity, Attribute attribute, AttributeModifier modifier) {
        AttributeInstance instance = getAttributeInstanceSafely(entity, attribute);
        if (instance == null || modifier == null) {
            return false;
        }
        
        try {
            instance.removeModifier(modifier);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể xóa attribute modifier: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Xóa attribute modifier bằng UUID
     * 
     * @param entity Entity
     * @param attribute Attribute
     * @param uuid UUID của modifier
     * @return true nếu xóa thành công
     */
    public static boolean removeAttributeModifier(LivingEntity entity, Attribute attribute, UUID uuid) {
        AttributeInstance instance = getAttributeInstanceSafely(entity, attribute);
        if (instance == null || uuid == null) {
            return false;
        }
        
        try {
            // Tìm modifier với UUID
            for (AttributeModifier modifier : instance.getModifiers()) {
                try {
                    // Thử với method mới trước
                    java.lang.reflect.Method getKeyMethod = modifier.getClass().getMethod("getKey");
                    Object key = getKeyMethod.invoke(modifier);
                    if (key != null && key.toString().contains(uuid.toString())) {
                        instance.removeModifier(modifier);
                        return true;
                    }
                } catch (Exception ex) {
                    // Fallback với getUniqueId() deprecated
                    try {
                        @SuppressWarnings("deprecation")
                        UUID modifierUuid = modifier.getUniqueId();
                        if (modifierUuid.equals(uuid)) {
                            instance.removeModifier(modifier);
                            return true;
                        }
                    } catch (Exception ex2) {
                        // Ignore
                    }
                }
            }
            return false;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể xóa attribute modifier bằng UUID: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Lấy tất cả modifiers của attribute
     * 
     * @param entity Entity
     * @param attribute Attribute
     * @return Collection của AttributeModifier
     */
    public static Collection<AttributeModifier> getAttributeModifiers(LivingEntity entity, Attribute attribute) {
        AttributeInstance instance = getAttributeInstanceSafely(entity, attribute);
        if (instance == null) {
            return java.util.Collections.emptyList();
        }
        
        try {
            return instance.getModifiers();
        } catch (Exception e) {
            return java.util.Collections.emptyList();
        }
    }
    
    /**
     * Tạo AttributeModifier một cách an toàn
     *
     * @param uuid UUID của modifier
     * @param name Tên modifier
     * @param amount Giá trị
     * @param operation Operation type
     * @return AttributeModifier hoặc null nếu có lỗi
     */
    @SuppressWarnings("deprecation")
    public static AttributeModifier createAttributeModifier(UUID uuid, String name, double amount,
                                                           AttributeModifier.Operation operation) {
        try {
            // Thử với constructor mới trước (1.20.5+)
            try {
                // Tạo NamespacedKey
                Class<?> namespacedKeyClass = Class.forName("org.bukkit.NamespacedKey");
                java.lang.reflect.Constructor<?> keyConstructor = namespacedKeyClass.getConstructor(String.class, String.class);
                Object key = keyConstructor.newInstance("storage", name.toLowerCase().replace(" ", "_"));

                // Tạo AttributeModifier với key
                java.lang.reflect.Constructor<AttributeModifier> constructor = AttributeModifier.class.getConstructor(
                    namespacedKeyClass, double.class, AttributeModifier.Operation.class);
                return constructor.newInstance(key, amount, operation);
            } catch (Exception e) {
                // Fallback với constructor cũ
                return new AttributeModifier(uuid, name, amount, operation);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể tạo AttributeModifier: " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * Tạo AttributeModifier với UUID ngẫu nhiên
     * 
     * @param name Tên modifier
     * @param amount Giá trị
     * @param operation Operation type
     * @return AttributeModifier hoặc null nếu có lỗi
     */
    public static AttributeModifier createAttributeModifier(String name, double amount, 
                                                           AttributeModifier.Operation operation) {
        return createAttributeModifier(UUID.randomUUID(), name, amount, operation);
    }
    
    /**
     * Thêm attribute modifier vào ItemStack
     * 
     * @param item ItemStack
     * @param attribute Attribute
     * @param modifier AttributeModifier
     * @param slot Equipment slot (có thể null)
     * @return true nếu thêm thành công
     */
    public static boolean addItemAttributeModifier(ItemStack item, Attribute attribute, 
                                                  AttributeModifier modifier, String slot) {
        if (item == null || !item.hasItemMeta() || attribute == null || modifier == null) {
            return false;
        }
        
        try {
            ItemMeta meta = item.getItemMeta();
            
            if (IS_1_20_5_OR_HIGHER && slot != null) {
                // Phiên bản mới với EquipmentSlot
                try {
                    Object equipmentSlot = getEquipmentSlot(slot);
                    if (equipmentSlot != null) {
                        java.lang.reflect.Method addAttributeMethod = meta.getClass().getMethod(
                            "addAttributeModifier", Attribute.class, AttributeModifier.class, 
                            Class.forName("org.bukkit.inventory.EquipmentSlot"));
                        addAttributeMethod.invoke(meta, attribute, modifier, equipmentSlot);
                    } else {
                        meta.addAttributeModifier(attribute, modifier);
                    }
                } catch (Exception e) {
                    meta.addAttributeModifier(attribute, modifier);
                }
            } else {
                meta.addAttributeModifier(attribute, modifier);
            }
            
            item.setItemMeta(meta);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể thêm attribute modifier vào item: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Xóa tất cả attribute modifiers khỏi ItemStack
     * 
     * @param item ItemStack
     * @param attribute Attribute
     * @return true nếu xóa thành công
     */
    public static boolean removeItemAttributeModifiers(ItemStack item, Attribute attribute) {
        if (item == null || !item.hasItemMeta() || attribute == null) {
            return false;
        }
        
        try {
            ItemMeta meta = item.getItemMeta();
            meta.removeAttributeModifier(attribute);
            item.setItemMeta(meta);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể xóa attribute modifiers khỏi item: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Lấy Attribute bằng reflection cho phiên bản cũ
     */
    private static Attribute getAttributeByReflection(String attributeName) {
        try {
            // Thử lấy từ enum Attribute nếu có
            Class<?> attributeClass = Class.forName("org.bukkit.attribute.Attribute");
            return (Attribute) Enum.valueOf((Class<Enum>) attributeClass, attributeName);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Lấy EquipmentSlot enum cho phiên bản mới
     */
    private static Object getEquipmentSlot(String slotName) {
        try {
            Class<?> equipmentSlotClass = Class.forName("org.bukkit.inventory.EquipmentSlot");
            return Enum.valueOf((Class<Enum>) equipmentSlotClass, slotName.toUpperCase());
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Lấy max health của entity
     * 
     * @param entity Entity
     * @return Max health hoặc 20.0 nếu có lỗi
     */
    public static double getMaxHealth(LivingEntity entity) {
        try {
            Attribute maxHealthAttr = getCompatibleAttribute("GENERIC_MAX_HEALTH", "MAX_HEALTH");
            if (maxHealthAttr != null) {
                return getAttributeValue(entity, maxHealthAttr);
            } else {
                // Fallback
                return entity.getMaxHealth();
            }
        } catch (Exception e) {
            return 20.0;
        }
    }
    
    /**
     * Set max health cho entity
     * 
     * @param entity Entity
     * @param maxHealth Max health mới
     * @return true nếu set thành công
     */
    public static boolean setMaxHealth(LivingEntity entity, double maxHealth) {
        try {
            Attribute maxHealthAttr = getCompatibleAttribute("GENERIC_MAX_HEALTH", "MAX_HEALTH");
            if (maxHealthAttr != null) {
                return setAttributeBaseValue(entity, maxHealthAttr, maxHealth);
            } else {
                // Fallback
                entity.setMaxHealth(maxHealth);
                return true;
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set max health: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Lấy movement speed của entity
     * 
     * @param entity Entity
     * @return Movement speed hoặc 0.1 nếu có lỗi
     */
    public static double getMovementSpeed(LivingEntity entity) {
        try {
            Attribute speedAttr = getCompatibleAttribute("GENERIC_MOVEMENT_SPEED", "MOVEMENT_SPEED");
            if (speedAttr != null) {
                return getAttributeValue(entity, speedAttr);
            }
        } catch (Exception e) {
            // Ignore
        }
        return 0.1;
    }
    
    /**
     * Set movement speed cho entity
     * 
     * @param entity Entity
     * @param speed Speed mới
     * @return true nếu set thành công
     */
    public static boolean setMovementSpeed(LivingEntity entity, double speed) {
        try {
            Attribute speedAttr = getCompatibleAttribute("GENERIC_MOVEMENT_SPEED", "MOVEMENT_SPEED");
            if (speedAttr != null) {
                return setAttributeBaseValue(entity, speedAttr, speed);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set movement speed: " + e.getMessage());
            }
        }
        return false;
    }
}
