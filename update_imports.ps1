# PowerShell script để cập nhật tất cả import statements
# Thay thế các import từ Utils sang compatibility package

$files = @(
    "src/main/java/com/hongminh54/storage/CMD/SpecialMaterialCMD.java",
    "src/main/java/com/hongminh54/storage/GUI/manager/InteractiveItem.java",
    "src/main/java/com/hongminh54/storage/GUI/ViewPlayerStorageGUI.java",
    "src/main/java/com/hongminh54/storage/GUI/LeaderboardGUI.java",
    "src/main/java/com/hongminh54/storage/GUI/MultiTransferGUI.java",
    "src/main/java/com/hongminh54/storage/GUI/PlayerActionGUI.java",
    "src/main/java/com/hongminh54/storage/GUI/PlayerSearchGUI.java",
    "src/main/java/com/hongminh54/storage/GUI/StatsGUI.java",
    "src/main/java/com/hongminh54/storage/GUI/TransferGUI.java",
    "src/main/java/com/hongminh54/storage/Manager/SpecialMaterialManager.java",
    "src/main/java/com/hongminh54/storage/Manager/AxeEnchantManager.java",
    "src/main/java/com/hongminh54/storage/Manager/ItemManager.java",
    "src/main/java/com/hongminh54/storage/Listeners/BlockBreak.java",
    "src/main/java/com/hongminh54/storage/Events/BlockBreakEvent_.java"
)

# Thay thế các usage cũ bằng usage mới
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Updating $file"
        
        # Đọc nội dung file
        $content = Get-Content $file -Raw
        
        # Thay thế các usage
        $content = $content -replace "com\.hongminh54\.storage\.Utils\.MaterialCompatibility", "MaterialCompatibility"
        $content = $content -replace "com\.hongminh54\.storage\.Utils\.SoundCompatibility", "SoundCompatibility"
        $content = $content -replace "com\.hongminh54\.storage\.Utils\.AdvancedCompatibility", "AdvancedCompatibility"
        $content = $content -replace "com\.hongminh54\.storage\.Utils\.PlayerCompatibility", "PlayerCompatibility"
        $content = $content -replace "com\.hongminh54\.storage\.Utils\.InventoryCompatibility", "InventoryCompatibility"
        
        # Ghi lại file
        Set-Content $file $content -NoNewline
        
        Write-Host "Updated $file"
    } else {
        Write-Host "File not found: $file"
    }
}

Write-Host "Import update completed!"
