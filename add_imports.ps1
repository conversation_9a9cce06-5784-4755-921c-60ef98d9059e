# PowerShell script để thêm import statements cần thiết

# Danh sách file và import cần thêm
$importMappings = @{
    "src/main/java/com/hongminh54/storage/GUI/ViewPlayerStorageGUI.java" = "import com.hongminh54.storage.compatibility.SoundCompatibility;"
    "src/main/java/com/hongminh54/storage/GUI/LeaderboardGUI.java" = "import com.hongminh54.storage.compatibility.MaterialCompatibility;"
    "src/main/java/com/hongminh54/storage/GUI/MultiTransferGUI.java" = "import com.hongminh54.storage.compatibility.SoundCompatibility;"
    "src/main/java/com/hongminh54/storage/GUI/PlayerActionGUI.java" = @"
import com.hongminh54.storage.compatibility.MaterialCompatibility;
import com.hongminh54.storage.compatibility.SoundCompatibility;
"@
    "src/main/java/com/hongminh54/storage/GUI/PlayerSearchGUI.java" = "import com.hongminh54.storage.compatibility.SoundCompatibility;"
    "src/main/java/com/hongminh54/storage/GUI/StatsGUI.java" = "import com.hongminh54.storage.compatibility.MaterialCompatibility;"
    "src/main/java/com/hongminh54/storage/GUI/TransferGUI.java" = "import com.hongminh54.storage.compatibility.SoundCompatibility;"
    "src/main/java/com/hongminh54/storage/Manager/SpecialMaterialManager.java" = @"
import com.hongminh54.storage.compatibility.MaterialCompatibility;
import com.hongminh54.storage.compatibility.SoundCompatibility;
"@
    "src/main/java/com/hongminh54/storage/Manager/AxeEnchantManager.java" = "import com.hongminh54.storage.compatibility.MaterialCompatibility;"
    "src/main/java/com/hongminh54/storage/Manager/ItemManager.java" = "import com.hongminh54.storage.compatibility.MaterialCompatibility;"
    "src/main/java/com/hongminh54/storage/Listeners/BlockBreak.java" = "import com.hongminh54.storage.compatibility.MaterialCompatibility;"
    "src/main/java/com/hongminh54/storage/Events/BlockBreakEvent_.java" = "import com.hongminh54.storage.compatibility.SoundCompatibility;"
}

foreach ($file in $importMappings.Keys) {
    if (Test-Path $file) {
        Write-Host "Adding imports to $file"
        
        # Đọc nội dung file
        $content = Get-Content $file -Raw
        $imports = $importMappings[$file]
        
        # Tìm vị trí để thêm import (sau import cuối cùng)
        $lines = $content -split "`n"
        $lastImportIndex = -1
        
        for ($i = 0; $i -lt $lines.Length; $i++) {
            if ($lines[$i] -match "^import ") {
                $lastImportIndex = $i
            }
        }
        
        if ($lastImportIndex -ge 0) {
            # Thêm import sau import cuối cùng
            $newLines = @()
            $newLines += $lines[0..$lastImportIndex]
            $newLines += $imports
            $newLines += $lines[($lastImportIndex + 1)..($lines.Length - 1)]
            
            $newContent = $newLines -join "`n"
            Set-Content $file $newContent -NoNewline
            
            Write-Host "Added imports to $file"
        } else {
            Write-Host "No import section found in $file"
        }
    } else {
        Write-Host "File not found: $file"
    }
}

Write-Host "Import addition completed!"
