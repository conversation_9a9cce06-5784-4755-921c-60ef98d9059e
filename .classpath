<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" output="build/resources/test" path="src/test/resources">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
			<attribute name="resource" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/classes/java/test" path="src/test/java">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/resources/main" path="src/main/resources">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
			<attribute name="resource" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/classes/java/main" path="src/main/java">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/classes/java/test" path="build/generated/sources/annotationProcessor/java/test">
		<attributes>
			<attribute name="test" value="true"/>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="build/classes/java/main" path="build/generated/sources/annotationProcessor/java/main">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-1.8">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.tchristofferson/ConfigUpdater/2.1-SNAPSHOT/7773ad85aeafe68f55e38b24a3eb7d634bf34793/ConfigUpdater-2.1-SNAPSHOT.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.spigotmc/spigot-api/1.16.5-R0.1-SNAPSHOT/2c2dfc5e5eade1a5e959b06b92e25ab411b5e5ea/spigot-api-1.16.5-R0.1-SNAPSHOT.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.spigotmc/spigot-api/1.16.5-R0.1-SNAPSHOT/b05164f5c5c1a48fd70cf63ce147b3d575820714/spigot-api-1.16.5-R0.1-SNAPSHOT-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.yaml/snakeyaml/1.27/359d62567480b07a679dc643f82fc926b100eed5/snakeyaml-1.27.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.yaml/snakeyaml/1.27/62fc33bebe2f0c71864032c0549daf0a4a0dae2b/snakeyaml-1.27-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.github.InitSync.XConfig/bukkit/1.1.7/e0679a75df15155bb1d07be0d4880c5c54139afa/bukkit-1.1.7.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.github.InitSync.XConfig/bukkit/1.1.7/1bec941c809397a2edc5e7f143dba38a4985802b/bukkit-1.1.7-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/junit/junit/4.10/e4f1766ce7404a08f45d859fb9c226fc9e41a861/junit-4.10.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/junit/junit/4.10/6c98d6766e72d5575f96c9479d1c1d3b865c6e25/junit-4.10-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/23.0.0/8cc20c07506ec18e0834947b84a864bfc094484e/annotations-23.0.0.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/23.0.0/ca5088d615accaabd2aa956b10b236a4e75cfccb/annotations-23.0.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/de.tr7zw/item-nbt-api/2.15.0/52fa821ba619524cac58be6d7260e72b42ae3a0a/item-nbt-api-2.15.0.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/de.tr7zw/item-nbt-api/2.15.0/29689f4d8039bc2abf8e5562c5c09927f9bfc51f/item-nbt-api-2.15.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.codemc.worldguardwrapper/worldguardwrapper/1.2.1-SNAPSHOT/7443aa3b34a91f24c38b3042ec32ab82fd026784/worldguardwrapper-1.2.1-SNAPSHOT.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.github.cryptomorin/XSeries/13.2.0/d7815220b59b2095f8d3c8704ef32a2cd27b843e/XSeries-13.2.0.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.github.cryptomorin/XSeries/13.2.0/ee768bcbfe1d1313ffc0481c4ce638e750882106/XSeries-13.2.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.hamcrest/hamcrest-core/1.1/860340562250678d1a344907ac75754e259cdb14/hamcrest-core-1.1.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.hamcrest/hamcrest-core/1.1/2ccf1154d1a8936042a8a742dc3e611d02ac7213/hamcrest-core-1.1-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/me.clip/placeholderapi/2.11.6/d9ad7a4c2759a6cc5c824cf56e5d06f12333f88/placeholderapi-2.11.6.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/me.clip/placeholderapi/2.11.6/b42a4ec35b8e983f03c7cffedff7fa374df9879a/placeholderapi-2.11.6-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.gson/gson/2.8.0/c4ba5371a29ac9b2ad6129b1d39ea38750043eff/gson-2.8.0.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.gson/gson/2.8.0/baf95d8519fc1a11d388f8543cb40cd2bb9d66dc/gson-2.8.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/commons-lang/commons-lang/2.6/ce1edb914c94ebc388f086c6827e8bdeec71ac2/commons-lang-2.6.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/commons-lang/commons-lang/2.6/67313d715fbf0ea4fd0bdb69217fb77f807a8ce5/commons-lang-2.6-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/guava/21.0/3a3d111be1be1b745edfa7d91678a12d7ed38709/guava-21.0.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/guava/21.0/b9ed26b8c23fe7cd3e6b463b34e54e5c6d9536d5/guava-21.0-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.googlecode.json-simple/json-simple/1.1.1/c9ad4a0850ab676c5c64461a05ca524cdfff59f1/json-simple-1.1.1.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.googlecode.json-simple/json-simple/1.1.1/15bba08e3a239d54b68209c001f9c911559d2fed/json-simple-1.1.1-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.md-5/bungeecord-chat/1.16-R0.4/e043e8eed8fdb5c157090a84ac8fd64a6a8d0d88/bungeecord-chat-1.16-R0.4.jar" sourcepath="C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.md-5/bungeecord-chat/1.16-R0.4/b875c61dc56dce4a6919f4531b06efa0b63f1af6/bungeecord-chat-1.16-R0.4-sources.jar">
		<attributes>
			<attribute name="gradle.buildServer" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="output" path="bin/default"/>
</classpath>
