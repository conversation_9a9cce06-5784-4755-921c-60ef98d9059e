package com.hongminh54.storage.compatibility;

import org.bukkit.Sound;
import org.bukkit.entity.Player;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;

/**
 * Lớp hỗ trợ tương thích âm thanh cho Minecraft 1.12.2 - 1.21.x
 * Xử lý các vấn đề IncompatibleClassChangeError và Sound enum differences
 */
public class SoundCompatibility {
    
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    
    /**
     * Phát âm thanh an toàn cho người chơi - String-only approach
     *
     * @param player Ng<PERSON><PERSON><PERSON> chơi
     * @param soundName Tên âm thanh
     * @param volume Âm lượng
     * @param pitch Cao độ
     */
    public static void playSound(Player player, String soundName, float volume, float pitch) {
        if (player == null || soundName == null || soundName.isEmpty()) {
            return;
        }

        try {
            // Thử phát âm thanh trực tiếp bằng string (tránh hoàn toàn Sound enum)
            player.playSound(player.getLocation(), soundName, volume, pitch);
        } catch (Exception e1) {
            try {
                // Thử với tên âm thanh đã chuyển đổi
                String convertedName = convertSoundName(soundName);
                player.playSound(player.getLocation(), convertedName, volume, pitch);
            } catch (Exception e2) {
                // Sử dụng fallback sound
                playFallbackSound(player, volume, pitch);
            }
        }
    }
    
    /**
     * Lấy Sound enum tương thích với phiên bản hiện tại
     *
     * @deprecated CẢNH BÁO: Method này có thể gây IncompatibleClassChangeError trên 1.12.2
     * Sử dụng playSound() với String thay thế
     *
     * @param soundName Tên âm thanh
     * @return Sound enum hoặc null nếu không tìm thấy
     */
    @Deprecated
    public static Sound getCompatibleSound(String soundName) {
        if (soundName == null) {
            return null;
        }

        try {
            // CHỈ sử dụng khi thực sự cần thiết và đã thử string trước
            Sound sound = findSoundByName(soundName);
            if (sound != null) {
                return sound;
            }

            // Nếu không tìm thấy, thử chuyển đổi tên
            String convertedName = convertSoundName(soundName);
            if (!convertedName.equals(soundName)) {
                return findSoundByName(convertedName);
            }

            return null;
        } catch (Exception e) {
            // Bỏ qua tất cả lỗi Sound enum
            return null;
        }
    }
    
    /**
     * Tìm Sound enum bằng tên sử dụng reflection hoàn toàn an toàn
     *
     * @deprecated CẢNH BÁO: Method này có thể gây IncompatibleClassChangeError trên 1.12.2
     *
     * @param name Tên âm thanh
     * @return Sound enum hoặc null
     */
    @Deprecated
    private static Sound findSoundByName(String name) {
        try {
            // Sử dụng reflection để tránh Sound.values() trực tiếp
            Class<Sound> soundClass = Sound.class;
            java.lang.reflect.Method valuesMethod = soundClass.getMethod("values");
            Sound[] sounds = (Sound[]) valuesMethod.invoke(null);

            for (Sound sound : sounds) {
                if (sound.name().equals(name)) {
                    return sound;
                }
            }
            return null;
        } catch (Exception e) {
            // Nếu reflection thất bại, thử cách khác
            return findSoundByNameFallback(name);
        }
    }

    /**
     * Fallback method để tìm Sound khi reflection thất bại
     * @param name Tên âm thanh
     * @return Sound enum hoặc null
     */
    private static Sound findSoundByNameFallback(String name) {
        try {
            // Thử với các âm thanh phổ biến trước
            String[] commonSounds = {
                "NOTE_PLING", "BLOCK_NOTE_BLOCK_PLING",
                "CHEST_OPEN", "BLOCK_CHEST_OPEN",
                "CHEST_CLOSE", "BLOCK_CHEST_CLOSE",
                "CLICK", "UI_BUTTON_CLICK",
                "VILLAGER_NO", "ENTITY_VILLAGER_NO",
                "VILLAGER_YES", "ENTITY_VILLAGER_YES",
                "EXPERIENCE_ORB_PICKUP", "ENTITY_EXPERIENCE_ORB_PICKUP",
                "PLAYER_LEVELUP", "ENTITY_PLAYER_LEVELUP",
                "EXPLODE", "ENTITY_GENERIC_EXPLODE",
                "ITEM_PICKUP", "ENTITY_ITEM_PICKUP"
            };

            for (String soundName : commonSounds) {
                if (soundName.equals(name)) {
                    try {
                        // Sử dụng reflection để tạo Sound enum
                        java.lang.reflect.Method valueOfMethod = Sound.class.getMethod("valueOf", String.class);
                        return (Sound) valueOfMethod.invoke(null, name);
                    } catch (Exception e) {
                        // Bỏ qua và thử tiếp
                    }
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Chuyển đổi tên âm thanh cho tương thích đa phiên bản
     * 
     * @param soundName Tên âm thanh gốc
     * @return Tên âm thanh đã chuyển đổi
     */
    private static String convertSoundName(String soundName) {
        if (soundName == null) {
            return null;
        }
        
        if (IS_PRE_113) {
            // Chuyển đổi từ tên mới sang tên cũ (1.12.2)
            switch (soundName) {
                case "BLOCK_NOTE_BLOCK_PLING":
                    return "NOTE_PLING";
                case "BLOCK_CHEST_OPEN":
                    return "CHEST_OPEN";
                case "BLOCK_CHEST_CLOSE":
                    return "CHEST_CLOSE";
                case "ENTITY_EXPERIENCE_ORB_PICKUP":
                    return "EXPERIENCE_ORB_PICKUP";
                case "ENTITY_PLAYER_LEVELUP":
                    return "PLAYER_LEVELUP";
                case "UI_BUTTON_CLICK":
                    return "CLICK";
                case "ENTITY_VILLAGER_NO":
                    return "VILLAGER_NO";
                case "ENTITY_VILLAGER_YES":
                    return "VILLAGER_YES";
                case "ENTITY_ITEM_PICKUP":
                    return "ITEM_PICKUP";
                case "ENTITY_GENERIC_EXPLODE":
                    return "EXPLODE";
                default:
                    // Thử các pattern chung
                    if (soundName.startsWith("BLOCK_NOTE_BLOCK_")) {
                        String instrument = soundName.replace("BLOCK_NOTE_BLOCK_", "");
                        return "NOTE_" + instrument;
                    } else if (soundName.startsWith("ENTITY_EXPERIENCE_ORB_")) {
                        String action = soundName.replace("ENTITY_EXPERIENCE_ORB_", "");
                        return "EXPERIENCE_ORB_" + action;
                    } else if (soundName.startsWith("ENTITY_PLAYER_")) {
                        String action = soundName.replace("ENTITY_PLAYER_", "");
                        return "PLAYER_" + action;
                    } else if (soundName.startsWith("ENTITY_VILLAGER_")) {
                        String action = soundName.replace("ENTITY_VILLAGER_", "");
                        return "VILLAGER_" + action;
                    } else if (soundName.startsWith("UI_BUTTON_")) {
                        return "CLICK";
                    }
                    return soundName;
            }
        } else {
            // Chuyển đổi từ tên cũ sang tên mới (1.13+)
            switch (soundName) {
                case "NOTE_PLING":
                    return "BLOCK_NOTE_BLOCK_PLING";
                case "CHEST_OPEN":
                    return "BLOCK_CHEST_OPEN";
                case "CHEST_CLOSE":
                    return "BLOCK_CHEST_CLOSE";
                case "EXPERIENCE_ORB_PICKUP":
                    return "ENTITY_EXPERIENCE_ORB_PICKUP";
                case "PLAYER_LEVELUP":
                    return "ENTITY_PLAYER_LEVELUP";
                case "CLICK":
                    return "UI_BUTTON_CLICK";
                case "VILLAGER_NO":
                    return "ENTITY_VILLAGER_NO";
                case "VILLAGER_YES":
                    return "ENTITY_VILLAGER_YES";
                case "ITEM_PICKUP":
                    return "ENTITY_ITEM_PICKUP";
                case "EXPLODE":
                    return "ENTITY_GENERIC_EXPLODE";
                default:
                    // Thử các pattern chung
                    if (soundName.startsWith("NOTE_")) {
                        String instrument = soundName.replace("NOTE_", "");
                        return "BLOCK_NOTE_BLOCK_" + instrument;
                    } else if (soundName.startsWith("EXPERIENCE_ORB_")) {
                        String action = soundName.replace("EXPERIENCE_ORB_", "");
                        return "ENTITY_EXPERIENCE_ORB_" + action;
                    } else if (soundName.startsWith("PLAYER_")) {
                        String action = soundName.replace("PLAYER_", "");
                        return "ENTITY_PLAYER_" + action;
                    } else if (soundName.startsWith("VILLAGER_")) {
                        String action = soundName.replace("VILLAGER_", "");
                        return "ENTITY_VILLAGER_" + action;
                    }
                    return soundName;
            }
        }
    }
    
    /**
     * Phát âm thanh fallback an toàn - String-only approach
     *
     * @param player Người chơi
     * @param volume Âm lượng
     * @param pitch Cao độ
     */
    private static void playFallbackSound(Player player, float volume, float pitch) {
        try {
            if (IS_PRE_113) {
                // Thử các âm thanh fallback cho 1.12.2
                String[] fallbackSounds = {"NOTE_PLING", "CLICK", "CHEST_OPEN"};
                for (String sound : fallbackSounds) {
                    try {
                        player.playSound(player.getLocation(), sound, volume, pitch);
                        return; // Thành công, thoát
                    } catch (Exception e) {
                        // Thử âm thanh tiếp theo
                    }
                }
            } else {
                // Thử các âm thanh fallback cho 1.13+
                String[] fallbackSounds = {"BLOCK_NOTE_BLOCK_PLING", "UI_BUTTON_CLICK", "BLOCK_CHEST_OPEN"};
                for (String sound : fallbackSounds) {
                    try {
                        player.playSound(player.getLocation(), sound, volume, pitch);
                        return; // Thành công, thoát
                    } catch (Exception e) {
                        // Thử âm thanh tiếp theo
                    }
                }
            }
        } catch (Exception e) {
            // Bỏ qua lỗi hoàn toàn để tránh crash
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể phát âm thanh fallback: " + e.getMessage());
            }
        }
    }
    
    /**
     * Phát âm thanh từ chuỗi cấu hình - String-only approach
     *
     * @param player Người chơi
     * @param soundConfig Chuỗi cấu hình dạng "SOUND:VOLUME:PITCH"
     */
    public static void playSoundFromConfig(Player player, String soundConfig) {
        if (player == null || soundConfig == null || soundConfig.isEmpty()) {
            return;
        }

        try {
            String[] parts = soundConfig.split(":");
            String soundName = parts[0];
            float volume = parts.length > 1 ? Float.parseFloat(parts[1]) : 1.0f;
            float pitch = parts.length > 2 ? Float.parseFloat(parts[2]) : 1.0f;

            playSound(player, soundName, volume, pitch);
        } catch (Exception e) {
            // Sử dụng fallback nếu có lỗi parse
            playFallbackSound(player, 1.0f, 1.0f);
        }
    }

    /**
     * Kiểm tra xem âm thanh có tồn tại không - String-only approach
     *
     * @param player Người chơi để test
     * @param soundName Tên âm thanh
     * @return true nếu âm thanh có thể phát được
     */
    public static boolean isSoundAvailable(Player player, String soundName) {
        if (player == null || soundName == null || soundName.isEmpty()) {
            return false;
        }

        try {
            // Thử phát âm thanh với volume 0 để test
            player.playSound(player.getLocation(), soundName, 0.0f, 1.0f);
            return true;
        } catch (Exception e) {
            try {
                // Thử với tên đã chuyển đổi
                String convertedName = convertSoundName(soundName);
                player.playSound(player.getLocation(), convertedName, 0.0f, 1.0f);
                return true;
            } catch (Exception e2) {
                return false;
            }
        }
    }
}
