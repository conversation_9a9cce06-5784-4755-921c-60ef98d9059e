package com.hongminh54.storage.Listeners;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.CacheManager;
import com.hongminh54.storage.Utils.LeaderboardManager;
import com.hongminh54.storage.Utils.StatsManager;

public class JoinQuit implements Listener {

    @EventHandler
    public void onJoin(@NotNull PlayerJoinEvent e) {
        Player p = e.getPlayer();
        
        // Tải dữ liệu trong luồng bất đồng bộ để tránh lag server khi người chơi đăng nhập
        Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
            // Đồng bộ hóa cache đơn giản cho người chơi
            // Đồng bộ cache đơn giản
            MineManager.loadPlayerData(p);
            
            // Ghi log để debug
            Storage.getStorage().getLogger().info("Đã tải dữ liệu kho và thống kê cho " + p.getName() + " (bất đồng bộ)");
        });
    }

    @EventHandler
    public void onQuit(@NotNull PlayerQuitEvent e) {
        Player p = e.getPlayer();
        
        // Lưu dữ liệu thống kê bất đồng bộ
        StatsManager.savePlayerStatsAsync(p);
        
        // Sau đó lưu dữ liệu kho bất đồng bộ
        MineManager.savePlayerDataAsync(p);
        
        // Xóa dữ liệu khỏi cache
        StatsManager.removeFromCache(p.getName());
        
        // Xóa dữ liệu từ cache bảng xếp hạng
        LeaderboardManager.removePlayerFromCache(p.getName());
        
        // Xóa dữ liệu đồng bộ hóa cache
        // Dọn dẹp cache đơn giản
        CacheManager.clearPlayerCache(p.getName());
        
        // Xóa dữ liệu chat đang chờ xử lý
        com.hongminh54.storage.Listeners.Chat.chat_deposit.remove(p);
        com.hongminh54.storage.Listeners.Chat.chat_withdraw.remove(p);
        com.hongminh54.storage.Listeners.Chat.chat_sell.remove(p);
        com.hongminh54.storage.Listeners.Chat.chat_convert_block.remove(p);
        com.hongminh54.storage.Listeners.Chat.chat_transfer.remove(p);
        
        // Ghi log để debug
        Storage.getStorage().getLogger().info("Đã lưu dữ liệu kho và thống kê cho " + p.getName() + " (bất đồng bộ)");
    }
}
