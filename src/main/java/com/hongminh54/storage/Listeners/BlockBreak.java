package com.hongminh54.storage.Listeners;

/**
 * Lớ<PERSON> này xử lý sự kiện khi người chơi đào khối trong trường hợp toggle của người chơi là TRUE.
 * Lớp BlockBreakEvent_.java xử lý sự kiện khi toggle của người chơi là FALSE.
 * 
 * Chú ý: Cả hai lớp này KHÔNG được xử lý cùng một sự kiện đào khối vì sẽ gây ra việc
 * tài nguyên được thêm hai lần vào kho của người chơi. Kiểm tra MineManager.toggle
 * đã được thêm trong BlockBreakEvent_ để tránh xung đột này.
 */

import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.logging.Level;

import org.bukkit.Bukkit;
import org.bukkit.block.Block;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.PlayerInventory;
import org.bukkit.metadata.MetadataValue;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.NotNull;

import com.cryptomorin.xseries.XEnchantment;
import com.cryptomorin.xseries.messages.ActionBar;
import com.cryptomorin.xseries.messages.Titles;
import com.hongminh54.storage.Events.MiningEvent;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.Number;
import com.hongminh54.storage.compatibility.PlayerCompatibility;
import com.hongminh54.storage.Utils.StatsManager;
import com.hongminh54.storage.WorldGuard.WorldGuard;

public class BlockBreak implements Listener {

    // Thêm biến để đếm số lượng block đã đào và kích hoạt lưu dữ liệu
    private static final ConcurrentMap<UUID, Integer> mineCountSinceLastSave = new ConcurrentHashMap<>();
    // Số lượng block đào trước khi lưu dữ liệu
    private static final int SAVE_THRESHOLD = 250; // Tăng ngưỡng lưu để giảm số lần I/O

    // Thêm biến để kiểm soát hiệu suất
    private static final ConcurrentMap<UUID, Long> lastBreakTime = new ConcurrentHashMap<>();
    private static final ConcurrentMap<UUID, Integer> breakCount = new ConcurrentHashMap<>();
    private static final long BREAK_COOLDOWN = 40; // 40ms cooldown
    
    // Bộ nhớ đệm cho các block đã kiểm tra và kết quả của nó để tránh kiểm tra lại nhiều lần
    private static final ConcurrentMap<String, Boolean> blockBreakableCache = new ConcurrentHashMap<>();
    private static final int CACHE_CLEANUP_INTERVAL = 12000; // 10 phút = 12000 ticks
    private static int lastCleanupTime = 0;

    @EventHandler(ignoreCancelled = true, priority = EventPriority.HIGHEST)
    public void onBreak(@NotNull BlockBreakEvent e) {
        // Kiểm tra nhanh trước khi xử lý
        if (!MineManager.toggle.getOrDefault(e.getPlayer(), false)) {
            return; // Chỉ xử lý khi toggle là true
        }

        Player p = e.getPlayer();
        UUID playerUUID = p.getUniqueId();
        Block block = e.getBlock();
        
        // Kiểm tra tốc độ đào để tránh người chơi đào quá nhanh
        long now = System.currentTimeMillis();
        if (lastBreakTime.containsKey(playerUUID)) {
            long lastTime = lastBreakTime.get(playerUUID);
            if (now - lastTime < BREAK_COOLDOWN / 4) {
                return; // Không xử lý nếu quá nhanh
            }
        }
        
        // Cập nhật thời gian đào gần nhất
        lastBreakTime.put(playerUUID, now);
        
        // Tăng số đếm block đã phá để kiểm soát tốc độ đào
        int breakCountValue = breakCount.getOrDefault(playerUUID, 0) + 1;
        breakCount.put(playerUUID, breakCountValue);
        
        // Reset đếm mỗi 5 giây
        if (breakCountValue == 1) {
            Bukkit.getScheduler().runTaskLaterAsynchronously(Storage.getStorage(), 
                () -> breakCount.remove(playerUUID), 100L);
        }
        
        // Kiểm tra các điều kiện cấm đào block
        // Tối ưu: Gộp các kiểm tra để tránh nhiều lần kiểm tra riêng biệt
        if ((Storage.isWorldGuardInstalled() && !WorldGuard.handleForLocation(p, block.getLocation())) ||
            (File.getConfig().getBoolean("prevent_rebreak") && isPlacedBlock(block)) ||
            (File.getConfig().contains("blacklist_world") && 
             File.getConfig().getStringList("blacklist_world").contains(p.getWorld().getName()))) {
                return;
            }

        // Kiểm tra xem block có trong danh sách có thể đào không bằng cache
        String blockKey = block.getType().toString();
        Boolean isBreakable = blockBreakableCache.get(blockKey);
        if (isBreakable == null) {
            isBreakable = MineManager.checkBreak(block);
            blockBreakableCache.put(blockKey, isBreakable);
        }

        if (!isBreakable) {
            return;
        }

        // Kiểm tra và dọn bộ nhớ đệm theo định kỳ
        if (++lastCleanupTime >= CACHE_CLEANUP_INTERVAL) {
            lastCleanupTime = 0;
            Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), 
                () -> blockBreakableCache.clear());
        }

        // Xử lý khi auto-pickup được bật
        if (MineManager.isAutoPickup(p)) {
            boolean inv_full = (p.getInventory().firstEmpty() == -1);
            if (inv_full) {
                processFullInventory(p);
            }

            String drop = MineManager.getDrop(block);
            if (drop == null || "UNKNOWN_DROP".equals(drop)) {
                return;
            }

            processBlockBreak(p, block, drop);
        }
    }

    private void processFullInventory(Player p) {
        for (ItemStack itemStack : p.getInventory().getContents()) {
            if (itemStack == null) continue;
            
            String drop = MineManager.getItemStackDrop(itemStack);
            if (drop == null) continue;
            
            int old_data = MineManager.getPlayerBlock(p, drop);
                int max_storage = MineManager.getMaxBlock(p);
                int count = max_storage - old_data;
                        int amount = itemStack.getAmount();
            
            int new_data = old_data + amount;
            int min = Math.min(count, amount);
                            int replacement = new_data >= max_storage ? min : amount;
            
                            if (MineManager.addBlockAmount(p, drop, replacement)) {
                                removeItems(p, itemStack, replacement);
                            }
                        }
                    }

    private void processBlockBreak(Player p, Block block, String drop) {
        ItemStack hand = p.getInventory().getItemInMainHand();
        // Lấy enchantment Fortune với tương thích đa phiên bản
        Enchantment fortune = XEnchantment.FORTUNE.get() != null
            ? XEnchantment.FORTUNE.get()
            : getFortuneEnchantment();
        
        // Tính toán số lượng vật phẩm rơi ra
                int amount;
                if (!hand.containsEnchantment(fortune)) {
                    amount = getDropAmount(block);
                } else {
                    if (File.getConfig().getStringList("whitelist_fortune").contains(block.getType().name())) {
                amount = Number.getRandomInteger(getDropAmount(block), 
                    getDropAmount(block) + hand.getEnchantmentLevel(fortune) + 2);
            } else {
                amount = getDropAmount(block);
            }
                }
                
                // Áp dụng hiệu ứng sự kiện khai thác nếu có
                MiningEvent miningEvent = MiningEvent.getInstance();
                if (miningEvent.isActive()) {
                    amount = miningEvent.processBlockBreak(p, drop, amount);
                }
                
        // Thêm tài nguyên vào kho
        if (addResourceToStorage(p, drop, amount)) {
            // Không bắt buộc phải lưu mỗi khi đào - xử lý theo ngưỡng
            updateMineCounter(p);
            
            // Hủy drop từ block - không thể truy cập e ở đây
            try {
                // Thêm metadata để đánh dấu block không nên drop vật phẩm
                block.setMetadata("NoDrops", new org.bukkit.metadata.FixedMetadataValue(Storage.getStorage(), true));
            } catch (Exception ex) {
                Storage.getStorage().getLogger().warning("Không thể đánh dấu block không drop: " + ex.getMessage());
            }
        }
    }

    private boolean addResourceToStorage(Player p, String drop, int amount) {
                if (MineManager.addBlockAmount(p, drop, amount)) {
            // Hiển thị thông báo cho người chơi nếu cần
            UUID playerUUID = p.getUniqueId();
            
                    if (File.getConfig().getBoolean("mine.actionbar.enable")) {
                        String name = File.getConfig().getString("items." + drop);
                        String actionBarMessage = Objects.requireNonNull(File.getConfig().getString("mine.actionbar.action"))
                            .replace("#item#", name != null ? name : drop.replace("_", " "))
                            .replace("#amount#", String.valueOf(amount))
                            .replace("#storage#", String.valueOf(MineManager.getPlayerBlock(p, drop)))
                            .replace("#max#", String.valueOf(MineManager.getMaxBlock(p)));

                        // Sử dụng PlayerCompatibility để gửi action bar an toàn
                        PlayerCompatibility.sendActionBar(p, actionBarMessage);
                    }
            
                    if (File.getConfig().getBoolean("mine.title.enable")) {
                        String name = File.getConfig().getString("items." + drop);
                        String replacement = name != null ? name : drop.replace("_", " ");

                        String title = Objects.requireNonNull(File.getConfig().getString("mine.title.title"))
                            .replace("#item#", replacement)
                            .replace("#amount#", String.valueOf(amount))
                            .replace("#storage#", String.valueOf(MineManager.getPlayerBlock(p, drop)))
                            .replace("#max#", String.valueOf(MineManager.getMaxBlock(p)));

                        String subtitle = Objects.requireNonNull(File.getConfig().getString("mine.title.subtitle"))
                            .replace("#item#", replacement)
                            .replace("#amount#", String.valueOf(amount))
                            .replace("#storage#", String.valueOf(MineManager.getPlayerBlock(p, drop)))
                            .replace("#max#", String.valueOf(MineManager.getMaxBlock(p)));

                        // Sử dụng PlayerCompatibility để gửi title an toàn
                        PlayerCompatibility.sendTitle(p, title, subtitle, 10, 70, 20);
                    }
                    
            // Ghi nhận thống kê khai thác
            Bukkit.getScheduler().runTaskAsynchronously(Storage.getStorage(), () -> {
                try {
                    StatsManager.recordMining(p, 1);
                    } catch (Exception ex) {
                        Storage.getStorage().getLogger().log(Level.WARNING, 
                        "Lỗi khi ghi nhận thống kê khai thác cho " + p.getName(), ex);
                    }
            });
            
            return true;
        } else {
            // Thông báo kho đã đầy
            StatsManager.sendStorageFullNotification(p, drop, 
                MineManager.getPlayerBlock(p, drop), MineManager.getMaxBlock(p));
            return false;
        }
    }

    private void updateMineCounter(Player p) {
        UUID playerUUID = p.getUniqueId();
                    int mineCount = mineCountSinceLastSave.getOrDefault(playerUUID, 0) + 1;
                    mineCountSinceLastSave.put(playerUUID, mineCount);
                    
        // Lưu dữ liệu nếu đạt ngưỡng
                    if (mineCount >= SAVE_THRESHOLD) {
                        mineCountSinceLastSave.put(playerUUID, 0);
            // Sử dụng độ trễ lớn hơn và xử lý bất đồng bộ
                        Bukkit.getScheduler().runTaskLaterAsynchronously(Storage.getStorage(), () -> {
                            try {
                                MineManager.savePlayerDataAsync(p);
                            } catch (Exception ex) {
                    Storage.getStorage().getLogger().warning(
                        "Lỗi khi lưu dữ liệu sau khi đào nhiều khối: " + ex.getMessage());
                            }
            }, 10L); // Độ trễ 10 tick (0.5s)
        }
    }

    public void removeItems(Player player, ItemStack itemStack, long amount) {
        final PlayerInventory inv = player.getInventory();
        final ItemStack[] items = inv.getContents();
        int c = 0;
        for (int i = 0; i < items.length; ++i) {
            final ItemStack is = items[i];
            if (is != null) {
                if (itemStack != null) {
                    if (is.isSimilar(itemStack)) {
                        if (c + is.getAmount() > amount) {
                            final long canDelete = amount - c;
                            is.setAmount((int) (is.getAmount() - canDelete));
                            items[i] = is;
                            break;
                        }
                        c += is.getAmount();
                        items[i] = null;
                    }
                }
            }
        }
        inv.setContents(items);
        player.updateInventory();
    }

    private int getDropAmount(Block block) {
        int amount = 0;
        if (block != null) for (ItemStack itemStack : block.getDrops())
            if (itemStack != null) amount += itemStack.getAmount();
        return Math.max(1, amount); // Đảm bảo luôn trả về ít nhất 1
    }

    public boolean isPlacedBlock(Block b) {
        List<MetadataValue> metaDataValues = b.getMetadata("PlacedBlock");
        for (MetadataValue value : metaDataValues) {
            return value.asBoolean();
        }
        return false;
    }

    // Đăng ký listener để xử lý việc chặn drop item
    public static void registerNoDropsListener(Plugin plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener() {
            @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
            public void onBlockBreak(BlockBreakEvent event) {
                Block block = event.getBlock();
                if (block.hasMetadata("NoDrops")) {
                    event.setDropItems(false);
                }
            }
        }, plugin);
    }

    /**
     * Lấy Fortune enchantment với tương thích đa phiên bản
     * @return Fortune enchantment
     */
    private Enchantment getFortuneEnchantment() {
        return com.hongminh54.storage.Utils.MaterialCompatibility.getCompatibleEnchantment("FORTUNE", "LOOT_BONUS_BLOCKS");
    }
}
