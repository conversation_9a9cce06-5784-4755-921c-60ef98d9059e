# Cấu hình GUI chuyển tài nguyên

# Thao tác với người chơi
player_action_gui:
  title: "&8Thao tác với &a{target}"
  size: 27
  
  # Viền trang trí
  decorates:
    slot: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 13, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26
    name: "&r"
    material: GRAY_STAINED_GLASS_PANE
    amount: 1
    # custom-model-data: 1001  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)
    flags:
      ALL: true
  
  # Các nút
  buttons:
    # Nút chuyển một loại tài nguyên
    transfer_single:
      slot: 12
      material: CHEST
      name: "&aChuyển một loại tài nguyên"
      lore:
        - "&7Chuyển một loại tài nguyên cho"
        - "&a{target}"
        - "&7"
        - "&eNhấp để chọn"
      sound: "UI_BUTTON_CLICK:0.5:1.0"
      # custom-model-data: 1002  (<PERSON><PERSON><PERSON> cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)

    # Nút chuyển nhiều loại tài nguyên
    transfer_multi:
      slot: 14
      material: ENDER_CHEST
      name: "&aChuyển nhiều loại tài nguyên"
      lore:
        - "&7Chuyển nhiều loại tài nguyên cho"
        - "&a{target}"
        - "&7"
        - "&eNhấp để chọn"
      sound: "UI_BUTTON_CLICK:0.5:1.0"
      # custom-model-data: 1003  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)

# Cấu hình GUI chuyển một loại tài nguyên
transfer_gui:
  title: "&8Chuyển tài nguyên cho &a{receiver}"
  size: 45
  
  # Âm thanh
  open_sound: "BLOCK_CHEST_OPEN:1.0:1.0"
  close_sound: "BLOCK_CHEST_CLOSE:1.0:1.0"
  click_sound: "UI_BUTTON_CLICK:0.5:1.0"
  success_sound: "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.0"
  fail_sound: "ENTITY_VILLAGER_NO:1.0:1.0"
  
  # Cấu hình viền trang trí
  decorates:
    slot: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 37, 38, 39, 41, 42, 43, 44
    name: "&r"
    material: GRAY_STAINED_GLASS_PANE
    amount: 1
    # custom-model-data: 1004  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)
    flags:
      ALL: true
  
  # Cấu hình các nút số lượng
  quantity_buttons:
    # Nút số lượng 1
    quantity_1:
      slot: 11
      material: PAPER
      name: "&a1 {material}"
      lore:
        - "&7Chuyển &f1 &b{material}"
        - "&7cho &a{receiver}"
        - "&7"
        - "&eNhấp để chuyển"
      amount: 1
      sound: "UI_BUTTON_CLICK:0.5:1.0"
      # custom-model-data: 1006  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)

    # Nút số lượng 5
    quantity_5:
      slot: 13
      material: PAPER
      name: "&a5 {material}"
      lore:
        - "&7Chuyển &f5 &b{material}"
        - "&7cho &a{receiver}"
        - "&7"
        - "&eNhấp để chuyển"
      amount: 5
      sound: "UI_BUTTON_CLICK:0.5:1.0"
      # custom-model-data: 1007  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)

    # Nút số lượng 10
    quantity_10:
      slot: 15
      material: PAPER
      name: "&a10 {material}"
      lore:
        - "&7Chuyển &f10 &b{material}"
        - "&7cho &a{receiver}"
        - "&7"
        - "&eNhấp để chuyển"
      amount: 10
      sound: "UI_BUTTON_CLICK:0.5:1.0"
      # custom-model-data: 1008  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)
      
    # Nút số lượng 32
    quantity_32:
      slot: 20
      material: PAPER
      name: "&a32 {material}"
      lore:
        - "&7Chuyển &f32 &b{material}"
        - "&7cho &a{receiver}"
        - "&7"
        - "&eNhấp để chuyển"
      amount: 32
      sound: "UI_BUTTON_CLICK:0.5:1.0"
      # custom-model-data: 1009  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)

    # Nút số lượng 64
    quantity_64:
      slot: 24
      material: PAPER
      name: "&a64 {material}"
      lore:
        - "&7Chuyển &f64 &b{material}"
        - "&7cho &a{receiver}"
        - "&7"
        - "&eNhấp để chuyển"
      amount: 64
      sound: "UI_BUTTON_CLICK:0.5:1.0"
      # custom-model-data: 1010  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)

    # Nút tùy chỉnh
    custom:
      slot: 29
      material: ANVIL
      name: "&aTùy chỉnh số lượng"
      lore:
        - "&7Nhập số lượng muốn chuyển"
        - "&7"
        - "&eNhấp để nhập"
      sound: "UI_BUTTON_CLICK:0.5:1.0"
      # custom-model-data: 1011  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)

    # Nút chuyển tất cả
    all:
      slot: 33
      material: CHEST
      name: "&aChuyển tất cả"
      lore:
        - "&7Chuyển tất cả &b{material}"
        - "&7cho &a{receiver}"
        - "&7Số lượng: &f{amount}"
        - "&7"
        - "&eNhấp để chuyển tất cả"
      sound: "UI_BUTTON_CLICK:0.5:1.0"
      # custom-model-data: 1012  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)
  
  # Thông tin người chơi
  player_info:
    slot: 4
    material: EMERALD
    name: "&a&l{receiver}"
    lore:
      - "&7Người nhận: &a{receiver}"
      - "&7Tài nguyên: &b{material}"
      - "&7Số lượng hiện có: &f{current_amount}"
      - "&7"
      - "&eLựa chọn số lượng muốn chuyển"
    # custom-model-data: 1005  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)
  
  # Nút điều khiển
  control_buttons:
    # Nút hủy
    cancel:
      slot: 40
      material: BARRIER
      name: "&c&lHủy"
      lore:
        - "&7Quay lại kho cá nhân"
        - "&7"
        - "&eNhấp để hủy"
      sound: "UI_BUTTON_CLICK:0.5:1.0"
      # custom-model-data: 1013  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)

# Cấu hình GUI chuyển nhiều loại tài nguyên
multi_transfer_gui:
  title: "&8Chuyển nhiều tài nguyên cho &a{receiver}"
  size: 54
  
  # Âm thanh
  open_sound: "BLOCK_CHEST_OPEN:1.0:1.0"
  close_sound: "BLOCK_CHEST_CLOSE:1.0:1.0"
  click_sound: "UI_BUTTON_CLICK:0.5:1.0"
  success_sound: "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.0"
  fail_sound: "ENTITY_VILLAGER_NO:1.0:1.0"
  
  # Cấu hình viền trang trí
  decorates:
    slot: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53
    name: "&r"
    material: GRAY_STAINED_GLASS_PANE
    amount: 1
    # custom-model-data: 1014  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)
    flags:
      ALL: true

  # Thông tin người chơi
  player_info:
    slot: 4
    material: EMERALD
    name: "&a&l{receiver}"
    lore:
      - "&7Người nhận: &a{receiver}"
      - "&7"
      - "&eLựa chọn tài nguyên muốn chuyển"
    # custom-model-data: 1015  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)
  
  # Khu vực hiển thị tài nguyên
  resource_area:
    slots: 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43
    
  # Nút điều khiển
  control_buttons:
    # Nút xác nhận
    confirm:
      slot: 49
      material: EMERALD_BLOCK
      name: "&a&lXác nhận chuyển"
      lore:
        - "&7Chuyển các tài nguyên đã chọn"
        - "&7cho &a{receiver}"
        - "&7"
        - "&eNhấp để xác nhận"
      sound: "ENTITY_EXPERIENCE_ORB_PICKUP:0.5:1.0"
      # custom-model-data: 1016  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)

    # Nút hủy
    cancel:
      slot: 45
      material: BARRIER
      name: "&c&lHủy"
      lore:
        - "&7Quay lại kho cá nhân"
        - "&7"
        - "&eNhấp để hủy"
      sound: "UI_BUTTON_CLICK:0.5:1.0"
      # custom-model-data: 1017  (Dành cho 1.14+ nếu máy chủ của bạn có hỗ trợ resource pack)
