package com.hongminh54.storage.Database;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Level;

import com.hongminh54.storage.Storage;

public class SQLite extends Database {

    public String SQLiteCreateTokensTable = "CREATE TABLE IF NOT EXISTS PlayerData (" +
            "`player` VARCHAR(36) NOT NULL," +
            "`data` TEXT DEFAULT '{}'," +
            "`max` BIGINT NOT NULL," +
            "`statsData` TEXT DEFAULT '{}'," +
            "`auto_pickup` BOOLEAN DEFAULT 1," +
            "PRIMARY KEY (`player`)" +
            ");";
    String dbname;
    
    // Đơn giản hóa - chỉ giữ biến cần thiết
    private static final Object connectionLock = new Object();

    public SQLite(Storage instance) {
        super(instance);
        dbname = "PlayerData";
    }

    /**
     * Thiết lập PRAGMA ban đầu khi tạo connection mới (an toàn)
     * @param connection Kết nối mới tạo
     */
    private void setupInitialPragmas(Connection connection) {
        if (connection == null) return;

        try (Statement stmt = connection.createStatement()) {
            // Thiết lập các PRAGMA cơ bản khi connection mới được tạo
            stmt.execute("PRAGMA busy_timeout = 30000");
            stmt.execute("PRAGMA journal_mode = WAL");
            stmt.execute("PRAGMA synchronous = NORMAL");
            stmt.execute("PRAGMA cache_size = 10000");
            stmt.execute("PRAGMA temp_store = memory");
        } catch (SQLException e) {
            // Log warning nhưng không crash
            Storage.getStorage().getLogger().warning("Không thể thiết lập PRAGMA SQLite: " + e.getMessage());
        }
    }

    /**
     * Thiết lập PRAGMA cơ bản và an toàn cho SQLite (trong transaction)
     * @param connection Kết nối cần thiết lập
     */
    private void setupBasicPragmas(Connection connection) {
        if (connection == null) return;

        try (Statement stmt = connection.createStatement()) {
            // Đảm bảo không có transaction đang hoạt động
            if (!connection.getAutoCommit()) {
                Storage.getStorage().getLogger().fine("Bỏ qua thiết lập PRAGMA vì đang trong transaction");
                return;
            }

            // Chỉ thiết lập busy_timeout vì nó an toàn trong mọi trường hợp
            stmt.execute("PRAGMA busy_timeout = 30000");
        } catch (SQLException e) {
            // Bỏ qua lỗi PRAGMA để tránh gây lỗi transaction
            Storage.getStorage().getLogger().fine("Bỏ qua thiết lập PRAGMA: " + e.getMessage());
        }
    }

    @Override
    public Connection getSQLConnection() {
        synchronized (connectionLock) {
            try {
                // Kiểm tra kết nối hiện tại
                if (connection != null && !connection.isClosed()) {
                    // Test kết nối đơn giản
                    try (Statement testStmt = connection.createStatement()) {
                        testStmt.setQueryTimeout(5);
                        testStmt.executeQuery("SELECT 1").close();
                        return connection;
                    } catch (SQLException testEx) {
                        // Kết nối không hoạt động, tạo mới
                        Storage.getStorage().getLogger().fine("Kết nối cũ không hoạt động, tạo mới: " + testEx.getMessage());
                    }
                }
            } catch (SQLException e) {
                // Kết nối bị lỗi, tạo mới
                Storage.getStorage().getLogger().fine("Lỗi kết nối: " + e.getMessage());
            }

            // Đóng kết nối cũ nếu có
            try {
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException closeEx) {
                // Bỏ qua lỗi đóng
            }
            connection = null;

            try {
                File dataFolder = new File(Storage.getStorage().getDataFolder(), dbname + ".db");
                if (!dataFolder.exists()) {
                    dataFolder.createNewFile();
                }

                Class.forName("org.sqlite.JDBC");
                connection = DriverManager.getConnection("jdbc:sqlite:" + dataFolder);

                // Thiết lập PRAGMA một lần khi tạo connection mới
                setupInitialPragmas(connection);

                // Thiết lập timeout cho statement
                try (Statement stmt = connection.createStatement()) {
                    stmt.setQueryTimeout(30); // 30 giây timeout
                } catch (SQLException timeoutEx) {
                    Storage.getStorage().getLogger().fine("Không thể thiết lập timeout: " + timeoutEx.getMessage());
                }

                return connection;
            } catch (SQLException | ClassNotFoundException | IOException ex) {
                Storage.getStorage().getLogger().log(Level.SEVERE, "SQLite connection error", ex);
                return null;
            }
        }
    }

    @Override
    public void load() {
        Connection conn = null;
        Statement s = null;
        
        try {
            conn = getSQLConnection();
            
            if (conn == null) {
                Storage.getStorage().getLogger().severe("Không thể kết nối cơ sở dữ liệu để khởi tạo bảng!");
                return;
            }
            
            s = conn.createStatement();
            
            // Thiết lập timeout lớn hơn để tránh lỗi database locked
            s.setQueryTimeout(60); // 60 giây timeout
            
            // Tạo bảng chính nếu chưa tồn tại
            s.executeUpdate(SQLiteCreateTokensTable);
            
            // Kiểm tra và thêm cột statsData nếu cần
            try {
                s.executeUpdate("ALTER TABLE PlayerData ADD COLUMN statsData TEXT DEFAULT '{}'");
                Storage.getStorage().getLogger().info("Added statsData column to the database");
            } catch (SQLException e) {
                // Cột đã tồn tại, bỏ qua lỗi
                if (!e.getMessage().contains("duplicate column name")) {
                    Storage.getStorage().getLogger().warning("Lỗi khi thêm cột statsData: " + e.getMessage());
                }
            }
            
            // Kiểm tra và thêm cột auto_pickup nếu cần
            try {
                s.executeUpdate("ALTER TABLE PlayerData ADD COLUMN auto_pickup BOOLEAN DEFAULT 1");
                Storage.getStorage().getLogger().info("Added auto_pickup column to the database");
            } catch (SQLException e) {
                // Cột đã tồn tại, bỏ qua lỗi
                if (!e.getMessage().contains("duplicate column name")) {
                    Storage.getStorage().getLogger().warning("Lỗi khi thêm cột auto_pickup: " + e.getMessage());
                }
            }
            
        } catch (SQLException e) {
            Storage.getStorage().getLogger().log(Level.SEVERE, "Lỗi khi khởi tạo cơ sở dữ liệu", e);
        } finally {
            try {
                if (s != null) {
                    s.close();
                }
            } catch (SQLException e) {
                Storage.getStorage().getLogger().warning("Không thể đóng statement: " + e.getMessage());
            }
        }
        
        // Khởi tạo connection pool
        initialize();
        Storage.getStorage().getLogger().info("Loaded SQLite Data");
    }

    /**
     * Thực hiện thao tác SQL với retry đơn giản
     * @param operation Thao tác cần thực hiện
     * @param maxRetries Số lần thử tối đa
     * @return true nếu thành công, false nếu thất bại
     */
    public boolean executeWithSimpleRetry(Runnable operation, int maxRetries) {
        for (int i = 0; i <= maxRetries; i++) {
            try {
                operation.run();
                return true;
            } catch (Exception e) {
                String errorMsg = e.getMessage();
                if (errorMsg != null && (errorMsg.contains("locked") || errorMsg.contains("busy"))) {
                    if (i < maxRetries) {
                        Storage.getStorage().getLogger().fine("Database busy, retry " + (i + 1) + "/" + maxRetries);
                        try {
                            Thread.sleep(100 * (i + 1)); // Tăng dần thời gian chờ
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                        continue;
                    }
                }
                Storage.getStorage().getLogger().warning("SQL operation failed: " + e.getMessage());
                break;
            }
        }
        return false;
    }

}

