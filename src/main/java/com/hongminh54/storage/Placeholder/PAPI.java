package com.hongminh54.storage.Placeholder;

import java.util.ArrayList;
import java.util.List;

import com.hongminh54.storage.Manager.SpecialMaterialManager;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.hongminh54.storage.Manager.ItemManager;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.LeaderboardManager;
import com.hongminh54.storage.Utils.LeaderboardManager.LeaderboardEntry;
import com.hongminh54.storage.Utils.Number;
import com.hongminh54.storage.Utils.StatsManager;

import me.clip.placeholderapi.expansion.PlaceholderExpansion;

public class PAPI extends PlaceholderExpansion {
    @Override
    public @NotNull String getIdentifier() {
        return "storage";
    }

    @Override
    public @NotNull String getAuthor() {
        return Storage.getStorage().getDescription().getAuthors().toString();
    }

    @Override
    public @NotNull String getVersion() {
        return Storage.getStorage().getDescription().getVersion();
    }

    @Override
    public boolean persist() {
        return true;
    }

    @Override
    public @Nullable String onPlaceholderRequest(Player p, @NotNull String args) {
        if (p == null) return null;
        if (args.equalsIgnoreCase("status")) {
            return ItemManager.getStatus(p);
        }
        if (args.startsWith("storage_")) {
            String item = args.substring(8);
            return String.valueOf(MineManager.getPlayerBlock(p, item));
        }
        if (args.equalsIgnoreCase("max_storage")) {
            return String.valueOf(MineManager.getMaxBlock(p));
        }
        if (args.startsWith("price_")) {
            String material = args.substring(6);
            ConfigurationSection section = File.getConfig().getConfigurationSection("worth");
            if (section != null) {
                List<String> sell_list = new ArrayList<>(section.getKeys(false));
                if (sell_list.contains(material)) {
                    int worth = section.getInt(material);
                    return String.valueOf(worth);
                }
            }
        }
        
        // Placeholders cho thống kê
        if (args.equalsIgnoreCase("stats_total_mined")) {
            return String.valueOf(StatsManager.getTotalMined(p));
        }
        if (args.equalsIgnoreCase("stats_total_deposited")) {
            return String.valueOf(StatsManager.getTotalDeposited(p));
        }
        if (args.equalsIgnoreCase("stats_total_withdrawn")) {
            return String.valueOf(StatsManager.getTotalWithdrawn(p));
        }
        if (args.equalsIgnoreCase("stats_total_sold")) {
            return String.valueOf(StatsManager.getTotalSold(p));
        }
        
        // Placeholders cho sự kiện và khoáng sản đặc biệt
        if (args.equalsIgnoreCase("event_active")) {
            return com.hongminh54.storage.Events.MiningEvent.getInstance().isActive() ? "true" : "false";
        }
        if (args.equalsIgnoreCase("event_type")) {
            return com.hongminh54.storage.Events.MiningEvent.getInstance().getCurrentEventType().getDisplayName();
        }
        if (args.equalsIgnoreCase("event_time_remaining")) {
            if (!com.hongminh54.storage.Events.MiningEvent.getInstance().isActive()) {
                return "0";
            }
            return String.valueOf(com.hongminh54.storage.Events.MiningEvent.getInstance().getRemainingTime());
        }
        if (args.equalsIgnoreCase("event_time_remaining_formatted")) {
            if (!com.hongminh54.storage.Events.MiningEvent.getInstance().isActive()) {
                return "00:00";
            }
            int seconds = com.hongminh54.storage.Events.MiningEvent.getInstance().getRemainingTime();
            int minutes = seconds / 60;
            int remainingSeconds = seconds % 60;
            return String.format("%02d:%02d", minutes, remainingSeconds);
        }
        
        // Placeholder cho khoáng sản đặc biệt
        if (args.equalsIgnoreCase("specialmaterial_event_boost")) {
            if (!com.hongminh54.storage.Events.MiningEvent.getInstance().isActive()) {
                return "0%";
            }
            double multiplier = SpecialMaterialManager.getEventMultiplier();
            if (multiplier <= 1.0) {
                return "0%";
            }
            return String.format("+%.1f%%", (multiplier - 1.0) * 100);
        }
        
        // Placeholders cho bảng xếp hạng
        
        // Xếp hạng người chơi hiện tại
        if (args.equalsIgnoreCase("rank_mined")) {
            return String.valueOf(LeaderboardManager.getPlayerRank(p, LeaderboardManager.TYPE_MINED));
        }
        if (args.equalsIgnoreCase("rank_deposited")) {
            return String.valueOf(LeaderboardManager.getPlayerRank(p, LeaderboardManager.TYPE_DEPOSITED));
        }
        if (args.equalsIgnoreCase("rank_withdrawn")) {
            return String.valueOf(LeaderboardManager.getPlayerRank(p, LeaderboardManager.TYPE_WITHDRAWN));
        }
        if (args.equalsIgnoreCase("rank_sold")) {
            return String.valueOf(LeaderboardManager.getPlayerRank(p, LeaderboardManager.TYPE_SOLD));
        }
        
        // Định dạng xếp hạng (hiển thị "Không xếp hạng" nếu = 0)
        if (args.equalsIgnoreCase("rank_mined_formatted")) {
            int rank = LeaderboardManager.getPlayerRank(p, LeaderboardManager.TYPE_MINED);
            return rank > 0 ? "#" + rank : "Không xếp hạng";
        }
        if (args.equalsIgnoreCase("rank_deposited_formatted")) {
            int rank = LeaderboardManager.getPlayerRank(p, LeaderboardManager.TYPE_DEPOSITED);
            return rank > 0 ? "#" + rank : "Không xếp hạng";
        }
        if (args.equalsIgnoreCase("rank_withdrawn_formatted")) {
            int rank = LeaderboardManager.getPlayerRank(p, LeaderboardManager.TYPE_WITHDRAWN);
            return rank > 0 ? "#" + rank : "Không xếp hạng";
        }
        if (args.equalsIgnoreCase("rank_sold_formatted")) {
            int rank = LeaderboardManager.getPlayerRank(p, LeaderboardManager.TYPE_SOLD);
            return rank > 0 ? "#" + rank : "Không xếp hạng";
        }
        
        // Lấy tên người chơi tại vị trí cụ thể trên bảng xếp hạng
        if (args.startsWith("top_mined_player_")) {
            try {
                int position = Integer.parseInt(args.substring(16)) - 1; // Chuyển về index 0-based
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_MINED, position + 1);
                return position < entries.size() ? entries.get(position).getDisplayName() : "---";
            } catch (NumberFormatException e) {
                return "Lỗi định dạng";
            }
        }
        if (args.startsWith("top_deposited_player_")) {
            try {
                int position = Integer.parseInt(args.substring(20)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_DEPOSITED, position + 1);
                return position < entries.size() ? entries.get(position).getDisplayName() : "---";
            } catch (NumberFormatException e) {
                return "Lỗi định dạng";
            }
        }
        if (args.startsWith("top_withdrawn_player_")) {
            try {
                int position = Integer.parseInt(args.substring(20)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_WITHDRAWN, position + 1);
                return position < entries.size() ? entries.get(position).getDisplayName() : "---";
            } catch (NumberFormatException e) {
                return "Lỗi định dạng";
            }
        }
        if (args.startsWith("top_sold_player_")) {
            try {
                int position = Integer.parseInt(args.substring(15)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_SOLD, position + 1);
                return position < entries.size() ? entries.get(position).getDisplayName() : "---";
            } catch (NumberFormatException e) {
                return "Lỗi định dạng";
            }
        }
        
        // Lấy giá trị tại vị trí cụ thể trên bảng xếp hạng
        if (args.startsWith("top_mined_value_")) {
            try {
                int position = Integer.parseInt(args.substring(15)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_MINED, position + 1);
                return position < entries.size() ? String.valueOf(entries.get(position).getValue()) : "0";
            } catch (NumberFormatException e) {
                return "0";
            }
        }
        if (args.startsWith("top_deposited_value_")) {
            try {
                int position = Integer.parseInt(args.substring(19)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_DEPOSITED, position + 1);
                return position < entries.size() ? String.valueOf(entries.get(position).getValue()) : "0";
            } catch (NumberFormatException e) {
                return "0";
            }
        }
        if (args.startsWith("top_withdrawn_value_")) {
            try {
                int position = Integer.parseInt(args.substring(19)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_WITHDRAWN, position + 1);
                return position < entries.size() ? String.valueOf(entries.get(position).getValue()) : "0";
            } catch (NumberFormatException e) {
                return "0";
            }
        }
        if (args.startsWith("top_sold_value_")) {
            try {
                int position = Integer.parseInt(args.substring(14)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_SOLD, position + 1);
                return position < entries.size() ? String.valueOf(entries.get(position).getValue()) : "0";
            } catch (NumberFormatException e) {
                return "0";
            }
        }
        
        // Định dạng giá trị xếp hạng (với dấu phân cách)
        if (args.startsWith("top_mined_value_formatted_")) {
            try {
                int position = Integer.parseInt(args.substring(25)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_MINED, position + 1);
                return position < entries.size() ? Number.formatWithSeparator(entries.get(position).getValue()) : "0";
            } catch (NumberFormatException e) {
                return "0";
            }
        }
        if (args.startsWith("top_deposited_value_formatted_")) {
            try {
                int position = Integer.parseInt(args.substring(29)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_DEPOSITED, position + 1);
                return position < entries.size() ? Number.formatWithSeparator(entries.get(position).getValue()) : "0";
            } catch (NumberFormatException e) {
                return "0";
            }
        }
        if (args.startsWith("top_withdrawn_value_formatted_")) {
            try {
                int position = Integer.parseInt(args.substring(29)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_WITHDRAWN, position + 1);
                return position < entries.size() ? Number.formatWithSeparator(entries.get(position).getValue()) : "0";
            } catch (NumberFormatException e) {
                return "0";
            }
        }
        if (args.startsWith("top_sold_value_formatted_")) {
            try {
                int position = Integer.parseInt(args.substring(24)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_SOLD, position + 1);
                return position < entries.size() ? Number.formatWithSeparator(entries.get(position).getValue()) : "0";
            } catch (NumberFormatException e) {
                return "0";
            }
        }
        
        // Định dạng gọn giá trị xếp hạng (rút gọn thành K, M, B)
        if (args.startsWith("top_mined_value_compact_")) {
            try {
                int position = Integer.parseInt(args.substring(23)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_MINED, position + 1);
                return position < entries.size() ? Number.formatCompact(entries.get(position).getValue()) : "0";
            } catch (NumberFormatException e) {
                return "0";
            }
        }
        if (args.startsWith("top_deposited_value_compact_")) {
            try {
                int position = Integer.parseInt(args.substring(27)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_DEPOSITED, position + 1);
                return position < entries.size() ? Number.formatCompact(entries.get(position).getValue()) : "0";
            } catch (NumberFormatException e) {
                return "0";
            }
        }
        if (args.startsWith("top_withdrawn_value_compact_")) {
            try {
                int position = Integer.parseInt(args.substring(27)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_WITHDRAWN, position + 1);
                return position < entries.size() ? Number.formatCompact(entries.get(position).getValue()) : "0";
            } catch (NumberFormatException e) {
                return "0";
            }
        }
        if (args.startsWith("top_sold_value_compact_")) {
            try {
                int position = Integer.parseInt(args.substring(22)) - 1;
                List<LeaderboardEntry> entries = LeaderboardManager.getCachedLeaderboard(LeaderboardManager.TYPE_SOLD, position + 1);
                return position < entries.size() ? Number.formatCompact(entries.get(position).getValue()) : "0";
            } catch (NumberFormatException e) {
                return "0";
            }
        }
        
        return null;
    }
}
