package com.hongminh54.storage.Listeners;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;

import com.hongminh54.storage.Manager.GemEffectManager;
import com.hongminh54.storage.Utils.Chat;

/**
 * Listener xử lý kích hoạt khoáng sản đặc biệt có hiệu ứng
 */
public class GemEffectListener implements Listener {

    private final GemEffectManager gemEffectManager;
    
    public GemEffectListener() {
        this.gemEffectManager = GemEffectManager.getInstance();
    }
    
    /**
     * Xử lý sự kiện khi người chơi click chuột phải vào item
     */
    @EventHandler(priority = EventPriority.NORMAL)
    public void onPlayerInteract(PlayerInteractEvent event) {
        // Chỉ xử lý khi click chuột phải vào item
        if (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        
        // Kiểm tra xem người chơi có cầm item không
        ItemStack item = event.getItem();
        if (item == null || item.getType() == Material.AIR) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // Kiểm tra xem item có phải là Ngọc Quý Cuối Tuần không
        if (isWeekendGem(item)) {
            event.setCancelled(true); // Không để sự kiện tiếp tục xử lý
            
            // Nếu người chơi đã có hiệu ứng Ngọc Quý Cuối Tuần
            if (gemEffectManager.hasWeekendGemEffect(player)) {
                // Làm mới thời gian hiệu ứng
                gemEffectManager.activateWeekendGemEffect(player);
            } else {
                // Kích hoạt hiệu ứng mới
                gemEffectManager.activateWeekendGemEffect(player);
            }
            
            // Giảm số lượng item đi 1
            if (item.getAmount() > 1) {
                item.setAmount(item.getAmount() - 1);
            } else {
                // Xóa item nếu chỉ còn 1
                player.getInventory().setItemInMainHand(null);
            }
            
            // Cập nhật inventory
            player.updateInventory();
        }
    }
    
    /**
     * Kiểm tra xem item có phải là Ngọc Quý Cuối Tuần không
     * @param item Item cần kiểm tra
     * @return true nếu là Ngọc Quý Cuối Tuần
     */
    private boolean isWeekendGem(ItemStack item) {
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return false;
        }
        
        // Kiểm tra tên hiển thị
        String displayName = Chat.stripColor(item.getItemMeta().getDisplayName());
        
        // Kiểm tra material và datavalue
        return item.getType() == Material.EMERALD && 
               displayName.contains("Ngọc Quý Cuối Tuần");
    }
} 