package com.hongminh54.storage.compatibility;

import org.bukkit.entity.Player;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import org.bukkit.Bukkit;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;

/**
 * Lớp hỗ trợ tương thích Player API cho Minecraft 1.12.2 - 1.21.x
 * <PERSON><PERSON> lý các vấn đề tương thích với action bar, title, subtitle, boss bar, v.v.
 */
public class PlayerCompatibility {
    
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_20_5_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(20) && 
                                                       nmsAssistant.getNMSVersion().getRevision() >= 5;
    
    /**
     * Gửi action bar message một cách an toàn cho tất cả phiên bản
     * 
     * @param player Người chơi nhận message
     * @param message Nội dung message
     */
    public static void sendActionBar(Player player, String message) {
        if (player == null || message == null) {
            return;
        }
        
        try {
            if (IS_PRE_116) {
                // Phiên bản 1.12.2 - 1.15.x: Sử dụng Spigot API
                sendActionBarLegacy(player, message);
            } else {
                // Phiên bản 1.16+: Sử dụng Adventure API hoặc Spigot API
                sendActionBarModern(player, message);
            }
        } catch (Exception e) {
            // Fallback: gửi message thông thường
            player.sendMessage(Chat.colorizewp(message));
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể gửi action bar: " + e.getMessage());
            }
        }
    }
    
    /**
     * Gửi title và subtitle một cách an toàn
     * 
     * @param player Người chơi
     * @param title Tiêu đề chính
     * @param subtitle Tiêu đề phụ
     * @param fadeIn Thời gian fade in (ticks)
     * @param stay Thời gian hiển thị (ticks)
     * @param fadeOut Thời gian fade out (ticks)
     */
    public static void sendTitle(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        if (player == null) {
            return;
        }
        
        try {
            if (IS_1_20_5_OR_HIGHER) {
                // Phiên bản 1.20.5+: Sử dụng Adventure API
                sendTitleAdventure(player, title, subtitle, fadeIn, stay, fadeOut);
            } else {
                // Phiên bản cũ hơn: Sử dụng Bukkit API
                sendTitleLegacy(player, title, subtitle, fadeIn, stay, fadeOut);
            }
        } catch (Exception e) {
            // Fallback: gửi message thông thường
            if (title != null && !title.isEmpty()) {
                player.sendMessage(Chat.colorizewp(title));
            }
            if (subtitle != null && !subtitle.isEmpty()) {
                player.sendMessage(Chat.colorizewp(subtitle));
            }
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể gửi title: " + e.getMessage());
            }
        }
    }
    
    /**
     * Tạo boss bar một cách an toàn
     * 
     * @param title Tiêu đề boss bar
     * @param color Màu sắc
     * @param style Kiểu dáng
     * @param progress Tiến độ (0.0 - 1.0)
     * @return BossBar hoặc null nếu không hỗ trợ
     */
    public static BossBar createBossBar(String title, BarColor color, BarStyle style, double progress) {
        try {
            if (IS_PRE_113) {
                // Phiên bản 1.12.2 không hỗ trợ BossBar API
                return null;
            }
            
            BossBar bossBar = Bukkit.createBossBar(Chat.colorizewp(title), color, style);
            bossBar.setProgress(Math.max(0.0, Math.min(1.0, progress)));
            return bossBar;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể tạo boss bar: " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * Gửi experience một cách an toàn
     * 
     * @param player Người chơi
     * @param amount Số lượng experience
     */
    public static void giveExperience(Player player, int amount) {
        if (player == null || amount <= 0) {
            return;
        }
        
        try {
            player.giveExp(amount);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể thêm experience: " + e.getMessage());
            }
        }
    }
    
    /**
     * Gửi action bar cho phiên bản cũ (1.12.2 - 1.15.x)
     */
    private static void sendActionBarLegacy(Player player, String message) {
        try {
            // Sử dụng Spigot API
            player.spigot().sendMessage(
                net.md_5.bungee.api.ChatMessageType.ACTION_BAR,
                net.md_5.bungee.api.chat.TextComponent.fromLegacyText(Chat.colorizewp(message))
            );
        } catch (Exception e) {
            // Fallback với reflection cho các phiên bản rất cũ
            sendActionBarReflection(player, message);
        }
    }
    
    /**
     * Gửi action bar cho phiên bản mới (1.16+)
     */
    private static void sendActionBarModern(Player player, String message) {
        try {
            // Thử với Spigot API trước
            player.spigot().sendMessage(
                net.md_5.bungee.api.ChatMessageType.ACTION_BAR,
                net.md_5.bungee.api.chat.TextComponent.fromLegacyText(Chat.colorizewp(message))
            );
        } catch (Exception e) {
            // Fallback với reflection
            sendActionBarReflection(player, message);
        }
    }
    
    /**
     * Gửi action bar bằng reflection (fallback method)
     */
    private static void sendActionBarReflection(Player player, String message) {
        try {
            // Sử dụng reflection để gửi action bar
            String version = Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];
            Class<?> craftPlayerClass = Class.forName("org.bukkit.craftbukkit." + version + ".entity.CraftPlayer");
            Object craftPlayer = craftPlayerClass.cast(player);
            
            Class<?> packetPlayOutChatClass = Class.forName("net.minecraft.server." + version + ".PacketPlayOutChat");
            Class<?> chatComponentTextClass = Class.forName("net.minecraft.server." + version + ".ChatComponentText");
            Class<?> chatMessageTypeClass = Class.forName("net.minecraft.server." + version + ".ChatMessageType");
            
            Object chatComponent = chatComponentTextClass.getConstructor(String.class).newInstance(Chat.colorizewp(message));
            Object chatMessageType = chatMessageTypeClass.getEnumConstants()[2]; // ACTION_BAR
            
            Object packet = packetPlayOutChatClass.getConstructor(chatComponentTextClass, chatMessageTypeClass)
                    .newInstance(chatComponent, chatMessageType);
            
            Object handle = craftPlayerClass.getMethod("getHandle").invoke(craftPlayer);
            Object playerConnection = handle.getClass().getField("playerConnection").get(handle);
            playerConnection.getClass().getMethod("sendPacket", Class.forName("net.minecraft.server." + version + ".Packet"))
                    .invoke(playerConnection, packet);
        } catch (Exception e) {
            // Cuối cùng fallback thành message thông thường
            player.sendMessage(Chat.colorizewp(message));
        }
    }
    
    /**
     * Gửi title cho phiên bản cũ
     */
    private static void sendTitleLegacy(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        try {
            player.sendTitle(
                title != null ? Chat.colorizewp(title) : "",
                subtitle != null ? Chat.colorizewp(subtitle) : "",
                fadeIn, stay, fadeOut
            );
        } catch (Exception e) {
            throw new RuntimeException("Failed to send legacy title", e);
        }
    }
    
    /**
     * Gửi title cho phiên bản mới (1.20.5+)
     */
    private static void sendTitleAdventure(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        try {
            // Thử với API cũ trước
            sendTitleLegacy(player, title, subtitle, fadeIn, stay, fadeOut);
        } catch (Exception e) {
            // Nếu API cũ không hoạt động, sử dụng reflection cho Adventure API
            sendTitleReflection(player, title, subtitle, fadeIn, stay, fadeOut);
        }
    }
    
    /**
     * Gửi title bằng reflection (fallback method)
     */
    private static void sendTitleReflection(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        try {
            // Fallback đơn giản: gửi message thông thường
            if (title != null && !title.isEmpty()) {
                player.sendMessage(Chat.colorizewp("§l" + title));
            }
            if (subtitle != null && !subtitle.isEmpty()) {
                player.sendMessage(Chat.colorizewp(subtitle));
            }
        } catch (Exception e) {
            // Bỏ qua lỗi
        }
    }
    
    /**
     * Kiểm tra xem player có online không một cách an toàn
     * 
     * @param player Người chơi cần kiểm tra
     * @return true nếu player online
     */
    public static boolean isPlayerOnline(Player player) {
        if (player == null) {
            return false;
        }
        
        try {
            return player.isOnline();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Lấy ping của player một cách an toàn
     * 
     * @param player Người chơi
     * @return Ping hoặc -1 nếu không thể lấy
     */
    public static int getPlayerPing(Player player) {
        if (player == null) {
            return -1;
        }
        
        try {
            // Thử với Spigot API (1.17+)
            return player.getPing();
        } catch (Exception e) {
            // Fallback với reflection cho phiên bản cũ
            return getPlayerPingReflection(player);
        }
    }
    
    /**
     * Lấy ping bằng reflection (fallback method)
     */
    private static int getPlayerPingReflection(Player player) {
        try {
            String version = Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];
            Class<?> craftPlayerClass = Class.forName("org.bukkit.craftbukkit." + version + ".entity.CraftPlayer");
            Object craftPlayer = craftPlayerClass.cast(player);
            Object handle = craftPlayerClass.getMethod("getHandle").invoke(craftPlayer);
            return (int) handle.getClass().getField("ping").get(handle);
        } catch (Exception e) {
            return -1;
        }
    }
}
