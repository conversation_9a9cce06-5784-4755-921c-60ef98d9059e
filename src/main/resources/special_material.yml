# Cấu hình khoáng sản đặc biệt
# Hướng dẫn sử dụng:
# 
# 1. <PERSON><PERSON> thêm khoáng sản đặc biệt mới:
#    a) Tạo một mục mới trong phần special_materials với key là ID của khoáng sản
#    b) from_block: Loại block mà khoáng sản đặc biệt có thể nhận được từ đó
#       - Có thể dùng tên cụ thể như "GOLD_ORE", "IRON_ORE", "COAL_ORE"...
#       - Hoặc dùng "ALL" để áp dụng cho tất cả các loại khoáng sản
#    c) display_name: Tên hiển thị của khoáng sản (hỗ trợ mã màu)
#    d) lore: Danh sách các dòng mô tả (hỗ trợ mã màu)
#    e) material: Loại vật phẩm Minecraft (GOLD_INGOT, DIAMOND, EMERALD...)
#    f) glow: true/false - <PERSON><PERSON> hiệu ứng phát sáng không
#    g) chance: Tỉ lệ rơi khoáng sản (%)
#    h) custom_model_data: S<PERSON> nguyên để sử dụng với resourcepack (để 0 hoặc bỏ trống nếu không dùng)
#    i) event_only: Loại sự kiện mà khoáng sản chỉ xuất hiện trong đó
#       - Có thể là: DOUBLE_DROP, FORTUNE_BOOST, RARE_MATERIALS, COMMUNITY_GOAL
#       - Không điền nếu khoáng sản có thể xuất hiện mọi lúc
#    j) effects: Hiệu ứng khi nhận được (particle, sound)
# 
# 2. Định dạng hiệu ứng:
#    a) Âm thanh: "TÊN_ÂM_THANH:ÂM_LƯỢNG:TỐC_ĐỘ"
#       Ví dụ: "ENTITY_PLAYER_LEVELUP:0.7:1.2"
#    b) Hạt: "TÊN_HẠT:OFFSET_X:OFFSET_Y:OFFSET_Z:TỐC_ĐỘ:SỐ_LƯỢNG"
#       Ví dụ: "SPELL_WITCH:0.3:0.5:0.3:0.05:15"
# 
# 3. Lệnh liên quan:
#    /kho specialmaterial list - Xem danh sách khoáng sản đặc biệt
#    /kho specialmaterial give <người chơi> <loại> [số lượng] [hiệu ứng] - Cấp khoáng sản đặc biệt
#    /kho specialmaterial reload - Tải lại cấu hình

enabled: true

# Hiệu ứng khi nhận được khoáng sản đặc biệt
effects:
  # Hiệu ứng âm thanh khi nhận được khoáng sản đặc biệt (định dạng: SOUND:VOLUME:PITCH)
  sound: "ENTITY_PLAYER_LEVELUP:0.7:1.2"
  # Hiệu ứng hạt khi nhận được khoáng sản đặc biệt (định dạng: PARTICLE:OFFSET_X:OFFSET_Y:OFFSET_Z:SPEED:COUNT)
  particle: "SPELL_WITCH:0.3:0.5:0.3:0.05:15"

# Cấu hình thông báo
notifications:
  # Thông báo khi sự kiện bắt đầu và kết thúc
  event:
    # Thông báo khi tỷ lệ khoáng sản tăng lên trong sự kiện
    start: "&e&l⚡ &eTỷ lệ rơi khoáng sản đặc biệt tăng &f%rate%% &etrong sự kiện này!"
    # Thông báo khi sự kiện kết thúc
    end: "&e&l✧ &eTỷ lệ rơi khoáng sản đặc biệt đã trở về bình thường."
    # Thông báo chi tiết cho sự kiện RARE_MATERIALS
    rare_materials:
      - "&d&l✧ &dSự kiện &5Khoáng sản quý hiếm &dđang diễn ra!"
      - "&d✧ &fBạn có thể nhận được &d&lNgọc Quý Cuối Tuần &fkhi đào bất kỳ khoáng sản nào."
      - "&d✧ &fKhoáng sản này chỉ có thể nhận được trong sự kiện cuối tuần."
  
  # Thông báo khi nhận được khoáng sản đặc biệt
  obtain:
    # Có hiển thị thông báo không khi nhận được khoáng sản đặc biệt
    show_message: true
    # Thông báo cho người chơi
    player: "&a&l✓ &aBạn đã tìm thấy &f#item_name#"
    # Thông báo khi nhận được trong sự kiện
    event_bonus: "&eTỷ lệ rơi khoáng sản đặc biệt tăng &f%rate%% &etrong sự kiện &f%event_name%&e!"
    # Thông báo toàn server khi có người nhận được khoáng sản sự kiện
    server_broadcast: "&d&l✧ &d&k!&r &f%player% &dđã tìm thấy &f%item_name% &dtrong sự kiện &5%event_name%&d! &d&k!&r"
    # Có bật thông báo toàn server không
    enable_server_broadcast: true
    # Chỉ thông báo toàn server cho khoáng sản hiếm (từ tỷ lệ rơi bao nhiêu % trở xuống)
    broadcast_rarity_threshold: 1.0
  
  # Thông báo khi sử dụng Ngọc Quý Cuối Tuần
  weekend_gem:
    # Thông báo khi kích hoạt hiệu ứng
    activate: "&d&l✧ &dBạn đã kích hoạt &5Ngọc Quý Cuối Tuần&d! Tốc độ đào tăng &f+20% &dtrong &f30 phút&d!"
    # Thông báo khi hiệu ứng hết tác dụng
    expire: "&d&l✧ &dHiệu ứng của &5Ngọc Quý Cuối Tuần &dđã hết tác dụng."
    # Thông báo khi người chơi đã có hiệu ứng
    already_active: "&d&l✧ &dBạn đã đang có hiệu ứng của &5Ngọc Quý Cuối Tuần&d!"
    # Thông báo khi làm mới thời gian hiệu ứng
    refresh: "&d&l✧ &dBạn đã làm mới thời gian hiệu ứng &5Ngọc Quý Cuối Tuần &f+30 phút&d!"

# Cấu hình hiệu ứng sử dụng Ngọc Quý Cuối Tuần
gem_effects:
  weekend_gem:
    # Thời gian hiệu ứng (phút)
    duration: 30
    # Tăng tốc độ đào (%)
    mining_speed_boost: 20
    # Hiệu ứng kích hoạt
    activate_effects:
      sound: "ENTITY_ENDER_DRAGON_GROWL:0.5:1.2"
      particle: "PORTAL:0.4:0.6:0.4:0.15:40"
    # Hiệu ứng trong khi đang có buff
    active_effects:
      # Hiệu ứng hạt đẹp với màu tím/hồng
      particle: "SPELL_WITCH:0.2:0.2:0.2:0.01:3"
      # Khoảng thời gian giữa các lần hiển thị hiệu ứng (ticks)
      # Giá trị thấp hơn = hiệu ứng mượt hơn (20 tick = 1 giây)
      particle_interval: 40
    # Hiệu ứng khi hết tác dụng
    expire_effects:
      sound: "ENTITY_ILLUSIONER_CAST_SPELL:0.7:0.8"
      particle: "CLOUD:0.4:0.5:0.4:0.02:30"

# Hệ số nhân tỷ lệ rơi khoáng sản đặc biệt trong các sự kiện
# Các giá trị này sẽ nhân với tỷ lệ rơi cơ bản của từng loại khoáng sản
# Bật/tắt tính năng hệ số nhân sự kiện
event_multipliers_enabled: true
event_multipliers:
  # Sự kiện nhân đôi tỷ lệ rơi - daily_double_drop
  DOUBLE_DROP: 0.2
  # Sự kiện tăng cường Fortune
  FORTUNE_BOOST: 0.2
  # Sự kiện khoáng sản quý hiếm - weekend_rare
  RARE_MATERIALS: 0.2
  # Sự kiện mục tiêu cộng đồng
  COMMUNITY_GOAL: 0.2

# Danh sách khoáng sản đặc biệt
special_materials:
  # ID của khoáng sản đặc biệt
  diamond_special:
    # Từ khóa khối cần đào (khớp với tên trong config.yml)
    # Sử dụng "ALL" để áp dụng cho tất cả các loại khoáng sản
    from_block: "DIAMOND_ORE"
    # Tên hiển thị của khoáng sản đặc biệt
    display_name: "&b&lKim Cương Nguyên Chất"
    # Lore của khoáng sản đặc biệt
    lore:
      - "&7Khoáng sản vô cùng quý hiếm"
      - "&7Tìm thấy khi đào kim cương"
      - ""
      - "&e&o(Khoáng sản sự kiện mùa hè)"
    # Vật phẩm để tạo
    material: "DIAMOND"
    # Vật phẩm có phát sáng không
    glow: true
    # Tỉ lệ nhận được khoáng sản đặc biệt (thay đổi theo từng loại)
    # Nếu để trống sẽ sử dụng tỉ lệ mặc định ở trên
    chance: 0.1
    # Custom Model Data cho resourcepack (đặt 0 hoặc bỏ trống để không sử dụng)
    # custom_model_data: 1001
    # Hiệu ứng riêng khi nhận được khoáng sản này (nếu có)
    effects:
      # Nếu muốn dùng hiệu ứng riêng, hãy bỏ comment
      # particle: "SPELL_WITCH:0.3:0.5:0.3:0.05:20"
      # sound: "ENTITY_PLAYER_LEVELUP:1.0:1.5"
    
  emerald_special:
    from_block: "EMERALD_ORE"
    display_name: "&a&lNgọc Lục Bảo Nguyên Chất"
    lore:
      - "&7Khoáng sản vô cùng quý hiếm"
      - "&7Tìm thấy khi đào ngọc lục bảo"
      - ""
      - "&e&o(Khoáng sản sự kiện mùa hè)"
    material: "EMERALD"
    glow: true
    chance: 0.15
    # Custom Model Data cho resourcepack
    custom_model_data: 1001
    
  gold_special:
    from_block: "GOLD_ORE"
    display_name: "&e&lVàng Nguyên Chất"
    lore:
      - "&7Khoáng sản vô cùng quý hiếm"
      - "&7Tìm thấy khi đào vàng"
      - ""
      - "&e&o(Khoáng sản sự kiện mùa hè)"
    material: "GOLD_INGOT"
    glow: true
    chance: 0.25
    
  iron_special:
    from_block: "IRON_ORE"
    display_name: "&f&lSắt Nguyên Chất"
    lore:
      - "&7Khoáng sản vô cùng quý hiếm"
      - "&7Tìm thấy khi đào sắt"
      - ""
      - "&e&o(Khoáng sản sự kiện mùa hè)"
    material: "IRON_INGOT"
    glow: true
    chance: 0.35
    
  coal_special:
    from_block: "COAL_ORE"
    display_name: "&8&lThan Nguyên Chất"
    lore:
      - "&7Khoáng sản vô cùng quý hiếm"
      - "&7Tìm thấy khi đào than"
      - ""
      - "&e&o(Khoáng sản sự kiện mùa hè)"
    material: "COAL"
    glow: true
    chance: 0.5
    
  rare_mineral:
    # Áp dụng cho tất cả loại khoáng sản
    from_block: "ALL"
    display_name: "&6&lTinh chất &e&lMùa Hè" 
    lore:
      - "&7Tinh chất đặc biệt dùng để trao đổi vật phẩm"
      - "&7Có thể nhận được khi đào khoáng sản"
      - ""
      - "&e&o(Khoáng sản sự kiện mùa hè)"
    material: "NETHER_STAR"
    glow: true
    chance: 0.15

  # Khoáng sản đặc biệt chỉ xuất hiện trong sự kiện weekend_rare
  weekend_special_gem:
    from_block: "ALL"
    display_name: "&d&lNgọc Quý Cuối Tuần"
    lore:
      - "&7Khoáng sản cực kỳ hiếm"
      - "&7Chỉ xuất hiện trong sự kiện Cuối Tuần"
      - ""
      - "&d✧ &fTăng tốc độ đào +20% trong 30 phút khi sử dụng"
      - "&d✧ &fCó thể bán với giá cao"
      - ""
      - "&f&o(Khoáng sản sự kiện giới hạn)"
    material: "EMERALD"
    glow: true
    # Custom Model Data cho resourcepack
    custom_model_data: 2001
    # Chỉ có thể nhận được trong sự kiện RARE_MATERIALS
    chance: 0.15
    # Chỉ xuất hiện trong sự kiện RARE_MATERIALS (weekend_rare)
    event_only: "RARE_MATERIALS"
    effects:
      particle: "FLAME:0.4:0.6:0.4:0.12:35"
      sound: "ENTITY_PLAYER_LEVELUP:1.0:2.0"