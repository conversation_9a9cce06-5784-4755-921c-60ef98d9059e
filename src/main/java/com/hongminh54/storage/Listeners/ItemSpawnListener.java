package com.hongminh54.storage.Listeners;

import org.bukkit.Location;
import org.bukkit.block.Block;
import org.bukkit.entity.Item;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.ItemSpawnEvent;
import org.bukkit.metadata.MetadataValue;

import com.hongminh54.storage.Storage;

/**
 * Listener này theo dõi sự kiện ItemSpawnEvent để hủy các vật phẩm rơi ra từ các khối
 * đã được đánh dấu để không rơi vật phẩm.
 * Điều này đặc biệt hữu ích để đảm bảo tương thích với tất cả các phiên bản, đặc biệt là 1.12.2.
 */
public class ItemSpawnListener implements Listener {

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onItemSpawn(ItemSpawnEvent event) {
        Item item = event.getEntity();
        Location itemLoc = item.getLocation();

        // Kiểm tra các khối xung quanh vị trí item spawn (vì item thường rơi trên khối hoặc bên cạnh)
        for (int x = -1; x <= 1; x++) {
            for (int y = -1; y <= 1; y++) {
                for (int z = -1; z <= 1; z++) {
                    Block nearbyBlock = itemLoc.getBlock().getRelative(x, y, z);

                    // Không can thiệp nếu block có metadata LeafEnchantDrop (từ leaf collector enchant)
                    if (hasLeafEnchantDropMetadata(nearbyBlock)) {
                        return; // Cho phép item spawn từ leaf enchant
                    }

                    // Kiểm tra xem block có metadata NoDrops không
                    if (hasNoDropsMetadata(nearbyBlock)) {
                        // Hủy sự kiện spawn item
                        event.setCancelled(true);
                        return;
                    }
                }
            }
        }
    }
    
    /**
     * Kiểm tra xem block có metadata NoDrops không
     * @param block Block cần kiểm tra
     * @return true nếu block có metadata NoDrops và giá trị là true
     */
    private boolean hasNoDropsMetadata(Block block) {
        if (block.hasMetadata("NoDrops")) {
            for (MetadataValue value : block.getMetadata("NoDrops")) {
                if (value.getOwningPlugin() == Storage.getStorage() && value.asBoolean()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Kiểm tra xem block có metadata LeafEnchantDrop không (từ leaf collector enchant)
     * @param block Block cần kiểm tra
     * @return true nếu block có metadata LeafEnchantDrop và giá trị là true
     */
    private boolean hasLeafEnchantDropMetadata(Block block) {
        if (block.hasMetadata("LeafEnchantDrop")) {
            for (MetadataValue value : block.getMetadata("LeafEnchantDrop")) {
                if (value.getOwningPlugin() == Storage.getStorage() && value.asBoolean()) {
                    return true;
                }
            }
        }
        return false;
    }
} 