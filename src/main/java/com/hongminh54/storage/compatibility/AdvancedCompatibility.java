package com.hongminh54.storage.compatibility;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;

import java.util.UUID;

/**
 * Lớp hỗ trợ tương thích cho các tính năng nâng cao Minecraft 1.12.2 - 1.21.x
 * Xử lý custom model data, player heads, NBT, và các tính năng khác
 */
public class AdvancedCompatibility {
    
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_114 = nmsAssistant.isVersionLessThan(14);
    private static final boolean IS_1_20_5_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(20) && 
                                                       nmsAssistant.getNMSVersion().getRevision() >= 5;
    
    /**
     * Áp dụng custom model data một cách an toàn
     * 
     * @param meta ItemMeta cần áp dụng
     * @param customModelData Custom model data value
     */
    public static void setCustomModelData(ItemMeta meta, int customModelData) {
        if (meta == null || customModelData <= 0) {
            return;
        }
        
        try {
            if (IS_PRE_114) {
                // Phiên bản 1.12.2-1.13.x không hỗ trợ custom model data
                return;
            }
            
            // Phiên bản 1.14+ hỗ trợ custom model data
            meta.setCustomModelData(customModelData);
        } catch (NoSuchMethodError e) {
            // Method không tồn tại trong phiên bản này
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("CustomModelData không được hỗ trợ trong phiên bản này");
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set custom model data: " + e.getMessage());
            }
        }
    }
    
    /**
     * Lấy custom model data một cách an toàn
     * 
     * @param meta ItemMeta cần lấy
     * @return Custom model data hoặc 0 nếu không có/không hỗ trợ
     */
    public static int getCustomModelData(ItemMeta meta) {
        if (meta == null || IS_PRE_114) {
            return 0;
        }
        
        try {
            return meta.hasCustomModelData() ? meta.getCustomModelData() : 0;
        } catch (NoSuchMethodError e) {
            return 0;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy custom model data: " + e.getMessage());
            }
            return 0;
        }
    }
    
    /**
     * Set player head skin một cách an toàn
     * 
     * @param skullMeta SkullMeta cần set
     * @param player Player để lấy skin
     */
    public static void setPlayerHead(SkullMeta skullMeta, Player player) {
        if (skullMeta == null || player == null) {
            return;
        }
        
        try {
            if (IS_PRE_113) {
                // Phiên bản 1.12.2: sử dụng setOwner
                skullMeta.setOwner(player.getName());
            } else {
                // Phiên bản 1.13+: sử dụng setOwningPlayer
                skullMeta.setOwningPlayer(player);
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set player head: " + e.getMessage());
            }
        }
    }
    
    /**
     * Set player head skin bằng tên một cách an toàn
     * 
     * @param skullMeta SkullMeta cần set
     * @param playerName Tên player
     */
    public static void setPlayerHead(SkullMeta skullMeta, String playerName) {
        if (skullMeta == null || playerName == null || playerName.isEmpty()) {
            return;
        }
        
        try {
            if (IS_PRE_113) {
                // Phiên bản 1.12.2: sử dụng setOwner
                skullMeta.setOwner(playerName);
            } else {
                // Phiên bản 1.13+: thử lấy OfflinePlayer
                try {
                    skullMeta.setOwningPlayer(Bukkit.getOfflinePlayer(playerName));
                } catch (Exception e) {
                    // Fallback: sử dụng setOwner nếu có
                    try {
                        java.lang.reflect.Method setOwnerMethod = skullMeta.getClass().getMethod("setOwner", String.class);
                        setOwnerMethod.invoke(skullMeta, playerName);
                    } catch (Exception ex) {
                        // Bỏ qua lỗi
                    }
                }
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set player head by name: " + e.getMessage());
            }
        }
    }
    
    /**
     * Set player head skin bằng UUID một cách an toàn
     * 
     * @param skullMeta SkullMeta cần set
     * @param uuid UUID của player
     */
    public static void setPlayerHead(SkullMeta skullMeta, UUID uuid) {
        if (skullMeta == null || uuid == null) {
            return;
        }
        
        try {
            if (IS_PRE_113) {
                // Phiên bản 1.12.2: lấy tên từ UUID
                try {
                    String playerName = Bukkit.getOfflinePlayer(uuid).getName();
                    if (playerName != null) {
                        skullMeta.setOwner(playerName);
                    }
                } catch (Exception e) {
                    // Bỏ qua lỗi
                }
            } else {
                // Phiên bản 1.13+: sử dụng setOwningPlayer
                skullMeta.setOwningPlayer(Bukkit.getOfflinePlayer(uuid));
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set player head by UUID: " + e.getMessage());
            }
        }
    }
    
    /**
     * Kiểm tra xem ItemStack có phải là player head không
     * 
     * @param item ItemStack cần kiểm tra
     * @return true nếu là player head
     */
    public static boolean isPlayerHead(ItemStack item) {
        if (item == null) {
            return false;
        }
        
        try {
            if (IS_PRE_113) {
                return item.getType().name().equals("SKULL_ITEM") && item.getDurability() == 3;
            } else {
                return item.getType().name().equals("PLAYER_HEAD");
            }
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Tạo player head ItemStack một cách an toàn
     * 
     * @param player Player để lấy skin
     * @return ItemStack player head hoặc null nếu có lỗi
     */
    public static ItemStack createPlayerHead(Player player) {
        if (player == null) {
            return null;
        }
        
        try {
            ItemStack head;
            if (IS_PRE_113) {
                head = new ItemStack(org.bukkit.Material.valueOf("SKULL_ITEM"), 1, (short) 3);
            } else {
                head = new ItemStack(org.bukkit.Material.valueOf("PLAYER_HEAD"));
            }
            
            SkullMeta meta = (SkullMeta) head.getItemMeta();
            if (meta != null) {
                setPlayerHead(meta, player);
                head.setItemMeta(meta);
            }
            
            return head;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể tạo player head: " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * Tạo player head ItemStack bằng tên một cách an toàn
     * 
     * @param playerName Tên player
     * @return ItemStack player head hoặc null nếu có lỗi
     */
    public static ItemStack createPlayerHead(String playerName) {
        if (playerName == null || playerName.isEmpty()) {
            return null;
        }
        
        try {
            ItemStack head;
            if (IS_PRE_113) {
                head = new ItemStack(org.bukkit.Material.valueOf("SKULL_ITEM"), 1, (short) 3);
            } else {
                head = new ItemStack(org.bukkit.Material.valueOf("PLAYER_HEAD"));
            }
            
            SkullMeta meta = (SkullMeta) head.getItemMeta();
            if (meta != null) {
                setPlayerHead(meta, playerName);
                head.setItemMeta(meta);
            }
            
            return head;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể tạo player head by name: " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * Kiểm tra xem có hỗ trợ persistent data container không
     * 
     * @return true nếu hỗ trợ (1.14+)
     */
    public static boolean supportsPersistentDataContainer() {
        return !IS_PRE_114;
    }
    
    /**
     * Kiểm tra xem có hỗ trợ custom model data không
     * 
     * @return true nếu hỗ trợ (1.14+)
     */
    public static boolean supportsCustomModelData() {
        return !IS_PRE_114;
    }
    
    /**
     * Kiểm tra xem có hỗ trợ RGB colors không
     * 
     * @return true nếu hỗ trợ (1.16+)
     */
    public static boolean supportsRGBColors() {
        return nmsAssistant.isVersionGreaterThanOrEqualTo(16);
    }
    
    /**
     * Kiểm tra xem có hỗ trợ Adventure API không
     * 
     * @return true nếu hỗ trợ (1.20.5+)
     */
    public static boolean supportsAdventureAPI() {
        return IS_1_20_5_OR_HIGHER;
    }
    
    /**
     * Lấy phiên bản Minecraft hiện tại dưới dạng string
     *
     * @return Phiên bản Minecraft (ví dụ: "1.21.1")
     */
    public static String getMinecraftVersion() {
        try {
            return nmsAssistant.getNMSVersion().getMajor() + "." +
                   nmsAssistant.getNMSVersion().getMinor() + "." +
                   nmsAssistant.getNMSVersion().getRevision();
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * Kiểm tra xem NBT-API có hoạt động không
     *
     * @return true nếu NBT-API hoạt động bình thường
     */
    public static boolean isNBTAPIWorking() {
        try {
            // Thử tạo một NBTItem đơn giản để test
            de.tr7zw.changeme.nbtapi.NBTItem testItem = new de.tr7zw.changeme.nbtapi.NBTItem(new ItemStack(org.bukkit.Material.PAPER));
            testItem.setString("test", "value");
            return testItem.getString("test").equals("value");
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("NBT-API không hoạt động: " + e.getMessage());
            }
            return false;
        }
    }

    /**
     * Set NBT string một cách an toàn
     *
     * @param item ItemStack cần set NBT
     * @param key Key NBT
     * @param value Value NBT
     * @return ItemStack đã được set NBT hoặc item gốc nếu có lỗi
     */
    public static ItemStack setNBTString(ItemStack item, String key, String value) {
        if (item == null || key == null || value == null) {
            return item;
        }

        try {
            if (isNBTAPIWorking()) {
                de.tr7zw.changeme.nbtapi.NBTItem nbtItem = new de.tr7zw.changeme.nbtapi.NBTItem(item);
                nbtItem.setString(key, value);
                return nbtItem.getItem();
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set NBT string: " + e.getMessage());
            }
        }
        return item;
    }

    /**
     * Lấy NBT string một cách an toàn
     *
     * @param item ItemStack cần lấy NBT
     * @param key Key NBT
     * @return Value NBT hoặc null nếu không có/có lỗi
     */
    public static String getNBTString(ItemStack item, String key) {
        if (item == null || key == null) {
            return null;
        }

        try {
            if (isNBTAPIWorking()) {
                de.tr7zw.changeme.nbtapi.NBTItem nbtItem = new de.tr7zw.changeme.nbtapi.NBTItem(item);
                return nbtItem.hasTag(key) ? nbtItem.getString(key) : null;
            }
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy NBT string: " + e.getMessage());
            }
        }
        return null;
    }
}
