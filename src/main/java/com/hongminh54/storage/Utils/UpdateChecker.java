package com.hongminh54.storage.Utils;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Level;

import javax.net.ssl.HttpsURLConnection;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.Storage;

public class UpdateChecker implements Listener {

    private final String GITHUB_REPO = "hongminh54/KhoKhoangSan"; 
    private final String GITHUB_API_URL = "https://api.github.com/repos/" + GITHUB_REPO + "/releases/latest";
    private final Storage plugin;
    private final String pluginVersion;
    private String githubVersion;
    private boolean updateAvailable;
    private boolean devBuildVersion;
    private String downloadUrl;
    private String changelog;
    private final Map<UUID, Boolean> pendingDownloads = new HashMap<>();
    private final Map<UUID, Boolean> waitingForConfirmation = new HashMap<>();

    // Enum để đại diện các hệ điều hành hỗ trợ
    public enum OperatingSystem {
        WINDOWS,
        LINUX,
        MAC,
        UNKNOWN
    }

    public UpdateChecker(@NotNull Storage storage) {
        plugin = storage;
        pluginVersion = storage.getDescription().getVersion();
    }

    public String getGithubVersion() {
        return githubVersion;
    }

    public void fetch() {
        Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
            if (com.hongminh54.storage.Utils.File.getConfig().getBoolean("check_update")) {
                try {
                    HttpsURLConnection con = (HttpsURLConnection) new URL(GITHUB_API_URL).openConnection();
                    con.setRequestMethod("GET");
                    con.setRequestProperty("Accept", "application/vnd.github.v3+json");
                    con.setConnectTimeout(10000);
                    con.setReadTimeout(10000);
                    
                    int responseCode = con.getResponseCode();
                    if (responseCode != 200) {
                        plugin.getLogger().warning("Không thể kiểm tra cập nhật: Mã phản hồi HTTP " + responseCode);
                        return;
                    }
                    
                    StringBuilder response = new StringBuilder();
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(con.getInputStream()))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            response.append(line);
                        }
                    }
                    
                    String jsonResponse = response.toString();
                    
                    int tagIndex = jsonResponse.indexOf("\"tag_name\"");
                    if (tagIndex != -1) {
                        int startIndex = jsonResponse.indexOf(":", tagIndex) + 2;
                        int endIndex = jsonResponse.indexOf("\"", startIndex + 1);
                        String tagName = jsonResponse.substring(startIndex, endIndex);
                        githubVersion = tagName.startsWith("v") ? tagName.substring(1) : tagName;
                    } else {
                        plugin.getLogger().warning("Không tìm thấy thông tin phiên bản trong phản hồi GitHub.");
                        return;
                    }
                    
                    int bodyIndex = jsonResponse.indexOf("\"body\"");
                    if (bodyIndex != -1) {
                        int startIndex = jsonResponse.indexOf(":", bodyIndex) + 2;
                        int endIndex = jsonResponse.indexOf("\"", startIndex + 1);
                        
                        while (jsonResponse.indexOf("\\n", endIndex) != -1 && 
                              jsonResponse.indexOf("\\n", endIndex) < jsonResponse.indexOf("\"", endIndex + 1)) {
                            endIndex = jsonResponse.indexOf("\"", endIndex + 1);
                        }
                        
                        if (startIndex < endIndex) {
                            changelog = jsonResponse.substring(startIndex, endIndex)
                                    .replace("\\r\\n", "\n")
                                    .replace("\\n", "\n")
                                    .replace("\\\"", "\"");
                        }
                    }
                    
                    int browserDownloadUrlIndex = jsonResponse.indexOf("\"browser_download_url\"");
                    if (browserDownloadUrlIndex != -1) {
                        int startIndex = jsonResponse.indexOf(":", browserDownloadUrlIndex) + 2;
                        int endIndex = jsonResponse.indexOf("\"", startIndex + 1);
                        downloadUrl = jsonResponse.substring(startIndex, endIndex);
                    } else {
                        downloadUrl = "https://github.com/" + GITHUB_REPO + "/releases/latest";
                        plugin.getLogger().info("Không tìm thấy URL tải xuống trực tiếp, sử dụng trang phát hành GitHub.");
                    }
                } catch (java.net.SocketTimeoutException e) {
                    plugin.getLogger().log(Level.WARNING, "Timeout khi kiểm tra cập nhật từ GitHub. Vui lòng thử lại sau.", e);
                    return;
                } catch (java.net.UnknownHostException e) {
                    plugin.getLogger().log(Level.WARNING, "Không thể kết nối đến GitHub. Vui lòng kiểm tra kết nối mạng của bạn.", e);
                    return;
                } catch (Exception ex) {
                    plugin.getLogger().log(Level.WARNING, "Không thể kiểm tra cập nhật từ GitHub: " + ex.getMessage(), ex);
                    return;
                }

                if (githubVersion == null || githubVersion.isEmpty()) {
                    plugin.getLogger().warning("Không thể xác định phiên bản từ GitHub.");
                    return;
                }

                updateAvailable = githubIsNewer();
                devBuildVersion = devBuildIsNewer();

                Bukkit.getScheduler().runTask(plugin, () -> {
                    if (devBuildVersion) {
                        plugin.getLogger().warning("Bạn đang sử dụng phiên bản DevBuild của KhoKhoangSan Plugin");
                        plugin.getLogger().warning("Hầu hết các tính năng trong DevBuild đã sửa lỗi và có tính năng mới cho phiên bản tiếp theo, và có thể bao gồm các vấn đề khác");
                        plugin.getLogger().warning("Vì vậy, nếu bạn gặp bất kỳ vấn đề nào, vui lòng vào Discord của tôi và báo cáo cho Danh!");
                    }
                    if (updateAvailable) {
                        plugin.getLogger().warning("Một bản cập nhật cho KhoKhoangSan (v" + getGithubVersion() + ") đã có sẵn tại:");
                        plugin.getLogger().warning("https://github.com/" + GITHUB_REPO + "/releases/latest");
                        plugin.getLogger().warning("Bạn đang sử dụng phiên bản v" + pluginVersion);
                        plugin.getLogger().warning("Nếu phiên bản plugin của bạn cao hơn phiên bản GitHub, bạn có thể bỏ qua thông báo này");
                        
                        if (changelog != null && !changelog.isEmpty()) {
                            plugin.getLogger().info("=== CHANGELOG ===");
                            for (String line : changelog.split("\n")) {
                                plugin.getLogger().info(line);
                            }
                            plugin.getLogger().info("================");
                        }
                        
                        Bukkit.getPluginManager().registerEvents(this, plugin);
                    } else {
                        plugin.getLogger().info("Đây là phiên bản mới nhất của KhoKhoangSan Plugin");
                    }
                });
            }
        });
    }

    private boolean githubIsNewer() {
        if (githubVersion == null || githubVersion.isEmpty() || !githubVersion.matches("[0-9].[0-9].[0-9]")) {
            return false;
        }

        int[] plV = toReadable(pluginVersion);
        int[] ghV = toReadable(githubVersion);

        if (plV == null || ghV == null) return false;

        if (plV[0] < ghV[0]) {
            return true;
        }
        if ((plV[1] < ghV[1])) {
            return true;
        }
        return plV[2] < ghV[2];
    }

    private boolean devBuildIsNewer() {
        if (githubVersion == null || githubVersion.isEmpty() || !githubVersion.matches("[0-9].[0-9].[0-9]")) {
            return false;
        }

        int[] plV = toReadable(pluginVersion);
        int[] ghV = toReadable(githubVersion);

        if (plV == null || ghV == null) return false;

        if (plV[0] > ghV[0]) {
            return true;
        }
        if ((plV[1] > ghV[1])) {
            return true;
        }
        return plV[2] > ghV[2];
    }

    private int[] toReadable(@NotNull String version) {
        try {
            if (version.endsWith("-SNAPSHOT")) {
                version = version.split("-SNAPSHOT")[0];
            }
            return Arrays.stream(version.split("\\.")).mapToInt(Integer::parseInt).toArray();
        } catch (NumberFormatException e) {
            plugin.getLogger().warning("Không thể phân tích phiên bản: " + version);
            return null;
        }
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onJoin(@NotNull PlayerJoinEvent e) {
        if (updateAvailable) {
            Player player = e.getPlayer();
            if (player.hasPermission("storage.admin")) {
                player.sendMessage(ChatColor.GREEN + String.format("Đã có bản cập nhật cho KhoKhoangSan tại %s", "https://github.com/" + GITHUB_REPO + "/releases/latest"));
                player.sendMessage(ChatColor.GREEN + String.format("Bạn đang sử dụng phiên bản %s, phiên bản mới là %s", pluginVersion, githubVersion));
                player.sendMessage(ChatColor.GREEN + "Nếu phiên bản plugin của bạn cao hơn phiên bản GitHub, bạn có thể bỏ qua thông báo này");
                
                if (changelog != null && !changelog.isEmpty()) {
                    player.sendMessage(ChatColor.GOLD + "=== THAY ĐỔI ===");
                    for (String line : changelog.split("\n")) {
                        if (!line.trim().isEmpty()) {
                            player.sendMessage(ChatColor.YELLOW + line);
                        }
                    }
                    player.sendMessage(ChatColor.GOLD + "===============");
                }
                
                if (downloadUrl != null && !downloadUrl.isEmpty()) {
                    player.sendMessage(ChatColor.YELLOW + "Bạn có muốn tải xuống phiên bản mới nhất không?");
                    player.sendMessage(ChatColor.GREEN + "Gõ " + ChatColor.WHITE + "/kho update" + ChatColor.GREEN + " để tải xuống bản cập nhật");
                    
                    pendingDownloads.put(player.getUniqueId(), true);
                }
            }
        }
    }
    
    public boolean hasPendingDownload(UUID playerUUID) {
        return pendingDownloads.getOrDefault(playerUUID, false);
    }
    
    public void removePendingDownload(UUID playerUUID) {
        pendingDownloads.remove(playerUUID);
        waitingForConfirmation.remove(playerUUID);
    }
    
    /**
     * Phát hiện hệ điều hành đang chạy máy chủ
     * @return Loại hệ điều hành (WINDOWS, LINUX, MAC, UNKNOWN)
     */
    public OperatingSystem detectOperatingSystem() {
        String os = System.getProperty("os.name").toLowerCase();
        
        if (os.contains("win")) {
            return OperatingSystem.WINDOWS;
        } else if (os.contains("nux") || os.contains("nix") || os.contains("aix")) {
            return OperatingSystem.LINUX;
        } else if (os.contains("mac")) {
            return OperatingSystem.MAC;
        } else {
            return OperatingSystem.UNKNOWN;
        }
    }
    
    /**
     * Hiển thị hướng dẫn cập nhật cho người chơi dựa trên hệ điều hành
     * @param player Người chơi cần nhận thông báo
     */
    public void showUpdateConfirmation(Player player) {
        if (!updateAvailable || downloadUrl == null || downloadUrl.isEmpty()) {
            player.sendMessage(ChatColor.RED + "Không có bản cập nhật nào để tải xuống.");
            return;
        }
        
        if (!player.hasPermission("storage.admin")) {
            player.sendMessage(ChatColor.RED + "Bạn không có quyền cập nhật plugin.");
            return;
        }
        
        OperatingSystem os = detectOperatingSystem();
        player.sendMessage(ChatColor.GOLD + "========= CẬP NHẬT PLUGIN =========");
        player.sendMessage(ChatColor.YELLOW + "Hệ điều hành phát hiện: " + ChatColor.WHITE + getOSDisplayName(os));
        player.sendMessage(ChatColor.YELLOW + "Phiên bản hiện tại: " + ChatColor.WHITE + pluginVersion);
        player.sendMessage(ChatColor.YELLOW + "Phiên bản mới: " + ChatColor.WHITE + githubVersion);
        player.sendMessage(ChatColor.YELLOW + "Bạn có chắc chắn muốn cập nhật plugin không?");
        player.sendMessage(ChatColor.GRAY + "Tên file plugin sẽ giữ nguyên là " + ChatColor.WHITE + "KhoKhoangSan.jar");
        
        switch (os) {
            case WINDOWS:
                player.sendMessage(ChatColor.AQUA + "Trên Windows: " + ChatColor.WHITE + "Plugin sẽ được tải xuống và cài đặt tự động.");
                break;
            case LINUX:
                player.sendMessage(ChatColor.AQUA + "Trên Linux: " + ChatColor.WHITE + "Plugin sẽ được tải xuống nhưng cần phân quyền thủ công.");
                player.sendMessage(ChatColor.GRAY + "Lệnh cần chạy sau khi tải: " + ChatColor.WHITE + "chmod 755 plugins/KhoKhoangSan.jar");
                break;
            case MAC:
                player.sendMessage(ChatColor.AQUA + "Trên macOS: " + ChatColor.WHITE + "Plugin sẽ được tải xuống nhưng cần phân quyền thủ công.");
                player.sendMessage(ChatColor.GRAY + "Lệnh cần chạy sau khi tải: " + ChatColor.WHITE + "chmod 755 plugins/KhoKhoangSan.jar");
                break;
            default:
                player.sendMessage(ChatColor.AQUA + "Hệ điều hành không xác định: " + ChatColor.WHITE + "Plugin sẽ được tải xuống nhưng có thể cần thêm thiết lập thủ công.");
                break;
        }
        
        player.sendMessage(ChatColor.YELLOW + "Gõ " + ChatColor.WHITE + "/kho update confirm" + ChatColor.YELLOW + " để xác nhận cập nhật");
        player.sendMessage(ChatColor.YELLOW + "Gõ " + ChatColor.WHITE + "/kho update cancel" + ChatColor.YELLOW + " để hủy cập nhật");
        player.sendMessage(ChatColor.GOLD + "====================================");
        
        waitingForConfirmation.put(player.getUniqueId(), true);
    }
    
    /**
     * Lấy tên hiển thị cho hệ điều hành
     * @param os Hệ điều hành
     * @return Tên hiển thị
     */
    private String getOSDisplayName(OperatingSystem os) {
        switch (os) {
            case WINDOWS:
                return "Windows";
            case LINUX:
                return "Linux";
            case MAC:
                return "macOS";
            default:
                return "Không xác định";
        }
    }
    
    /**
     * Kiểm tra xem người chơi có đang chờ xác nhận cập nhật không
     * @param playerUUID UUID của người chơi
     * @return true nếu người chơi đang chờ xác nhận
     */
    public boolean isWaitingForConfirmation(UUID playerUUID) {
        return waitingForConfirmation.getOrDefault(playerUUID, false);
    }
    
    /**
     * Tải xuống và cài đặt bản cập nhật
     * @param player Người chơi yêu cầu cập nhật
     */
    public void downloadUpdate(Player player) {
        if (!updateAvailable || downloadUrl == null || downloadUrl.isEmpty()) {
            player.sendMessage(ChatColor.RED + "Không có bản cập nhật nào để tải xuống.");
            return;
        }
        
        if (!player.hasPermission("storage.admin")) {
            player.sendMessage(ChatColor.RED + "Bạn không có quyền cập nhật plugin.");
            return;
        }
        
        // Xác định hệ điều hành
        final OperatingSystem os = detectOperatingSystem();
        
        player.sendMessage(ChatColor.YELLOW + "Đang tải xuống phiên bản mới nhất của KhoKhoangSan...");
        player.sendMessage(ChatColor.GRAY + "Phiên bản mới sẽ được cài đặt khi máy chủ khởi động lại.");
        
        new BukkitRunnable() {
            @Override
            public void run() {
                HttpsURLConnection connection = null;
                try {
                    connection = (HttpsURLConnection) new URL(downloadUrl).openConnection();
                    connection.setRequestProperty("Accept", "application/octet-stream");
                    connection.setConnectTimeout(30000);
                    connection.setReadTimeout(30000);
                    
                    // Sử dụng tên file cố định thay vì thêm phiên bản
                    String fileName = "KhoKhoangSan.jar";
                    
                    Path pluginsDir = Bukkit.getUpdateFolderFile().toPath().getParent();
                    if (!Files.exists(pluginsDir)) {
                        Files.createDirectories(pluginsDir);
                    }
                    
                    // Thay đổi đường dẫn tải xuống tùy thuộc vào hệ điều hành
                    Path downloadPath;
                    if (os == OperatingSystem.WINDOWS) {
                        // Trên Windows, tải xuống thư mục update
                        Path updateFolder = Paths.get(pluginsDir.toString(), "update");
                        if (!Files.exists(updateFolder)) {
                            Files.createDirectories(updateFolder);
                        }
                        downloadPath = Paths.get(updateFolder.toString(), fileName);
                    } else {
                        // Trên Linux/Mac, tải xuống thư mục plugins trực tiếp để dễ dàng phân quyền
                        downloadPath = Paths.get(pluginsDir.toString(), fileName + ".update");
                    }
                    
                    long fileSize = connection.getContentLengthLong();
                    long downloaded = 0;
                    int reportedProgress = 0;
                    final int bufferSize = 8192;
                    
                    try (BufferedInputStream in = new BufferedInputStream(connection.getInputStream());
                         FileOutputStream out = new FileOutputStream(downloadPath.toFile())) {
                        byte[] buffer = new byte[bufferSize];
                        int bytesRead;
                        long startTime = System.currentTimeMillis();
                        
                        while ((bytesRead = in.read(buffer)) != -1) {
                            out.write(buffer, 0, bytesRead);
                            downloaded += bytesRead;
                            
                            if (fileSize > 0) {
                                int progress = (int) (downloaded * 100 / fileSize);
                                if (progress >= reportedProgress + 25) {
                                    reportedProgress = progress;
                                    final int currentProgress = progress;
                                    
                                    new BukkitRunnable() {
                                        @Override
                                        public void run() {
                                            player.sendMessage(ChatColor.YELLOW + "Đang tải xuống: " + 
                                                               currentProgress + "% hoàn thành...");
                                        }
                                    }.runTask(plugin);
                                }
                            }
                        }
                        
                        long endTime = System.currentTimeMillis();
                        final double timeInSeconds = (endTime - startTime) / 1000.0;
                        final Path finalDownloadPath = downloadPath;
                        
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                                player.sendMessage(ChatColor.GREEN + "Tải xuống hoàn tất trong " + 
                                                  String.format("%.1f", timeInSeconds) + " giây!");
                                
                                // Xử lý khác nhau cho Windows và Linux/Mac
                                if (os == OperatingSystem.WINDOWS) {
                                    player.sendMessage(ChatColor.YELLOW + "Đang cài đặt bản cập nhật...");
                                    
                                    File currentPluginFile = null;
                                    for (File file : pluginsDir.toFile().listFiles()) {
                                        if (file.getName().equals("KhoKhoangSan.jar")) {
                                            currentPluginFile = file;
                                            break;
                                        }
                                    }
                                    
                                    if (currentPluginFile != null) {
                                        try {
                                            File backupFile = new File(pluginsDir.toFile(), currentPluginFile.getName() + ".bak");
                                            if (backupFile.exists()) {
                                                backupFile.delete();
                                            }
                                            Files.copy(currentPluginFile.toPath(), backupFile.toPath());
                                            
                                            Files.copy(finalDownloadPath, currentPluginFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                                            
                                            player.sendMessage(ChatColor.GREEN + "Cài đặt bản cập nhật thành công!");
                                            player.sendMessage(ChatColor.YELLOW + "Vui lòng khởi động lại máy chủ để áp dụng bản cập nhật.");
                                            player.sendMessage(ChatColor.GRAY + "Đã tạo bản sao lưu tại: " + backupFile.getName());
                                            
                                            Files.delete(finalDownloadPath);
                                            
                                            removePendingDownload(player.getUniqueId());
                                        } catch (IOException e) {
                                            player.sendMessage(ChatColor.RED + "Không thể cài đặt bản cập nhật: " + e.getMessage());
                                            player.sendMessage(ChatColor.RED + "Vui lòng thử tải xuống thủ công tại: https://github.com/" + GITHUB_REPO + "/releases/latest");
                                            plugin.getLogger().log(Level.SEVERE, "Không thể cài đặt bản cập nhật", e);
                                        }
                                    } else {
                                        player.sendMessage(ChatColor.RED + "Không tìm thấy file KhoKhoangSan.jar trong thư mục plugins!");
                                        player.sendMessage(ChatColor.YELLOW + "Đã tải xuống tại: " + finalDownloadPath.toString());
                                        player.sendMessage(ChatColor.YELLOW + "Vui lòng di chuyển thủ công file này vào thư mục plugins và đổi tên thành KhoKhoangSan.jar");
                                        plugin.getLogger().warning("Không tìm thấy file KhoKhoangSan.jar trong thư mục plugins! Đã tải xuống tại: " + finalDownloadPath.toString());
                                    }
                                } else {
                                    // Xử lý cho Linux/Mac
                                    try {
                                        // Đổi tên file từ .jar.update thành .jar
                                        Path targetPath = Paths.get(pluginsDir.toString(), "KhoKhoangSan.jar");
                                        
                                        // Tạo bản sao lưu của file hiện tại nếu có
                                        File currentPluginFile = null;
                                        for (File file : pluginsDir.toFile().listFiles()) {
                                            if (file.getName().equals("KhoKhoangSan.jar")) {
                                                currentPluginFile = file;
                                                break;
                                            }
                                        }
                                        
                                        if (currentPluginFile != null) {
                                            File backupFile = new File(pluginsDir.toFile(), currentPluginFile.getName() + ".bak");
                                            if (backupFile.exists()) {
                                                backupFile.delete();
                                            }
                                            Files.copy(currentPluginFile.toPath(), backupFile.toPath());
                                            player.sendMessage(ChatColor.GRAY + "Đã tạo bản sao lưu tại: " + backupFile.getName());
                                        }
                                        
                                        // Đổi tên file tải về thành tên thật
                                        Files.move(finalDownloadPath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                                        
                                        // Hướng dẫn người chơi phân quyền trên Linux/Mac
                                        player.sendMessage(ChatColor.GREEN + "Tải xuống thành công! File đã được lưu tại: " + targetPath.toString());
                                        player.sendMessage(ChatColor.YELLOW + "Vui lòng thực hiện các bước sau trên máy chủ Linux/Mac để hoàn tất cài đặt:");
                                        player.sendMessage(ChatColor.WHITE + "1. Chạy lệnh: " + ChatColor.AQUA + "chmod 755 " + targetPath.toString());
                                        player.sendMessage(ChatColor.WHITE + "2. Khởi động lại máy chủ để áp dụng bản cập nhật");
                                        
                                        removePendingDownload(player.getUniqueId());
                                    } catch (IOException e) {
                                        player.sendMessage(ChatColor.RED + "Không thể hoàn tất quá trình cập nhật: " + e.getMessage());
                                        player.sendMessage(ChatColor.RED + "Vui lòng thử tải xuống thủ công tại: https://github.com/" + GITHUB_REPO + "/releases/latest");
                                        plugin.getLogger().log(Level.SEVERE, "Không thể hoàn tất quá trình cập nhật", e);
                                    }
                                }
                            }
                        }.runTask(plugin);
                        
                    }
                } catch (java.net.SocketTimeoutException e) {
                    final String errorMsg = "Timeout khi kết nối đến server tải xuống. Vui lòng thử lại sau.";
                    showErrorMessage(player, errorMsg, e);
                } catch (java.net.UnknownHostException e) {
                    final String errorMsg = "Không thể kết nối đến server tải xuống. Vui lòng kiểm tra kết nối mạng của bạn.";
                    showErrorMessage(player, errorMsg, e);
                } catch (Exception e) {
                    final String errorMsg = "Không thể tải xuống bản cập nhật: " + e.getMessage();
                    showErrorMessage(player, errorMsg, e);
                } finally {
                    if (connection != null) {
                        connection.disconnect();
                    }
                }
            }
            
            private void showErrorMessage(Player player, String message, Exception e) {
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        player.sendMessage(ChatColor.RED + message);
                        player.sendMessage(ChatColor.YELLOW + "Vui lòng tải xuống thủ công tại: " + 
                                          ChatColor.WHITE + "https://github.com/" + GITHUB_REPO + "/releases/latest");
                        plugin.getLogger().log(Level.SEVERE, message, e);
                    }
                }.runTask(plugin);
            }
        }.runTaskAsynchronously(plugin);
    }
}