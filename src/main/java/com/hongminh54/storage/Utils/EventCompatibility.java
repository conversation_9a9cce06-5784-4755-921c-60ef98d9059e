package com.hongminh54.storage.compatibility;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.plugin.Plugin;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;

/**
 * Lớp hỗ trợ tương thích Event handling cho Minecraft 1.12.2 - 1.21.x
 * Xử lý event registration, calling, và các vấn đề tương thích
 */
public class EventCompatibility {
    
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    
    /**
     * Đăng ký listener một cách an toàn
     * 
     * @param listener Listener cần đăng ký
     * @param plugin Plugin đăng ký
     * @return true nếu đăng ký thành công
     */
    public static boolean registerListener(Listener listener, Plugin plugin) {
        if (listener == null || plugin == null) {
            return false;
        }
        
        try {
            Bukkit.getPluginManager().registerEvents(listener, plugin);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể đăng ký listener: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Hủy đăng ký listener một cách an toàn
     * 
     * @param listener Listener cần hủy đăng ký
     * @return true nếu hủy đăng ký thành công
     */
    public static boolean unregisterListener(Listener listener) {
        if (listener == null) {
            return false;
        }
        
        try {
            HandlerList.unregisterAll(listener);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể hủy đăng ký listener: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Call event một cách an toàn
     * 
     * @param event Event cần call
     * @return true nếu call thành công
     */
    public static boolean callEvent(Event event) {
        if (event == null) {
            return false;
        }
        
        try {
            Bukkit.getPluginManager().callEvent(event);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể call event: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Kiểm tra event có bị cancel không một cách an toàn
     * 
     * @param event Event cần kiểm tra
     * @return true nếu event bị cancel
     */
    public static boolean isCancelled(Event event) {
        if (event == null) {
            return true;
        }
        
        try {
            if (event instanceof org.bukkit.event.Cancellable) {
                return ((org.bukkit.event.Cancellable) event).isCancelled();
            }
            return false;
        } catch (Exception e) {
            return true; // Giả định bị cancel nếu có lỗi
        }
    }
    
    /**
     * Set cancel cho event một cách an toàn
     * 
     * @param event Event cần set cancel
     * @param cancelled Trạng thái cancel
     * @return true nếu set thành công
     */
    public static boolean setCancelled(Event event, boolean cancelled) {
        if (event == null) {
            return false;
        }
        
        try {
            if (event instanceof org.bukkit.event.Cancellable) {
                ((org.bukkit.event.Cancellable) event).setCancelled(cancelled);
                return true;
            }
            return false;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set cancel cho event: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Lấy player từ event một cách an toàn
     * 
     * @param event Event
     * @return Player hoặc null nếu không có/có lỗi
     */
    public static Player getPlayerFromEvent(Event event) {
        if (event == null) {
            return null;
        }
        
        try {
            if (event instanceof org.bukkit.event.player.PlayerEvent) {
                return ((org.bukkit.event.player.PlayerEvent) event).getPlayer();
            } else if (event instanceof BlockBreakEvent) {
                return ((BlockBreakEvent) event).getPlayer();
            } else if (event instanceof InventoryClickEvent) {
                if (((InventoryClickEvent) event).getWhoClicked() instanceof Player) {
                    return (Player) ((InventoryClickEvent) event).getWhoClicked();
                }
            } else if (event instanceof PlayerInteractEvent) {
                return ((PlayerInteractEvent) event).getPlayer();
            }
            
            // Thử reflection cho các event khác
            try {
                java.lang.reflect.Method getPlayerMethod = event.getClass().getMethod("getPlayer");
                Object result = getPlayerMethod.invoke(event);
                if (result instanceof Player) {
                    return (Player) result;
                }
            } catch (Exception e) {
                // Bỏ qua lỗi reflection
            }
            
            return null;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy player từ event: " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * Kiểm tra event có phải async không
     * 
     * @param event Event cần kiểm tra
     * @return true nếu event async
     */
    public static boolean isAsyncEvent(Event event) {
        if (event == null) {
            return false;
        }
        
        try {
            return event.isAsynchronous();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Tạo custom event một cách an toàn
     * 
     * @param eventClass Class của event
     * @param args Arguments cho constructor
     * @return Event instance hoặc null nếu có lỗi
     */
    public static Event createCustomEvent(Class<? extends Event> eventClass, Object... args) {
        if (eventClass == null) {
            return null;
        }
        
        try {
            // Tìm constructor phù hợp
            Class<?>[] paramTypes = new Class[args.length];
            for (int i = 0; i < args.length; i++) {
                paramTypes[i] = args[i] != null ? args[i].getClass() : Object.class;
            }
            
            java.lang.reflect.Constructor<? extends Event> constructor = eventClass.getConstructor(paramTypes);
            return constructor.newInstance(args);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể tạo custom event: " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * Kiểm tra plugin có đang enabled không
     * 
     * @param plugin Plugin cần kiểm tra
     * @return true nếu plugin enabled
     */
    public static boolean isPluginEnabled(Plugin plugin) {
        if (plugin == null) {
            return false;
        }
        
        try {
            return plugin.isEnabled();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Lấy plugin by name một cách an toàn
     * 
     * @param name Tên plugin
     * @return Plugin hoặc null nếu không tìm thấy
     */
    public static Plugin getPlugin(String name) {
        if (name == null || name.isEmpty()) {
            return null;
        }
        
        try {
            return Bukkit.getPluginManager().getPlugin(name);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Kiểm tra plugin có tồn tại và enabled không
     * 
     * @param name Tên plugin
     * @return true nếu plugin tồn tại và enabled
     */
    public static boolean isPluginAvailable(String name) {
        Plugin plugin = getPlugin(name);
        return plugin != null && isPluginEnabled(plugin);
    }
    
    /**
     * Disable plugin một cách an toàn
     * 
     * @param plugin Plugin cần disable
     * @return true nếu disable thành công
     */
    public static boolean disablePlugin(Plugin plugin) {
        if (plugin == null) {
            return false;
        }
        
        try {
            Bukkit.getPluginManager().disablePlugin(plugin);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể disable plugin: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Enable plugin một cách an toàn
     * 
     * @param plugin Plugin cần enable
     * @return true nếu enable thành công
     */
    public static boolean enablePlugin(Plugin plugin) {
        if (plugin == null) {
            return false;
        }
        
        try {
            Bukkit.getPluginManager().enablePlugin(plugin);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể enable plugin: " + e.getMessage());
            }
            return false;
        }
    }
}
