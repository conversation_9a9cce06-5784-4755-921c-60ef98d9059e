package com.hongminh54.storage;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Level;

import com.hongminh54.storage.CMD.SpecialMaterialCMD;
import com.hongminh54.storage.Listeners.SpecialMaterialListener;
import com.hongminh54.storage.Manager.SpecialMaterialManager;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.bukkit.plugin.java.JavaPlugin;

import com.hongminh54.storage.CMD.StorageCMD;
import com.hongminh54.storage.CMD.TNTEnchantCommand;
import com.hongminh54.storage.Database.Database;
import com.hongminh54.storage.Database.PlayerData;
import com.hongminh54.storage.Database.SQLite;
import com.hongminh54.storage.Events.EventScheduler;
import com.hongminh54.storage.Events.MiningEvent;
import com.hongminh54.storage.GUI.GUI;
import com.hongminh54.storage.Listeners.GemEffectListener;
import com.hongminh54.storage.Listeners.PlayerListener;
import com.hongminh54.storage.Manager.GemEffectManager;
import com.hongminh54.storage.Manager.MineManager;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Placeholder.PAPI;

import com.hongminh54.storage.Utils.CacheManager;
import com.hongminh54.storage.Utils.DebugLogger;
import com.hongminh54.storage.Utils.ErrorLogger;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.Utils.LeaderboardManager;

import com.hongminh54.storage.Utils.PlayerSearchChatHandler;
import com.hongminh54.storage.Utils.StatsManager;
import com.hongminh54.storage.Utils.TransferMonitor;
import com.hongminh54.storage.Utils.UpdateChecker;

import net.xconfig.bukkit.model.SimpleConfigurationManager;

public final class Storage extends JavaPlugin {

    public static Database db;
    private static Storage storage;
    private static boolean WorldGuard;
    private static final List<UpdateChecker> updateCheckers = new ArrayList<>();

    // Biến kiểm tra phiên bản NMS
    private NMSAssistant nmsAssistant;
    
    // Thêm biến để quản lý trạng thái debug
    private boolean debug = false;
    
    /**
     * Kiểm tra xem plugin có đang ở chế độ debug không
     * @return true nếu đang ở chế độ debug
     */
    public boolean isDebug() {
        return debug;
    }
    
    /**
     * Đặt trạng thái debug
     * @param debug trạng thái debug mới
     */
    public void setDebug(boolean debug) {
        this.debug = debug;
    }

    public static Storage getStorage() {
        return storage;
    }
    
    public static List<UpdateChecker> getUpdateCheckers() {
        return updateCheckers;
    }

    public static boolean isWorldGuardInstalled() {
        return WorldGuard;
    }

    @Override
    public void onLoad() {
        storage = this;
        if (getServer().getPluginManager().getPlugin("WorldGuard") != null) {
            WorldGuard = true;
            com.hongminh54.storage.WorldGuard.WorldGuard.register(storage);
            getLogger().log(Level.INFO, "Hook with WorldGuard");
        }
    }

    @Override
    public void onEnable() {
        storage = this;
        nmsAssistant = new NMSAssistant();
        
        // Kiểm tra phiên bản
        if (!nmsAssistant.isSupportedVersion()) {
            getLogger().severe("Xin lỗi, Phiên bản máy chủ của bạn không được hỗ trợ :(");
            getLogger().severe("Plugin sẽ bị vô hiệu hóa.");
            Bukkit.getPluginManager().disablePlugin(this);
            return;
        }
        
        // Thiết lập cấp độ log mặc định để giảm lượng log không cần thiết
        setupLogging();
        
        // Đảm bảo thư mục plugin tồn tại
        if (!getDataFolder().exists()) {
            getDataFolder().mkdirs();
        }
        

        Bukkit.getConsoleSender().sendMessage(" §e§lStorage");
        Bukkit.getConsoleSender().sendMessage(" ");
        Bukkit.getConsoleSender().sendMessage(" §f§oVersion: §6v1.0.5");
        Bukkit.getConsoleSender().sendMessage(" ");
        Bukkit.getConsoleSender().sendMessage(" §d§oSupport Minecraft Version: §a1.12.2 - 1.21.x");
        Bukkit.getConsoleSender().sendMessage(" ");
        Bukkit.getConsoleSender().sendMessage(" §c§oAuthor: §dVoChiDanh");
        Bukkit.getConsoleSender().sendMessage(" §b§oContributor: §dTYBZI, hongminh54");
        
        this.debug = false;
        if (debug) {
            getLogger().info("Chế độ debug đã được bật!");
        }
        
        GUI.register(storage);
        SimpleConfigurationManager.register(storage);
        File.loadFiles();
        
        // Thiết lập thời gian cache từ cấu hình
        try {
            FileConfiguration config = getConfig();
            if (config.contains("settings.config_cache_duration")) {
                long cacheDuration = config.getLong("settings.config_cache_duration", 30000);
                File.setCacheDuration(cacheDuration);
                if (debug) {
                    getLogger().info("Đã thiết lập thời gian cache cấu hình: " + cacheDuration + "ms");
                }
            } else {
                // Thêm cấu hình mặc định nếu chưa có
                config.set("settings.config_cache_duration", 30000);
                config.set("settings.config_cache_duration_comment", "Thời gian cache cấu hình (mili giây), tối thiểu 5000ms (5 giây)");
                saveConfig();
                if (debug) {
                    getLogger().info("Đã thêm cấu hình mặc định cho thời gian cache cấu hình: 30000ms");
                }
            }
        } catch (Exception e) {
            getLogger().warning("Lỗi khi thiết lập thời gian cache cấu hình: " + e.getMessage());
        }
        
        File.loadGUI();
        File.updateConfig();
        File.updateMessage();
        
        // Khởi tạo hệ thống sự kiện
        MiningEvent.getInstance().loadEventConfig();
        
        // Khởi tạo và bắt đầu lịch trình sự kiện tự động
        EventScheduler.initializeInstance().loadSchedules();
        
        // Khởi tạo quản lý tài nguyên đặc biệt
        SpecialMaterialManager.initialize();
        getLogger().info("§aKhởi tạo quản lý tài nguyên đặc biệt thành công");
        
        // PlaceholderAPI Hook
        if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            new PAPI().register();
            getLogger().info("§aHooked PlaceholderAPI");
        }
        
        // Khởi tạo StorageAPI
        com.hongminh54.storage.API.StorageAPI.initialize();
        getLogger().info("§aKhởi tạo StorageAPI thành công");
        
        // Khởi tạo hệ thống cache đơn giản
        try {
            getLogger().info("Đang khởi tạo hệ thống cache...");
            CacheManager.initialize();
            getLogger().info("Hệ thống cache đã được khởi tạo thành công!");
        } catch (Exception e) {
            getLogger().severe("Lỗi khi khởi tạo hệ thống cache: " + e.getMessage());
            e.printStackTrace();
        }

        // Tạo và lưu trữ UpdateChecker
        UpdateChecker updateChecker = new UpdateChecker(storage);
        updateCheckers.add(updateChecker);
        
        com.hongminh54.storage.Events.BlockBreakEvent_ blockBreakEvent = new com.hongminh54.storage.Events.BlockBreakEvent_();
        blockBreakEvent.scheduleCacheCleanup();
        
        // Đăng ký tất cả các sự kiện
        registerEvents(updateChecker, new com.hongminh54.storage.Listeners.JoinQuit(),
                       new com.hongminh54.storage.Listeners.BlockBreak(),
                       new com.hongminh54.storage.Listeners.Chat(),
                       new com.hongminh54.storage.Listeners.BlockPlace(),
                       blockBreakEvent,
                       new com.hongminh54.storage.Listeners.TNTEnchantListener(),
                       new com.hongminh54.storage.Listeners.AxeEnchantListener(),
                       new com.hongminh54.storage.Listeners.HoeEnchantListener(),
                       new com.hongminh54.storage.Listeners.ItemSpawnListener(),
                       new SpecialMaterialListener(),
                       new com.hongminh54.storage.Listeners.ServerCommandListener(this));
        updateChecker.fetch();
        
        // Đăng ký các lệnh
        new StorageCMD("storage");
        new TNTEnchantCommand();
        new com.hongminh54.storage.CMD.ViewStorageCommand();
        new com.hongminh54.storage.CMD.AxeEnchantCommand();
        new com.hongminh54.storage.CMD.HoeEnchantCommand();
        new SpecialMaterialCMD("specialmaterial");
        new com.hongminh54.storage.CMD.ConvertBlockCMD("doiblock");

        // Test command (chỉ để debug)
        Objects.requireNonNull(getCommand("testskull")).setExecutor(new com.hongminh54.storage.CMD.TestSkullCMD());
        
        try {
            db = new SQLite(this);
            db.load();
        } catch (Exception e) {
            getLogger().severe("Error when loading database: " + e.getMessage());
            e.printStackTrace();
            getPluginLoader().disablePlugin(this);
            return;
        }
        
        // Khởi tạo bảng lỗi và hệ thống giám sát chuyển kho
        try {
            ErrorLogger.initialize();
            getLogger().info("Đã khởi tạo hệ thống ghi lỗi thành công.");
            
            TransferMonitor.initialize();
            getLogger().info("Đã khởi tạo hệ thống giám sát chuyển kho thành công.");
            
            // Cập nhật cấu hình - thêm cài đặt mới cho thời gian chờ chuyển tài nguyên
            updateTransferConfig();
            
            // Khởi tạo bảng transfer_history nếu chưa tồn tại
            if (com.hongminh54.storage.Utils.TransferManager.createTransferHistoryTable()) {
                getLogger().info("Đã khởi tạo bảng lịch sử chuyển kho thành công.");
            } else {
                getLogger().warning("Không thể khởi tạo bảng lịch sử chuyển kho.");
            }
        } catch (Exception e) {
            getLogger().severe("Lỗi khi khởi tạo hệ thống giám sát: " + e.getMessage());
            e.printStackTrace();
        }
        
        // Khởi tạo hệ thống bảng xếp hạng
        try {
            getLogger().info("Đang khởi tạo hệ thống bảng xếp hạng...");
            // Cập nhật tất cả các bảng xếp hạng ban đầu
            LeaderboardManager.updateAllLeaderboards();
            getLogger().info("Hệ thống bảng xếp hạng đã được khởi tạo thành công!");
        } catch (Exception e) {
            getLogger().severe("Lỗi khi khởi tạo hệ thống bảng xếp hạng: " + e.getMessage());
            e.printStackTrace();
        }
        
        MineManager.loadBlocks();
        
        // Cập nhật bảng xếp hạng khi server đã sẵn sàng
        try {
            // Đảm bảo database đã được khởi tạo đầy đủ
            getLogger().info("Đang cập nhật bảng xếp hạng...");
            LeaderboardManager.updateAllLeaderboards();
            getLogger().info("Cập nhật bảng xếp hạng hoàn tất.");
        } catch (Exception e) {
            getLogger().warning("Không thể cập nhật dữ liệu bảng xếp hạng: " + e.getMessage());
        }

        // Đăng ký PlayerListener mới để xử lý sự kiện khi người chơi thoát game
        registerEvents(new PlayerListener(this));
        
        // Dọn dẹp các file backup cũ khi khởi động
        try {
            getLogger().info("Đang dọn dẹp các file backup cũ...");
            MineManager.cleanupAllBackups();
        } catch (Exception e) {
            getLogger().warning("Không thể dọn dẹp các file backup cũ: " + e.getMessage());
        }
        
        // Lên lịch sao lưu dữ liệu theo định kỳ
        scheduleDataBackup();

        // Lên lịch auto-save nếu được bật trong config
        scheduleAutoSave();

        // Lên lịch kiểm tra và khắc phục tính toàn vẹn dữ liệu
        scheduleDataIntegrityCheck();

        // Khởi tạo GemEffectManager
        GemEffectManager.getInstance();
        
        // Đăng ký các listener
        getServer().getPluginManager().registerEvents(new GemEffectListener(), this);

        // Tải cache tài nguyên
        MineManager.loadBlocks();
        
        // Khởi tạo cache tên vật liệu thông qua CacheManager
        CacheManager.initialize();
        
        // Các chức năng khác
        setupNMSHandler();
        registerPAPI();
        initUpdateChecker();
        
        // Đảm bảo tính năng tài nguyên đặc biệt hoạt động ngay khi khởi động
        SpecialMaterialManager.reload();
        getLogger().info("Đã khởi tạo lại quản lý tài nguyên đặc biệt để đảm bảo listener hoạt động");
        
        // Đăng ký listener để ngăn việc rơi item từ các khối được đánh dấu NoDrops
        com.hongminh54.storage.Listeners.BlockBreak.registerNoDropsListener(this);
        getLogger().info("Đã đăng ký NoDropsListener để tối ưu hóa hiệu suất đào khối");
        
        // Đăng ký ItemSpawnListener để ngăn chặn các item spawn từ block được đánh dấu
        getServer().getPluginManager().registerEvents(new com.hongminh54.storage.Listeners.ItemSpawnListener(), this);
        getLogger().info("Đã đăng ký ItemSpawnListener để tối ưu hóa xử lý item drop");

        // Tải cài đặt tối ưu hóa cho máy chủ có nhiều người chơi
        loadHighLoadOptimizationSettings();

        // Khởi tạo quản lý bảng xếp hạng
        LeaderboardManager.initialize();
    }

    @Override
    public void onDisable() {
        // Đánh dấu server đang tắt và hủy tất cả các task
        Bukkit.getScheduler().cancelTasks(this);
        
        // Set biến isShuttingDown bằng cách gửi tin nhắn tới tất cả đối tượng PlayerListener
        PlayerListener.isServerShuttingDown = true;
        
        getLogger().info("Plugin Storage đang tắt, xin vui lòng chờ...");
        getLogger().info("Đang lưu dữ liệu người chơi...");
        
        // Hủy tất cả chat input đang chờ xử lý
        PlayerSearchChatHandler.cancelAllInputs();
        com.hongminh54.storage.Utils.ResourceInputChatHandler.cancelAllInputs();
        
        // Tắt hệ thống sự kiện đang chạy
        MiningEvent.getInstance().endEvent();
        
        // Đảm bảo không tạo instance mới của EventScheduler
        try {
            // Chỉ gọi dispose() nếu instance đã tồn tại
            com.hongminh54.storage.Events.EventScheduler eventSchedulerInstance = 
                com.hongminh54.storage.Events.EventScheduler.getInstance();
            if (eventSchedulerInstance != null) {
                getLogger().info("Đang dọn dẹp tài nguyên của EventScheduler...");
                eventSchedulerInstance.stopScheduler(); // Chỉ dừng các task, không dispose hoàn toàn
            }
        } catch (Exception e) {
            getLogger().warning("Lỗi khi dừng EventScheduler: " + e.getMessage());
        }
        
        getLogger().info("Đang lưu dữ liệu người chơi trước khi tắt plugin...");
        int playerCount = 0;
        
        // Lặp qua tất cả người chơi đang online và lưu dữ liệu của họ
        List<String> failedPlayers = new ArrayList<>();
        
        for (Player p : getServer().getOnlinePlayers()) {
            try {
                // Bắt đầu lưu dữ liệu
                getLogger().info("Đang lưu dữ liệu cho " + p.getName() + "...");
                
                // Đếm số lượng vật phẩm và ghi log để debug
                Map<String, Integer> playerItems = new HashMap<>();
                int totalItems = 0;
                
                for (String material : MineManager.getPluginBlocks()) {
                    if (MineManager.hasPlayerBlock(p, material)) {
                        int amount = MineManager.getPlayerBlock(p, material);
                        if (amount > 0) {
                            playerItems.put(material, amount);
                            totalItems += amount;
                        }
                    }
                }
                
                getLogger().info("Số lượng vật phẩm của " + p.getName() + ": " + totalItems);
                
                // Sử dụng phương thức đồng bộ để lưu dữ liệu khi tắt plugin
                com.hongminh54.storage.Utils.StatsManager.savePlayerStatsSynchronously(p);
                MineManager.savePlayerData(p);
                playerCount++;
                
                // Xóa cache của người chơi
                CacheManager.removePlayerCache(p);
                // Xóa dữ liệu khỏi cache
                StatsManager.removeFromCache(p.getName());
                // Xóa dữ liệu từ cache bảng xếp hạng
                LeaderboardManager.removePlayerFromCache(p.getName());
                
                // Kiểm tra lại dữ liệu sau khi lưu
                PlayerData savedData = Storage.db.getData(p.getName());
                
                if (savedData != null) {
                    getLogger().info("Đã lưu dữ liệu thành công cho " + p.getName());
                    
                    // Kiểm tra lại xem dữ liệu có bị mất không
                    if (totalItems > 0 && savedData.getData().equals("{}")) {
                        getLogger().warning("CẢNH BÁO: Dữ liệu sau khi lưu của " + p.getName() + " trống rỗng mặc dù trước đó có " + totalItems + " vật phẩm!");
                        
                        // Thử lưu lại một lần nữa
                        MineManager.savePlayerData(p);
                        
                        // Kiểm tra lại lần cuối
                        PlayerData recheckData = Storage.db.getData(p.getName());
                        if (recheckData != null && !recheckData.getData().equals("{}")) {
                            getLogger().info("Đã lưu lại thành công dữ liệu cho " + p.getName() + " sau lần thử lại.");
                        } else {
                            failedPlayers.add(p.getName());
                        }
                    }
                }
            } catch (Exception ex) {
                getLogger().warning("Lỗi khi lưu dữ liệu cho " + p.getName() + ": " + ex.getMessage());
                failedPlayers.add(p.getName());
                ex.printStackTrace();
            }
        }
        
        getLogger().info("Đã lưu dữ liệu cho " + playerCount + " người chơi.");
        
        if (!failedPlayers.isEmpty()) {
            getLogger().severe("Không thể lưu dữ liệu cho " + failedPlayers.size() + " người chơi: " + String.join(", ", failedPlayers));
        }
        
        // Dừng sự kiện nếu đang diễn ra
        MiningEvent.getInstance().endEvent();
        
        // Đóng kết nối với cơ sở dữ liệu
        if (db != null) {
            db.closeConnection();
            getLogger().info("Đã đóng kết nối đến cơ sở dữ liệu.");
        }
        
        // Lưu các file cấu hình
        File.saveFiles();
        
        // Dọn dẹp các file backup trước khi tắt hoàn toàn
        try {
            getLogger().info("Đang dọn dẹp các file backup cũ trước khi tắt plugin...");
            MineManager.cleanupAllBackups();
            getLogger().info("Đã hoàn thành dọn dẹp các file backup.");
        } catch (Exception e) {
            getLogger().warning("Không thể dọn dẹp các file backup cũ: " + e.getMessage());
        }
        
        // Lưu cấu hình tài nguyên đặc biệt trước khi tắt
        try {
            getLogger().info("Đang lưu cấu hình tài nguyên đặc biệt...");
            SpecialMaterialManager.saveConfig();
        } catch (Exception e) {
            getLogger().warning("Không thể lưu cấu hình tài nguyên đặc biệt: " + e.getMessage());
        }
        
        // Dọn dẹp GemEffectManager
        GemEffectManager.getInstance().cleanup();
        
        // Xóa cache đầu người chơi để tránh rò rỉ bộ nhớ
        com.hongminh54.storage.GUI.LeaderboardGUI.clearHeadCache();
        com.hongminh54.storage.GUI.PlayerSearchGUI.clearAllHeadCache();
        
        // Tắt Leaderboard Manager
        LeaderboardManager.shutdown();

        // Dọn dẹp cache


        getLogger().info("Plugin Storage đã bị tắt!");
    }

    /**
     * Lên lịch auto-save nếu được bật trong config
     */
    private void scheduleAutoSave() {
        boolean autoSaveEnabled = File.getConfig().getBoolean("database.auto_save", true);

        if (!autoSaveEnabled) {
            if (debug) {
                getLogger().info("Auto-save đã bị tắt trong config.");
            }
            return;
        }

        // Lấy thời gian auto-save từ config (mặc định 5 phút = 300 giây)
        long autoSaveInterval = File.getConfig().getLong("database.auto_save_interval", 300) * 20L; // Chuyển đổi sang ticks

        // Kiểm tra giá trị tối thiểu (5 phút)
        if (autoSaveInterval < 6000L) { // Tối thiểu 5 phút (6000 ticks)
            autoSaveInterval = 6000L;
            getLogger().info("Đã thiết lập lại thời gian auto-save tối thiểu là 5 phút để tối ưu hiệu suất.");
        }

        getLogger().info("Đã bật auto-save mỗi " + (autoSaveInterval / 20 / 60) + " phút.");

        // Lên lịch task auto-save
        Bukkit.getScheduler().runTaskTimerAsynchronously(this, () -> {
            try {
                if (debug) {
                    getLogger().info("Đang thực hiện auto-save...");
                }

                // Auto-save dữ liệu tất cả người chơi online
                int savedCount = 0;
                for (Player player : Bukkit.getOnlinePlayers()) {
                    try {
                        MineManager.savePlayerData(player);
                        savedCount++;
                    } catch (Exception e) {
                        getLogger().warning("Lỗi khi auto-save dữ liệu cho " + player.getName() + ": " + e.getMessage());
                    }
                }

                if (debug) {
                    getLogger().info("Hoàn thành auto-save cho " + savedCount + " người chơi.");
                }
            } catch (Exception e) {
                getLogger().severe("Lỗi trong quá trình auto-save: " + e.getMessage());
                e.printStackTrace();
            }
        }, autoSaveInterval, autoSaveInterval);
    }

    /**
     * Lên lịch sao lưu dữ liệu theo định kỳ
     */
    private void scheduleDataBackup() {
        // Lấy thời gian từ config (mặc định 1 giờ = 3600 giây)
        long backupInterval = File.getConfig().getLong("backup.interval", 3600) * 20L; // Chuyển đổi sang ticks
        
        // Kiểm tra xem có giá trị mặc định quá thấp không và áp dụng giá trị tối thiểu
        if (backupInterval < 72000L) { // Tối thiểu 1 giờ (72000 ticks)
            backupInterval = 72000L;
            getLogger().info("Đã thiết lập lại thời gian sao lưu tự động tối thiểu là 1 giờ để tối ưu hiệu suất.");
            
            // Cập nhật lại giá trị trong config
            if (File.getConfig().getLong("backup.interval", 3600) < 3600) {
                File.getConfig().set("backup.interval", 3600);
                try {
                    File.getConfig().save(new java.io.File(getDataFolder(), "config.yml"));
                } catch (Exception e) {
                    getLogger().warning("Không thể lưu cấu hình với giá trị backup.interval mới: " + e.getMessage());
                }
            }
        }
        
        // Kiểm tra nếu tính năng sao lưu đã được bật
        boolean backupEnabled = File.getConfig().getBoolean("backup.enabled", true);
        if (!backupEnabled) {
            getLogger().info("Tính năng sao lưu tự động đã bị tắt trong cấu hình.");
            return;
        }
        
        getLogger().info("Đã thiết lập lịch sao lưu dữ liệu mỗi " + (backupInterval / 20 / 60) + " phút.");
        
        // Lên lịch sao lưu dữ liệu với độ trễ ban đầu nhỏ để tránh backup liên tục
        Bukkit.getScheduler().runTaskTimerAsynchronously(this, () -> {
            // Dọn dẹp các file backup cũ trước khi sao lưu mới
            try {
                MineManager.cleanupAllBackups();
            } catch (Exception e) {
                getLogger().warning("Không thể dọn dẹp các file backup cũ trong lịch trình: " + e.getMessage());
            }
            
            // Lưu dữ liệu tất cả người chơi đang online
            getLogger().info("Đang sao lưu dữ liệu của tất cả người chơi đang online...");
            
            // Kiểm tra và đảm bảo tính nhất quán dữ liệu trước khi sao lưu
            verifyDataConsistency();
            
            // Sao lưu dữ liệu đồng bộ
            int count = 0;
            for (Player p : Bukkit.getOnlinePlayers()) {
                try {
                    MineManager.savePlayerData(p);
                    com.hongminh54.storage.Utils.StatsManager.savePlayerStatsSynchronously(p);
                    count++;
                } catch (Exception e) {
                    getLogger().warning("Lỗi khi sao lưu dữ liệu của " + p.getName() + ": " + e.getMessage());
                }
            }
            
            getLogger().info("Đã sao lưu dữ liệu cho " + count + " người chơi.");
            
            // Kiểm tra xem có cần sao lưu cơ sở dữ liệu
            boolean backupDatabase = File.getConfig().getBoolean("backup.database", true);
            if (backupDatabase) {
                backupDatabase();
            }
        }, 2400L, backupInterval); // Chạy lần đầu sau 2 phút (2400 ticks), sau đó lặp lại theo chu kỳ
    }
    
    /**
     * Kiểm tra và xác minh tính nhất quán của dữ liệu trước khi sao lưu
     */
    private void verifyDataConsistency() {
        getLogger().info("Đang kiểm tra tính nhất quán dữ liệu trước khi sao lưu...");
        
        try {
            int playerCount = 0;
            int fixedCount = 0;
            
            for (Player player : Bukkit.getOnlinePlayers()) {
                playerCount++;
                
                // Đếm số lượng vật phẩm trong bộ nhớ
                int memoryItemCount = 0;
                for (String material : MineManager.getPluginBlocks()) {
                    if (MineManager.hasPlayerBlock(player, material)) {
                        memoryItemCount += MineManager.getPlayerBlock(player, material);
                    }
                }
                
                // Lấy dữ liệu từ database
                PlayerData dbData = db.getData(player.getName());
                
                // Bỏ qua nếu không có dữ liệu
                if (dbData == null) continue;
                
                // So sánh dữ liệu database với bộ nhớ để phát hiện xung đột
                String dataStr = dbData.getData();
                Map<String, Integer> dbItems = new HashMap<>();
                int dbItemCount = 0;
                
                // Phân tích chuỗi dữ liệu để đếm số lượng vật phẩm
                if (!dataStr.equals("{}")) {
                    String content = dataStr.substring(1, dataStr.length() - 1);
                    if (!content.isEmpty()) {
                        for (String pair : content.split(", ")) {
                            String[] parts = pair.split("=");
                            if (parts.length == 2) {
                                try {
                                    String material = parts[0];
                                    int count = Integer.parseInt(parts[1]);
                                    dbItems.put(material, count);
                                    dbItemCount += count;
                                } catch (NumberFormatException e) {
                                    // Bỏ qua nếu không phân tích được
                                }
                            }
                        }
                    }
                }
                
                // Phát hiện sự khác biệt đáng kể (>10 vật phẩm) giữa bộ nhớ và database
                if (Math.abs(memoryItemCount - dbItemCount) > 10) {
                    getLogger().warning("Phát hiện khác biệt dữ liệu đáng kể cho " + player.getName() + 
                            ": Memory=" + memoryItemCount + ", DB=" + dbItemCount);
                    
                    if (memoryItemCount > dbItemCount) {
                        // Nếu bộ nhớ có nhiều vật phẩm hơn, lưu dữ liệu xuống database
                        getLogger().info("Đồng bộ hóa dữ liệu bộ nhớ -> database cho " + player.getName());
                        MineManager.savePlayerData(player);
                    } else if (dbItemCount > memoryItemCount) {
                        // Nếu database có nhiều vật phẩm hơn, tải lại dữ liệu từ database
                        getLogger().info("Đồng bộ hóa dữ liệu database -> bộ nhớ cho " + player.getName());
                        MineManager.loadPlayerData(player);
                    }
                    
                    fixedCount++;
                }
            }
            
            if (fixedCount > 0) {
                getLogger().info("Đã sửa chữa dữ liệu cho " + fixedCount + "/" + playerCount + " người chơi.");
            } else {
                getLogger().info("Dữ liệu của tất cả " + playerCount + " người chơi đều nhất quán. Tiếp tục sao lưu.");
            }
            
        } catch (Exception e) {
            getLogger().severe("Lỗi khi kiểm tra tính nhất quán dữ liệu: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Sao lưu cơ sở dữ liệu SQLite
     */
    private void backupDatabase() {
        if (!(db instanceof SQLite)) {
            return;
        }
        
        try {
            getLogger().info("Đang sao lưu cơ sở dữ liệu...");
            
            String dbName = "PlayerData";
            String dbPath = getDataFolder() + "/" + dbName + ".db";
            
            // Kiểm tra file tồn tại trước khi sao lưu
            java.io.File dbFile = new java.io.File(dbPath);
            if (!dbFile.exists()) {
                getLogger().severe("Không thể sao lưu cơ sở dữ liệu: File " + dbPath + " không tồn tại!");
                
                // Ghi log thêm thông tin về thư mục để kiểm tra
                getLogger().severe("Thông tin debug:");
                getLogger().severe("- Đường dẫn tuyệt đối: " + dbFile.getAbsolutePath());
                getLogger().severe("- Thư mục tồn tại: " + getDataFolder().exists());
                getLogger().severe("- Nội dung thư mục:");
                
                java.io.File[] files = getDataFolder().listFiles();
                if (files != null) {
                    for (java.io.File file : files) {
                        getLogger().severe("  + " + file.getName() + " (" + file.length() + " bytes)");
                    }
                } else {
                    getLogger().severe("  (Không thể liệt kê files)");
                }
                
                return;
            }
            
            // Tạo thư mục sao lưu nếu nó không tồn tại
            java.io.File backupDir = new java.io.File(getDataFolder(), "backups");
            if (!backupDir.exists()) {
                backupDir.mkdirs();
            }
            
            // Tạo tên tệp sao lưu với nhãn thời gian
            java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyyMMdd_HHmmss");
            String timestamp = dateFormat.format(new java.util.Date());
            String backupFile = backupDir.getPath() + "/" + dbName + "_" + timestamp + ".db";
            
            // Sao lưu tệp
            java.io.File source = new java.io.File(dbPath);
            java.io.File dest = new java.io.File(backupFile);
            
            // Sao chép tệp
            java.nio.file.Files.copy(
                source.toPath(), 
                dest.toPath(), 
                java.nio.file.StandardCopyOption.REPLACE_EXISTING
            );
            
            getLogger().info("Đã sao lưu cơ sở dữ liệu thành công: " + backupFile);
            
            // Xóa các bản sao lưu cũ (giữ 10 bản mới nhất)
            cleanupOldBackups(backupDir, 10);
            
        } catch (Exception e) {
            getLogger().severe("Lỗi khi sao lưu cơ sở dữ liệu: " + e.getMessage());
            
            // Thêm thông tin chi tiết về lỗi
            if (e instanceof java.nio.file.NoSuchFileException) {
                getLogger().severe("File database không tồn tại tại đường dẫn chỉ định. Kiểm tra lại tên và vị trí file.");
                getLogger().severe("Đường dẫn tuyệt đối: " + ((java.nio.file.NoSuchFileException) e).getFile());
            }
            
            e.printStackTrace();
        }
    }
    
    /**
     * Xóa các bản sao lưu cũ
     * @param backupDir Thư mục sao lưu
     * @param keepCount Số lượng bản sao lưu cần giữ lại
     */
    private void cleanupOldBackups(java.io.File backupDir, int keepCount) {
        // Lấy danh sách tất cả các tệp sao lưu
        java.io.File[] backups = backupDir.listFiles((dir, name) -> name.startsWith("storage_") && name.endsWith(".db"));
        
        if (backups == null || backups.length <= keepCount) {
            return;
        }
        
        // Sắp xếp theo thời gian sửa đổi (mới nhất đầu tiên)
        Arrays.sort(backups, (f1, f2) -> Long.compare(f2.lastModified(), f1.lastModified()));
        
        // Xóa các tệp cũ
        for (int i = keepCount; i < backups.length; i++) {
            if (backups[i].delete()) {
                getLogger().info("Đã xóa bản sao lưu cũ: " + backups[i].getName());
            }
        }
    }

    /**
     * Lên lịch kiểm tra tính toàn vẹn dữ liệu theo định kỳ
     */
    private void scheduleDataIntegrityCheck() {
        // Kiểm tra mỗi 15 phút = 18000 ticks
        long checkInterval = 18000L;
        boolean enableIntegrityCheck = getConfig().getBoolean("settings.enable_integrity_check", true);
        
        if (!enableIntegrityCheck) {
            getLogger().info("Tính năng kiểm tra toàn vẹn dữ liệu đã bị tắt trong cấu hình.");
            return;
        }
        
        getLogger().info("Đã lập lịch kiểm tra tính toàn vẹn dữ liệu mỗi 15 phút.");
        
        Bukkit.getScheduler().runTaskTimerAsynchronously(this, () -> {
            getLogger().info("Đang kiểm tra tính toàn vẹn dữ liệu cho tất cả người chơi...");
            
            try {
                int playerCount = 0;
                int fixedCount = 0;
                int errorCount = 0;
                
                List<String> errorPlayers = new ArrayList<>();
                
                // Duyệt qua tất cả người chơi trực tuyến
                for (Player player : Bukkit.getOnlinePlayers()) {
                    playerCount++;
                    
                    try {
                        // Kiểm tra dữ liệu trên RAM vs Database
                        PlayerData dbData = db.getData(player.getName());
                        if (dbData == null) {
                            continue; // Bỏ qua nếu không có dữ liệu
                        }
                        
                        // Đếm tổng số vật phẩm trong database
                        int dbItemCount = 0;
                        String dbDataStr = dbData.getData();
                        if (!dbDataStr.equals("{}")) {
                            String content = dbDataStr.substring(1, dbDataStr.length() - 1);
                            if (!content.isEmpty()) {
                                String[] pairs = content.split(", ");
                                for (String pair : pairs) {
                                    String[] keyValue = pair.split("=");
                                    if (keyValue.length == 2) {
                                        try {
                                            dbItemCount += Integer.parseInt(keyValue[1]);
                                        } catch (NumberFormatException e) {
                                            // Bỏ qua định dạng không hợp lệ
                                        }
                                    }
                                }
                            }
                        }
                        
                        // Đếm tổng số vật phẩm trong bộ nhớ
                        int memItemCount = 0;
                        for (String material : MineManager.getPluginBlocks()) {
                            if (MineManager.hasPlayerBlock(player, material)) {
                                memItemCount += MineManager.getPlayerBlock(player, material);
                            }
                        }
                        
                        // Phát hiện sự khác biệt đáng kể
                        if (Math.abs(dbItemCount - memItemCount) > 20) {
                            getLogger().warning("Phát hiện sự khác biệt dữ liệu cho " + player.getName() + ": DB=" + dbItemCount + ", Memory=" + memItemCount);
                            
                            if (dbItemCount > memItemCount) {
                                // Nếu DB có nhiều dữ liệu hơn, tải dữ liệu từ DB
                                getLogger().info("Đang khôi phục dữ liệu từ database cho " + player.getName());
                                MineManager.loadPlayerData(player);
                                fixedCount++;
                            } else {
                                // Nếu bộ nhớ có nhiều dữ liệu hơn, lưu dữ liệu xuống DB
                                getLogger().info("Đang lưu dữ liệu bộ nhớ xuống database cho " + player.getName());
                                MineManager.savePlayerData(player);
                                fixedCount++;
                            }
                        }
                    } catch (Exception e) {
                        getLogger().severe("Lỗi khi kiểm tra dữ liệu cho " + player.getName() + ": " + e.getMessage());
                        errorCount++;
                        errorPlayers.add(player.getName());
                    }
                }
                
                getLogger().info("Đã kiểm tra toàn vẹn dữ liệu cho " + playerCount + " người chơi. Đã sửa: " + fixedCount + ", Lỗi: " + errorCount);
                
                if (!errorPlayers.isEmpty()) {
                    getLogger().warning("Danh sách người chơi gặp lỗi: " + String.join(", ", errorPlayers));
                }
                
            } catch (Exception e) {
                getLogger().severe("Lỗi trong quá trình kiểm tra toàn vẹn dữ liệu: " + e.getMessage());
                e.printStackTrace();
            }
        }, 6000L, checkInterval); // Bắt đầu sau 5 phút (6000 ticks), kiểm tra mỗi 15 phút
    }

    public void registerEvents(Listener... listeners) {
        Arrays.asList(listeners).forEach(listener -> getServer().getPluginManager().registerEvents(listener, storage));
    }

    /**
     * Cài đặt cấp độ log phù hợp để giảm lượng log không cần thiết
     */
    private void setupLogging() {
        try {
            // Thiết lập cấp độ log mặc định là INFO
            getLogger().setLevel(Level.INFO);
            
            // Nếu có cấu hình debug trong config, sử dụng cấp độ FINE
            if (getConfig().getBoolean("debug_logging", false)) {
                getLogger().setLevel(Level.FINE);
                getLogger().info("Đã bật chế độ ghi log chi tiết (debug)");
            }
            
            // Đặt giới hạn cho các loại log để giảm thông báo trùng lặp
            java.util.logging.Logger rootLogger = java.util.logging.Logger.getLogger("");
            java.util.logging.Handler[] handlers = rootLogger.getHandlers();
            for (java.util.logging.Handler handler : handlers) {
                if (handler instanceof java.util.logging.ConsoleHandler) {
                    // Đặt cấp độ log của ConsoleHandler để phù hợp với cấp độ logger
                    handler.setLevel(getLogger().getLevel());
                }
            }
        } catch (Exception e) {
            getLogger().warning("Không thể cài đặt cấp độ log: " + e.getMessage());
        }
    }

    /**
     * Cập nhật cấu hình với cài đặt mới cho thời gian chờ chuyển tài nguyên
     */
    private void updateTransferConfig() {
        FileConfiguration config = File.getConfig();
        
        // Kiểm tra xem cài đặt đã tồn tại chưa
        if (!config.contains("transfer.processing_delay")) {
            // Nếu chưa có, thêm cài đặt mới với giá trị mặc định
            config.set("transfer.processing_delay", 2);
            
            // Ghi log về cập nhật cấu hình
            getLogger().info("Đã thêm cài đặt mới 'transfer.processing_delay' (thời gian chờ khi chuyển tài nguyên: 2 giây)");
            
            // Lưu cấu hình
            try {
                File.saveFiles();
                getLogger().info("Đã cập nhật cấu hình thành công");
            } catch (Exception e) {
                getLogger().warning("Không thể cập nhật cấu hình: " + e.getMessage());
            }
        }
    }

    private void setupNMSHandler() {
        // Implementation of setupNMSHandler method
    }

    private void registerPAPI() {
        // Implementation of registerPAPI method
    }

    private void initUpdateChecker() {
        // Implementation of initUpdateChecker method
    }

    /**
     * Tải và áp dụng các cài đặt tối ưu hóa cho máy chủ có nhiều người chơi
     */
    private void loadHighLoadOptimizationSettings() {
        try {
            FileConfiguration config = getConfig();
            
            // Kiểm tra xem tính năng tối ưu hóa có được bật không
            if (!config.getBoolean("high_load_optimization.enabled", true)) {
                getLogger().info("Chế độ tối ưu hóa cho nhiều người chơi: TẮT");
                return;
            }
            
            getLogger().info("Đang tải cài đặt tối ưu hóa cho máy chủ có nhiều người chơi...");
            
            // Điều chỉnh các biến static trong các lớp quản lý cache và xử lý batch
            int playerThreshold = config.getInt("high_load_optimization.player_threshold", 100);
            int lowTpsThreshold = config.getInt("high_load_optimization.low_tps_threshold", 16);
            int tpsCheckInterval = config.getInt("high_load_optimization.tps_check_interval", 60);
            
            // Đặt kích thước các pool và cache
            if (config.getBoolean("high_load_optimization.increase_cache_size", true)) {
                double cacheMultiplier = config.getDouble("high_load_optimization.cache_size_multiplier", 2.0);
                
                // Áp dụng hệ số tăng kích thước cho các cache
                try {
                    // Sử dụng reflection để thay đổi các giá trị static
                    java.lang.reflect.Field maxItemStacksField = CacheManager.class.getDeclaredField("maxItemStacks");
                    maxItemStacksField.setAccessible(true);
                    int currentMaxItemStacks = maxItemStacksField.getInt(null);
                    maxItemStacksField.setInt(null, (int)(currentMaxItemStacks * cacheMultiplier));
                    
                    java.lang.reflect.Field maxMineableBlocksField = CacheManager.class.getDeclaredField("maxMineableBlocks");
                    maxMineableBlocksField.setAccessible(true);
                    int currentMaxMineableBlocks = maxMineableBlocksField.getInt(null);
                    maxMineableBlocksField.setInt(null, (int)(currentMaxMineableBlocks * cacheMultiplier));
                    
                    java.lang.reflect.Field maxBlockValuesField = CacheManager.class.getDeclaredField("maxBlockValues");
                    maxBlockValuesField.setAccessible(true);
                    int currentMaxBlockValues = maxBlockValuesField.getInt(null);
                    maxBlockValuesField.setInt(null, (int)(currentMaxBlockValues * cacheMultiplier));
                    
                    java.lang.reflect.Field maxDisplayNamesField = CacheManager.class.getDeclaredField("maxDisplayNames");
                    maxDisplayNamesField.setAccessible(true);
                    int currentMaxDisplayNames = maxDisplayNamesField.getInt(null);
                    maxDisplayNamesField.setInt(null, (int)(currentMaxDisplayNames * cacheMultiplier));
                    
                    getLogger().info("Đã tăng kích thước cache lên " + cacheMultiplier + " lần");
                } catch (Exception e) {
                    getLogger().warning("Không thể điều chỉnh kích thước cache: " + e.getMessage());
                }
            }
            

            
            // Khởi chạy tác vụ giám sát hiệu suất
            startPerformanceMonitoring(playerThreshold, lowTpsThreshold, tpsCheckInterval);
            
            getLogger().info("Đã tải cài đặt tối ưu hóa cho máy chủ có nhiều người chơi thành công!");
        } catch (Exception e) {
            getLogger().severe("Lỗi khi tải cài đặt tối ưu hóa: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Khởi chạy tác vụ giám sát hiệu suất máy chủ và điều chỉnh các tham số
     * dựa trên số người chơi và TPS hiện tại
     * 
     * @param playerThreshold Ngưỡng số người chơi để kích hoạt tối ưu hóa
     * @param lowTpsThreshold Ngưỡng TPS thấp để kích hoạt tối ưu hóa bổ sung
     * @param tpsCheckInterval Khoảng thời gian giữa các lần kiểm tra (giây)
     */
    private void startPerformanceMonitoring(int playerThreshold, int lowTpsThreshold, int tpsCheckInterval) {
        getServer().getScheduler().runTaskTimer(this, () -> {
            try {
                // Lấy số lượng người chơi hiện tại
                int onlinePlayers = getServer().getOnlinePlayers().size();

                // Lấy TPS hiện tại (ước tính)
                double currentTps = estimateServerTPS();

                // Gửi thông tin hiệu suất nếu debug được bật
                if (isDebug()) {
                    getLogger().info(String.format(
                            "Thông tin hiệu suất - Người chơi: %d, TPS: %.2f",
                            onlinePlayers, currentTps));
                }

                // Điều chỉnh tham số dựa trên số người chơi và TPS
                adjustPerformanceParameters(onlinePlayers, currentTps, playerThreshold, lowTpsThreshold);

            } catch (Exception e) {
                if (isDebug()) {
                    getLogger().warning("Lỗi khi giám sát hiệu suất: " + e.getMessage());
                }
            }
        }, 20L * 60, 20L * tpsCheckInterval);// Chạy mỗi tpsCheckInterval giây
    }
    
    /**
     * Ước tính TPS hiện tại của server
     * Lưu ý: Đây là phương pháp ước tính và có thể không chính xác 100%
     * 
     * @return Ước tính TPS hiện tại
     */
    private double estimateServerTPS() {
        try {
            // Thời gian bắt đầu
            long startTime = System.nanoTime();
            
            // Chờ một tick máy chủ (đồng bộ)
            final boolean[] tickComplete = {false};
            
            getServer().getScheduler().runTask(this, () -> {
                tickComplete[0] = true;
            });
            
            // Chờ cho tick hoàn tất (tối đa 1 giây)
            int attempts = 0;
            while (!tickComplete[0] && attempts < 100) {
                Thread.sleep(10);
                attempts++;
            }
            
            // Thời gian kết thúc
            long endTime = System.nanoTime();
            
            // Tính toán thời gian cho mỗi tick (nano giây)
            double tickTimeNanos = (endTime - startTime);
            
            // Chuyển đổi sang TPS (tick mỗi giây)
            // Công thức: 1 giây (1_000_000_000 nano giây) / thời gian mỗi tick
            double ticksPerSecond = Math.min(20.0, 1_000_000_000 / tickTimeNanos);
            
            return ticksPerSecond;
        } catch (Exception e) {
            // Trả về 20 TPS nếu có lỗi
            return 20.0;
        }
    }
    
    /**
     * Điều chỉnh các tham số hiệu suất dựa trên số người chơi và TPS
     * 
     * @param onlinePlayers Số lượng người chơi đang online
     * @param currentTps TPS hiện tại
     * @param playerThreshold Ngưỡng số người chơi để kích hoạt tối ưu hóa
     * @param lowTpsThreshold Ngưỡng TPS thấp để kích hoạt tối ưu hóa bổ sung
     */
    private void adjustPerformanceParameters(int onlinePlayers, double currentTps, 
                                            int playerThreshold, int lowTpsThreshold) {
        try {
            boolean isHighLoad = onlinePlayers >= playerThreshold;
            boolean isLowTps = currentTps <= lowTpsThreshold;
            
            // Điều chỉnh thông số nếu có nhiều người chơi hoặc TPS thấp
            if (isHighLoad || isLowTps) {
                if (isDebug()) {
                    getLogger().info("Đang áp dụng cài đặt tối ưu hóa cao (Người chơi: " + onlinePlayers + 
                        ", TPS: " + String.format("%.2f", currentTps) + ")");
                }
                
                // Tối ưu hóa cache thống kê với tham số phù hợp
                com.hongminh54.storage.Utils.StatsManager.optimizeForHighLoad(onlinePlayers, isHighLoad, isLowTps);
                
                // Điều chỉnh giảm hiệu ứng hạt nếu được cấu hình
                if (getConfig().getBoolean("high_load_optimization.reduce_particles", true)) {
                    // Điều chỉnh số lượng hạt tối đa trong hiệu ứng
                    double reductionFactor = isLowTps ? 0.2 : 0.5; // Giảm mạnh hơn nếu TPS thấp
                    try {
                        // Tìm các instance của BlockBreakEvent_
                        com.hongminh54.storage.Events.BlockBreakEvent_ blockBreakEvent = null;
                        
                        // Tìm tất cả Listener đã đăng ký
                        for (org.bukkit.plugin.RegisteredListener registeredListener : org.bukkit.event.block.BlockBreakEvent.getHandlerList().getRegisteredListeners()) {
                            if (registeredListener.getPlugin() == this) {
                                org.bukkit.event.Listener listener = registeredListener.getListener();
                                if (listener instanceof com.hongminh54.storage.Events.BlockBreakEvent_) {
                                    blockBreakEvent = (com.hongminh54.storage.Events.BlockBreakEvent_) listener;
                                    break;
                                }
                            }
                        }
                        
                        // Nếu tìm thấy instance, gọi phương thức điều chỉnh hiệu ứng
                        if (blockBreakEvent != null) {
                            blockBreakEvent.adjustParticleEffects(onlinePlayers, currentTps, playerThreshold, reductionFactor);
                            if (isDebug()) {
                                getLogger().info("Đã điều chỉnh hiệu ứng hạt dựa trên tải máy chủ");
                            }
                        } else {
                            // Sử dụng phương thức dự phòng nếu không tìm thấy instance
                            int maxParticleCount = getConfig().getInt("settings.max_particle_count", 15);
                            int reducedParticleCount = (int)(maxParticleCount * reductionFactor);
                            getConfig().set("settings.max_particle_count", reducedParticleCount);
                            
                            if (isDebug()) {
                                getLogger().info("Đã giảm số lượng hạt từ " + maxParticleCount + " xuống " + reducedParticleCount);
                            }
                        }
                    } catch (Exception e) {
                        // Bỏ qua lỗi để tránh làm gián đoạn hoạt động của plugin
                        if (isDebug()) {
                            getLogger().warning("Không thể điều chỉnh hiệu ứng hạt: " + e.getMessage());
                        }
                    }
                }
            } else {
                // Khôi phục cài đặt mặc định nếu server không quá tải
                if (isDebug()) {
                    getLogger().info("Khôi phục cài đặt mặc định (Người chơi: " + onlinePlayers + 
                        ", TPS: " + String.format("%.2f", currentTps) + ")");
                }
                
                // Tối ưu hóa cache thống kê với tham số false cho isHighLoad
                com.hongminh54.storage.Utils.StatsManager.optimizeForHighLoad(onlinePlayers, false, false);
            }
        } catch (Exception e) {
            // Bỏ qua lỗi để tránh làm gián đoạn hoạt động của plugin
            if (isDebug()) {
                getLogger().warning("Lỗi khi điều chỉnh tham số hiệu suất: " + e.getMessage());
            }
        }
    }
}
