package com.hongminh54.storage.CMD;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.SkullUtils;

import java.util.Arrays;

/**
 * Test command để kiểm tra SkullUtils
 * Chỉ sử dụng để debug, sẽ xóa sau khi hoàn thành
 */
public class TestSkullCMD implements CommandExecutor {

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Chat.colorize("&cChỉ người chơi mới có thể sử dụng lệnh này!"));
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            player.sendMessage(Chat.colorize("&eUsage: /testskull <player_name>"));
            return true;
        }

        String targetName = args[0];
        
        try {
            // Tạo player head với skin
            ItemStack skull = SkullUtils.createPlayerHead(
                targetName,
                "&e&l" + targetName,
                Arrays.asList(
                    Chat.colorize("&7Test skull cho: &f" + targetName),
                    Chat.colorize("&8Skin được tải từ Mojang"),
                    Chat.colorize("&aTest thành công!")
                )
            );
            
            // Đưa skull vào inventory của player
            player.getInventory().addItem(skull);
            player.sendMessage(Chat.colorize("&a✓ Đã tạo skull cho " + targetName + " và thêm vào inventory!"));
            player.sendMessage(Chat.colorize("&7Kiểm tra xem skull có hiển thị skin không."));
            
        } catch (Exception e) {
            player.sendMessage(Chat.colorize("&c❌ Lỗi khi tạo skull: " + e.getMessage()));
            e.printStackTrace();
        }

        return true;
    }
}
