package com.hongminh54.storage.CMD;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.hongminh54.storage.Manager.SpecialMaterialManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.util.StringUtil;
import org.jetbrains.annotations.NotNull;

import com.hongminh54.storage.API.CMDBase;
import com.hongminh54.storage.Storage;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.compatibility.MaterialCompatibility;

/**
 * Lớp quản lý lệnh /specialmaterial
 * Đ<PERSON>ợc tách riêng từ StorageCMD.java
 */
public class SpecialMaterialCMD extends CMDBase {

    public SpecialMaterialCMD(String name) {
        super(name);
    }

    @Override
    public void execute(@NotNull CommandSender sender, String[] args) {
        // Ki<PERSON>m tra quyền
        if (!sender.hasPermission("storage.admin") && !sender.hasPermission("storage.specialmaterial")) {
            sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.no_permission")));
            return;
        }
        
        // Nếu không có đối sềE hiển thềEtrợ giúp
        if (args.length == 0) {
            displayHelp(sender);
            return;
        }
        
        // Xử lý các lệnh con
        switch (args[0].toLowerCase()) {
            case "list":
                handleList(sender);
                break;
            case "give":
                handleGive(sender, args);
                break;
            case "reload":
                handleReload(sender);
                break;
            case "debug":
                handleDebug(sender, args);
                break;
            case "help":
                displayHelp(sender);
                break;
            default:
                sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.specialmaterial.invalid_command",
                        "&cLệnh con không hợp lềE Sử dụng: give, list, reload")));
                break;
        }
    }

    @Override
    public List<String> TabComplete(@NotNull CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();
        List<String> commands = new ArrayList<>();
        
        // Kiểm tra quyền
        if (!sender.hasPermission("storage.admin") && !sender.hasPermission("storage.admin.specialmaterial")) {
            return completions;
        }

        if (args.length == 1) {
            // Lệnh con cơ bản
            commands.add("give");
            commands.add("list");
            commands.add("reload");
            commands.add("help");
            
            // Thêm lệnh debug chềEcho admin
            if (sender.hasPermission("storage.admin")) {
                commands.add("debug");
            }
            
            StringUtil.copyPartialMatches(args[0], commands, completions);
        } else if (args.length == 2) {
            // Tab completion cho lệnh give - hiển thềEdanh sách người chơi
            if (args[0].equalsIgnoreCase("give")) {
                Bukkit.getServer().getOnlinePlayers().forEach(player -> commands.add(player.getName()));
                StringUtil.copyPartialMatches(args[1], commands, completions);
            } else if (args[0].equalsIgnoreCase("debug") && sender.hasPermission("storage.admin")) {
                commands.add("blocktype");
                commands.add("mappings");
                StringUtil.copyPartialMatches(args[1], commands, completions);
            }
        } else if (args.length == 3) {
            // Tab completion cho debug blocktype
            if (args[0].equalsIgnoreCase("debug") && args[1].equalsIgnoreCase("blocktype") && sender.hasPermission("storage.admin")) {
                if (sender instanceof Player) {
                    Player player = (Player) sender;
                    Block targetBlock = player.getTargetBlock(null, 5);
                    commands.add(targetBlock.getType().name());
                }
                StringUtil.copyPartialMatches(args[2], commands, completions);
            }
            // Tab completion cho loại khoáng sản đặc biệt
            else if (args[0].equalsIgnoreCase("give")) {
                List<String> materialIds = SpecialMaterialManager.getAllSpecialMaterialIds();
                StringUtil.copyPartialMatches(args[2], materialIds, completions);
            }
        } else if (args.length == 4) {
            // Tab completion cho sềElượng
            if (args[0].equalsIgnoreCase("give")) {
                commands.add("1");
                commands.add("5");
                commands.add("10");
                commands.add("64");
                StringUtil.copyPartialMatches(args[3], commands, completions);
            }
        } else if (args.length == 5) {
            // Tab completion cho hiệu ứng
            if (args[0].equalsIgnoreCase("give")) {
                commands.add("true");
                commands.add("false");
                StringUtil.copyPartialMatches(args[4], commands, completions);
            }
        }
        
        return completions;
    }
    
    /**
     * Hiển thềEtrợ giúp vềElệnh
     * @param sender Người gửi lệnh
     */
    private void displayHelp(CommandSender sender) {
        List<String> helpMessages = File.getMessage().getStringList("admin.specialmaterial.help");
        if (helpMessages != null && !helpMessages.isEmpty()) {
            helpMessages.forEach(s -> sender.sendMessage(Chat.colorize(s)));
        } else {
            sender.sendMessage(Chat.colorize("&e&n===&f &6Lệnh Tài Nguyên Đặc Biệt &e&n==="));
            sender.sendMessage(Chat.colorize("&e/specialmaterial give <người chơi> <loại> [sềElượng] [hiệu ứng] &7- &fGive tài nguyên đặc biệt"));
            sender.sendMessage(Chat.colorize("&e/specialmaterial list &7- &fXem danh sách tài nguyên đặc biệt"));
            sender.sendMessage(Chat.colorize("&e/specialmaterial reload &7- &fTải lại cấu hình tài nguyên đặc biệt"));
            sender.sendMessage(Chat.colorize("&e/specialmaterial help &7- &fHiển thềEtrợ giúp này"));
        }
    }
    
    /**
     * Xử lý lệnh hiển thềEdanh sách tài nguyên đặc biệt
     * @param sender Người gửi lệnh
     */
    private void handleList(CommandSender sender) {
        // Lấy danh sách tài nguyên đặc biệt
        List<String> materialIds = SpecialMaterialManager.getAllSpecialMaterialIds();
        
        if (materialIds.isEmpty()) {
            sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.specialmaterial.no_materials_special_exist",
                    "&cKhông có tài nguyên đặc biệt nào tồn tại.")));
            return;
        }

        // Kiểm tra xem có yêu cầu debug không
        boolean isDebug = sender.hasPermission("storage.admin") &&
                         (sender instanceof Player) &&
                         ((Player)sender).isSneaking();

        sender.sendMessage(Chat.colorize("&e&l=== &fDanh Sách Tài Nguyên Đặc Biệt &e&l==="));
        sender.sendMessage(Chat.colorize("&eTổng sềE &f" + materialIds.size() + " &eloại"));

        // Phân loại tài nguyên theo loại block nguồn
        Map<String, List<String>> materialsByType = new HashMap<>();

        for (String id : materialIds) {
            SpecialMaterialManager.SpecialMaterial material =
                SpecialMaterialManager.getSpecialMaterialById(id);
            
            if (material != null) {
                String fromBlock = material.getFromBlock();

                // Gom nhóm theo loại block
                if (!materialsByType.containsKey(fromBlock)) {
                    materialsByType.put(fromBlock, new ArrayList<>());
                }

                String eventInfo = material.getEventOnly() != null ?
                    " &7(&f" + material.getEventOnly().getDisplayName() + "&7)" : "";

                String materialInfo = "&a• &f" + id + " &7- " + material.getDisplayName() +
                                " &7- TềElềE &f" + material.getChance() + "%" + eventInfo;
                
                // Thêm thông tin debug nếu cần
                if (isDebug) {
                    String blockMatches = "&8[&eThêm vào:";
                    int matchCount = 0;
                    
                    // Kiểm tra từng loại block mà khoáng sản này áp dụng
                    for (String blockType : com.hongminh54.storage.Manager.MineManager.getPluginBlocks()) {
                        if ("ALL".equalsIgnoreCase(fromBlock) || 
                            blockType.startsWith(fromBlock + ";") || 
                            blockType.equalsIgnoreCase(fromBlock)) {
                            if (matchCount > 0) blockMatches += ", ";
                            blockMatches += blockType;
                            matchCount++;
                            if (matchCount >= 3) {
                                blockMatches += ", ...";
                                break;
                            }
                        }
                    }
                    
                    if (matchCount == 0) {
                        blockMatches += " &cKhông khớp với block nào!";
                    }
                    
                    blockMatches += "&8]";
                    materialInfo += " " + blockMatches;
                }

                materialsByType.get(fromBlock).add(materialInfo);
            }
        }

        // Hiển thềEthông tin theo nhóm
        for (Map.Entry<String, List<String>> entry : materialsByType.entrySet()) {
            String blockType = entry.getKey();
            List<String> materialList = entry.getValue();
            
            // Tiêu đềEnhóm
            if ("ALL".equals(blockType)) {
                sender.sendMessage(Chat.colorize("&6◁E&f&lTẤT CẢ KHOÁNG SẢN:"));
            } else {
                sender.sendMessage(Chat.colorize("&6◁E&f&l" + blockType + ":"));
            }
            
            // Hiển thềEtừng khoáng sản trong nhóm
            for (String materialInfo : materialList) {
                sender.sendMessage(Chat.colorize("  " + materialInfo));
            }
        }
        
        // Hiển thềEchú thích cho chế đềEdebug
        if (sender instanceof Player) {
            if (isDebug) {
                sender.sendMessage(Chat.colorize("&7&o(Đang hiển thềEthông tin debug - Shift + Click đềEtắt)"));
            } else if (sender.hasPermission("storage.admin")) {
                sender.sendMessage(Chat.colorize("&7&o(Shift + Click đềExem thông tin debug)"));
            }
        }
    }
    
    /**
     * Xử lý lệnh give khoáng sản đặc biệt
     * @param sender Người gửi lệnh
     * @param args Các đối sềEcủa lệnh
     */
    private void handleGive(CommandSender sender, String[] args) {
        if (args.length < 3) {
            sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.specialmaterial.give_command",
                    "&aSử dụng: &e/specialmaterial give <người chơi> <loại> [sềElượng] [hiệu ứng]")));
            return;
        }
        
        // Lấy thông tin người chơi
        Player targetPlayer = Bukkit.getPlayer(args[1]);
        if (targetPlayer == null) {
            sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.specialmaterial.not_online",
                    "&cNgười chơi không trực tuyến")));
            return;
        }
        
        // Lấy thông tin khoáng sản đặc biệt
        String materialId = args[2];
        SpecialMaterialManager.SpecialMaterial material =
            SpecialMaterialManager.getSpecialMaterialById(materialId);

        if (material == null) {
            sender.sendMessage(Chat.colorize("&cTài nguyên đặc biệt &f" + materialId + " &ckhông tồn tại."));
            sender.sendMessage(Chat.colorize("&eSử dụng &f/specialmaterial list &eđềExem danh sách."));
            return;
        }
        
        // Xử lý sềElượng (mặc định là 1)
        int amount = 1;
        if (args.length >= 4) {
            try {
                amount = Integer.parseInt(args[3]);
                if (amount <= 0) {
                    sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.specialmaterial.low_number",
                            "&cVui lòng nhập sềElượng lớn hơn 0")));
                    return;
                }
            } catch (NumberFormatException e) {
                sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.specialmaterial.invalid_number",
                        "&cVui lòng nhập sềElượng hợp lềE)));
                return;
            }
        }
        
        // Xử lý cềEhiệu ứng (mặc định là true)
        boolean playEffects = true;
        if (args.length >= 5) {
            playEffects = args[4].equalsIgnoreCase("true") || args[4].equalsIgnoreCase("yes") || 
                         args[4].equalsIgnoreCase("1") || args[4].equalsIgnoreCase("on");
        }
        
        // Thực hiện give tài nguyên đặc biệt
        try {
            boolean success = SpecialMaterialManager.giveSpecialMaterialToPlayer(
                targetPlayer, materialId, amount, playEffects);

            if (success) {
                String successMsg = File.getMessage().getString("admin.specialmaterial.player_give_success",
                        "&a✁EĐã cấp &f#amount# &a#material# &acho &f#player#&a.");
                successMsg = successMsg.replace("#amount#", String.valueOf(amount))
                        .replace("#material#", material.getDisplayName())
                        .replace("#player#", targetPlayer.getName());
                sender.sendMessage(Chat.colorize(successMsg));
                
                // Thông báo cho người chơi nếu người gửi lệnh không phải là người chơi đó
                if (!(sender instanceof Player) || !((Player) sender).getUniqueId().equals(targetPlayer.getUniqueId())) {
                    String playerMsg = File.getMessage().getString("admin.specialmaterial.self_give_success",
                            "&a✁EBạn đã nhận được &f#amount# &a#material#.");
                    playerMsg = playerMsg.replace("#amount#", String.valueOf(amount))
                            .replace("#material#", material.getDisplayName());
                    targetPlayer.sendMessage(Chat.colorize(playerMsg));
                }

                // Ghi log
                Storage.getStorage().getLogger().info(sender.getName() + " đã give " + amount + " " + materialId +
                        " cho người chơi " + targetPlayer.getName());
            } else {
                sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.specialmaterial.give_fail",
                        "&cKhông thềEgive khoáng sản đặc biệt cho người chơi.")));
            }
        } catch (Exception e) {
            sender.sendMessage(Chat.colorize("&cĐã xảy ra lỗi khi give khoáng sản đặc biệt: " + e.getMessage()));
            Storage.getStorage().getLogger().warning("Lỗi khi give khoáng sản đặc biệt: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Xử lý lệnh tải lại cấu hình khoáng sản đặc biệt
     * @param sender Người gửi lệnh
     */
    private void handleReload(CommandSender sender) {
        try {
            SpecialMaterialManager.reload();
            sender.sendMessage(Chat.colorize(File.getMessage().getString("admin.specialmaterial.reload_success",
                    "&a✁EĐã tải lại cấu hình khoáng sản đặc biệt.")));
            
            // Ghi log
            Storage.getStorage().getLogger().info(sender.getName() + " đã reload cấu hình khoáng sản đặc biệt");
        } catch (Exception e) {
            sender.sendMessage(Chat.colorize("&cĐã xảy ra lỗi khi tải lại cấu hình: " + e.getMessage()));
            Storage.getStorage().getLogger().warning("Lỗi khi reload khoáng sản đặc biệt: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Xử lý lệnh debug đềEkiểm tra các vấn đềEvới khoáng sản đặc biệt
     * @param sender Người gửi lệnh
     * @param args Các đối sềEcủa lệnh
     */
    private void handleDebug(CommandSender sender, String[] args) {
        // ChềEcho phép admin sử dụng
        if (!sender.hasPermission("storage.admin")) {
            sender.sendMessage(Chat.colorize("&cBạn không có quyền sử dụng lệnh này."));
            return;
        }
        
        if (args.length < 2) {
            sender.sendMessage(Chat.colorize("&e&l=== &fDebug Khoáng Sản Đặc Biệt &e&l==="));
            sender.sendMessage(Chat.colorize("&e/specialmaterial debug blocktype <tên_block> &7- &fKiểm tra loại block có được hềEtrợ không"));
            sender.sendMessage(Chat.colorize("&e/specialmaterial debug mappings &7- &fXem các ánh xạ tài nguyên đặc biệt"));
            return;
        }
        
        switch (args[1].toLowerCase()) {
            case "blocktype":
                if (args.length < 3) {
                    if (sender instanceof Player) {
                        Player player = (Player) sender;
                        Block targetBlock = player.getTargetBlock(null, 5);
                        debugBlockType(sender, targetBlock.getType().name());
                    } else {
                        sender.sendMessage(Chat.colorize("&cBạn cần chềEđịnh tên block: &f/specialmaterial debug blocktype <tên_block>"));
                    }
                } else {
                    debugBlockType(sender, args[2]);
                }
                break;
            case "mappings":
                debugMappings(sender);
                break;
            default:
                sender.sendMessage(Chat.colorize("&cLệnh debug không hợp lềE Sử dụng: &f/specialmaterial debug [blocktype|mappings]"));
                break;
        }
    }
    
    /**
     * Debug thông tin vềEmột loại block cụ thềE     * @param sender Người gửi lệnh
     * @param blockType Tên block cần kiểm tra
     */
    private void debugBlockType(CommandSender sender, String blockType) {
        sender.sendMessage(Chat.colorize("&e&l=== &fDebug Block Type: &6" + blockType + " &e&l==="));
        
        // Kiểm tra xem block có trong danh sách các block được hềEtrợ không
        List<String> supportedBlocks = new ArrayList<>();
        for (String block : com.hongminh54.storage.Manager.MineManager.getPluginBlocks()) {
            if (block.startsWith(blockType + ";") || block.equalsIgnoreCase(blockType)) {
                supportedBlocks.add(block);
            }
        }
        
        if (supportedBlocks.isEmpty()) {
            sender.sendMessage(Chat.colorize("&c❁EBlock này KHÔNG được hềEtrợ trong cấu hình!"));
            sender.sendMessage(Chat.colorize("&7Kiểm tra xem block đã được thêm vào config.yml phần blocks chưa."));
        } else {
            sender.sendMessage(Chat.colorize("&a✁EBlock này được hềEtrợ với các dạng sau:"));
            for (String block : supportedBlocks) {
                sender.sendMessage(Chat.colorize("  &7- &f" + block));
            }
            
            // Kiểm tra tài nguyên đặc biệt nào áp dụng cho block này
            List<String> applicableMaterials = new ArrayList<>();
            for (String materialId : SpecialMaterialManager.getAllSpecialMaterialIds()) {
                SpecialMaterialManager.SpecialMaterial material =
                    SpecialMaterialManager.getSpecialMaterialById(materialId);

                if (material != null) {
                    String fromBlock = material.getFromBlock();
                    if ("ALL".equalsIgnoreCase(fromBlock) ||
                        fromBlock.equalsIgnoreCase(blockType) ||
                        blockType.startsWith(fromBlock + ";")) {
                        applicableMaterials.add(material.getId() + " (" + material.getDisplayName() + ")");
                    }
                }
            }
            
            if (applicableMaterials.isEmpty()) {
                sender.sendMessage(Chat.colorize("&e⚠ Không có tài nguyên đặc biệt nào áp dụng cho block này."));
            } else {
                sender.sendMessage(Chat.colorize("&a✁ECác tài nguyên đặc biệt áp dụng:"));
                for (String material : applicableMaterials) {
                    sender.sendMessage(Chat.colorize("  &7- &f" + material));
                }
            }
        }
        
        // Kiểm tra block trong game
        try {
            Material material = MaterialCompatibility.getMaterialSafely(blockType);
            if (material != null) {
                sender.sendMessage(Chat.colorize("&a✁EBlock này tồn tại trong game."));

                // Thêm thông tin vềEloại block
                sender.sendMessage(Chat.colorize("&7Thông tin block:"));
                sender.sendMessage(Chat.colorize("  &7- &fLoại: &e" + material.name()));
                sender.sendMessage(Chat.colorize("  &7- &fBền: &e" + (material.getMaxDurability() > 0)));
                sender.sendMessage(Chat.colorize("  &7- &fRắn: &e" + material.isSolid()));
                sender.sendMessage(Chat.colorize("  &7- &fTrong suốt: &e" + material.isTransparent()));
            } else {
                sender.sendMessage(Chat.colorize("&c❁EBlock này KHÔNG tồn tại trong game!"));
            }
        } catch (Exception e) {
            sender.sendMessage(Chat.colorize("&c❁EBlock này KHÔNG tồn tại trong game!"));
        }
    }
    
    /**
     * Debug thông tin vềEcác ánh xạ khoáng sản đặc biệt
     * @param sender Người gửi lệnh
     */
    private void debugMappings(CommandSender sender) {
        sender.sendMessage(Chat.colorize("&e&l=== &fDebug Ánh Xạ Khoáng Sản Đặc Biệt &e&l==="));
        
        // Kiểm tra xem tính năng khoáng sản đặc biệt có được bật không
        if (!SpecialMaterialManager.isEnabled()) {
            sender.sendMessage(Chat.colorize("&c❁ETính năng khoáng sản đặc biệt đang BềETẮT!"));
            return;
        }
        
        sender.sendMessage(Chat.colorize("&a✁ETính năng khoáng sản đặc biệt đang được BẬT."));
        
        // Lấy danh sách tất cả các tài nguyên đặc biệt
        List<String> materialIds = SpecialMaterialManager.getAllSpecialMaterialIds();
        sender.sendMessage(Chat.colorize("&eSềElượng tài nguyên đặc biệt: &f" + materialIds.size()));

        // Liệt kê các loại tài nguyên đặc biệt theo from_block
        Map<String, List<String>> materialsByBlock = new HashMap<>();
        for (String materialId : materialIds) {
            SpecialMaterialManager.SpecialMaterial material =
                SpecialMaterialManager.getSpecialMaterialById(materialId);

            if (material != null) {
                String fromBlock = material.getFromBlock();
                if (!materialsByBlock.containsKey(fromBlock)) {
                    materialsByBlock.put(fromBlock, new ArrayList<>());
                }
                materialsByBlock.get(fromBlock).add(materialId);
            }
        }

        sender.sendMessage(Chat.colorize("&ePhân loại theo from_block:"));
        for (Map.Entry<String, List<String>> entry : materialsByBlock.entrySet()) {
            String fromBlock = entry.getKey();
            List<String> materials = entry.getValue();
            
            sender.sendMessage(Chat.colorize("  &6" + fromBlock + " &7(" + materials.size() + "): &f" + String.join(", ", materials)));
        }
        
        // Thông tin vềEcác block được hềEtrợ
        List<String> supportedBlocks = com.hongminh54.storage.Manager.MineManager.getPluginBlocks();
        sender.sendMessage(Chat.colorize("&eSềElượng block được hềEtrợ: &f" + supportedBlocks.size()));
        
        // Hiển thềEcác block được hềEtrợ (tối đa 10 block)
        if (!supportedBlocks.isEmpty()) {
            StringBuilder blocksStr = new StringBuilder();
            int count = 0;
            for (String block : supportedBlocks) {
                if (count >= 10) {
                    blocksStr.append(", ...");
                    break;
                }
                if (count > 0) blocksStr.append(", ");
                blocksStr.append(block);
                count++;
            }
            sender.sendMessage(Chat.colorize("  &7Các block: &f" + blocksStr.toString()));
        }
        
        // Lời khuyên
        sender.sendMessage(Chat.colorize("&e&l=== &fLời Khuyên &e&l==="));
        sender.sendMessage(Chat.colorize("&71. Mỗi block trong config.yml có dạng: &fMATERIAL;DATA"));
        sender.sendMessage(Chat.colorize("&72. Mỗi from_block nên khớp với phần MATERIAL (trước dấu ';')"));
        sender.sendMessage(Chat.colorize("&73. Sử dụng 'ALL' trong from_block đềEáp dụng cho tất cả các loại khoáng sản"));
        sender.sendMessage(Chat.colorize("&74. Kiểm tra debug block cụ thềE &f/specialmaterial debug blocktype <tên_block>"));
    }
} 