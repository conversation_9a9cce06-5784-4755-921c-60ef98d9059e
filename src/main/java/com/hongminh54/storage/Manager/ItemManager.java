package com.hongminh54.storage.Manager;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import com.cryptomorin.xseries.XEnchantment;
import com.cryptomorin.xseries.XMaterial;
import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Utils.Chat;
import com.hongminh54.storage.Utils.File;
import com.hongminh54.storage.compatibility.MaterialCompatibility;

public class ItemManager {
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);

    /**
     * Tạo ItemStack từ Material, tên và danh sách lore
     * @param material Loại vật liệu
     * @param name Tên hiển thềE     * @param lore Danh sách mô tả
     * @return ItemStack đã tạo
     */
    public static ItemStack createItem(Material material, String name, List<String> lore) {
        ItemStack itemStack = new ItemStack(material);
        ItemMeta meta = itemStack.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(Chat.colorize(name));
            meta.setLore(Chat.colorizewp(lore));
            meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES);
            itemStack.setItemMeta(meta);
        }
        return itemStack;
    }

    public static ItemStack getItemConfig(ConfigurationSection section) {
        ItemStack itemStack;
        String m_s = section.getString("material");
        
        // Xử lý tương thích với 1.12.2
        if (m_s == null || m_s.isEmpty()) {
            m_s = "BLACK_STAINED_GLASS_PANE";
        }
        
        // Kiểm tra nếu chuỗi chứa dấu hai chấm (định dạng legacy cho material và data)
        if (m_s.contains(":")) {
            String[] parts = m_s.split(":");
            String materialName = parts[0];
            byte data = Byte.parseByte(parts[1]);
            
            if (IS_PRE_113) {
                // Phiên bản 1.12.2 - Sử dụng data value
                try {
                    Material material = com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely(materialName);
                    if (material != null) {
                        itemStack = new ItemStack(material, 1, data);
                    } else {
                        itemStack = new ItemStack(Material.STONE);
                    }
                } catch (Exception e) {
                    // Fallback
                    itemStack = new ItemStack(Material.STONE);
                }
            } else {
                // Phiên bản 1.13+ - Chuyển đổi tên material cũ sang mới
                Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(m_s);
                if (xMaterial.isPresent() && xMaterial.get().parseItem() != null) {
                    itemStack = xMaterial.get().parseItem();
                } else {
                    // Fallback
                    itemStack = new ItemStack(Material.STONE);
                }
            }
        } else {
            // Trường hợp thông thường không có data
            Optional<XMaterial> xMaterialOptional = XMaterial.matchXMaterial(m_s);
            if (xMaterialOptional.isPresent() && xMaterialOptional.get().parseItem() != null) {
                itemStack = xMaterialOptional.get().parseItem();
            } else {
                // Fallback nếu không tìm thấy vật liệu trong phiên bản hiện tại
                itemStack = new ItemStack(Material.STONE);
            }
        }
        
        ItemMeta meta = itemStack.getItemMeta();
        
        // Xử lý custom model data nếu có (chềEtừ 1.14+)
        if (section.contains("custom-model-data") && !nmsAssistant.isVersionLessThan(14)) {
            try {
                meta.setCustomModelData(section.getInt("custom-model-data"));
            } catch (Exception e) {
                // BềEqua nếu phiên bản không hềEtrợ
            }
        }
        
        itemStack.setAmount(section.getInt("amount"));
        
        // Unbreakable không có trong 1.12.2
        if (!IS_PRE_113) {
            meta.setUnbreakable(section.getBoolean("unbreakable"));
        }
        
        meta.setLore(Chat.colorizewp(section.getStringList("lore")));
        meta.setDisplayName(Chat.colorizewp(section.getString("name")));
        if (section.contains("enchants")) {
            for (String enchant_name : Objects.requireNonNull(section.getConfigurationSection("enchants")).getKeys(false)) {
                int level = section.getInt("enchants." + enchant_name);
                Optional<XEnchantment> enchantment = XEnchantment.matchXEnchantment(enchant_name);
                if (enchantment.isPresent() && enchantment.get().getEnchant() != null) {
                    meta.addEnchant(enchantment.get().getEnchant(), level, false);
                }
            }
        }
        if (section.contains("flags")) {
            for (String flag_name : Objects.requireNonNull(section.getConfigurationSection("flags")).getKeys(false)) {
                boolean apply = section.getBoolean("flags." + flag_name);
                if (flag_name.equalsIgnoreCase("ALL")) {
                    if (apply) {
                        // Sử dụng tương thích đa phiên bản cho ItemFlag
                        ItemFlag potionFlag = MaterialCompatibility.getCompatibleItemFlag("HIDE_ADDITIONAL_TOOLTIP", "HIDE_POTION_EFFECTS");
                        meta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE, ItemFlag.HIDE_ATTRIBUTES, ItemFlag.HIDE_DESTROYS, ItemFlag.HIDE_ENCHANTS, ItemFlag.HIDE_PLACED_ON, potionFlag);
                        break;
                    }
                } else {
                    try {
                        meta.addItemFlags(ItemFlag.valueOf(flag_name));
                    } catch (IllegalArgumentException e) {
                        // BềEqua nếu flag không tồn tại trong phiên bản này
                    }
                }
            }
        }
        itemStack.setItemMeta(meta);
        return itemStack;
    }

    public static ItemStack getItemConfig(Player p, String material, ConfigurationSection section) {
        String m_s = section.getString("material");
        ItemStack itemStack = createCompatibleItemStack(m_s);
        ItemMeta meta = itemStack.getItemMeta();
        
        // Xử lý custom model data nếu có (chềEtừ 1.14+)
        if (section.contains("custom-model-data") && !nmsAssistant.isVersionLessThan(14)) {
            try {
                meta.setCustomModelData(section.getInt("custom-model-data"));
            } catch (Exception e) {
                // BềEqua nếu phiên bản không hềEtrợ
            }
        }
        
        itemStack.setAmount(section.getInt("amount"));
        
        // Unbreakable không có trong 1.12.2
        if (!IS_PRE_113) {
            meta.setUnbreakable(section.getBoolean("unbreakable"));
        }
        
        meta.setLore(Chat.colorizewp(section.getStringList("lore")
                .stream().map(s -> s.replace("#item_amount#", String.valueOf(MineManager.getPlayerBlock(p, material)))
                        .replace("#max_storage#", String.valueOf(MineManager.getMaxBlock(p)))
                        .replace("#material#", Objects.requireNonNull(File.getConfig().getString("items." + material)))).collect(Collectors.toList())));
        meta.setDisplayName(Chat.colorizewp(Objects.requireNonNull(section.getString("name"))
                .replace("#item_amount#", String.valueOf(MineManager.getPlayerBlock(p, material)))
                .replace("#max_storage#", String.valueOf(MineManager.getMaxBlock(p)))
                .replace("#material#", Objects.requireNonNull(File.getConfig().getString("items." + material)))));
        
        applyEnchantsAndFlags(meta, section);
        
        itemStack.setItemMeta(meta);
        return itemStack;
    }

    public static String getStatus(Player p) {
        if (MineManager.toggle.get(p)) {
            return Chat.colorizewp(File.getMessage().getString("user.status.status_on"));
        } else return Chat.colorizewp(File.getMessage().getString("user.status.status_off"));
    }

    public static ItemStack getItemConfig(Player p, ConfigurationSection section) {
        String m_s = section.getString("material");
        ItemStack itemStack = createCompatibleItemStack(m_s);
        ItemMeta meta = itemStack.getItemMeta();
        
        // Xử lý custom model data nếu có (chềEtừ 1.14+)
        if (section.contains("custom-model-data") && !nmsAssistant.isVersionLessThan(14)) {
            try {
                meta.setCustomModelData(section.getInt("custom-model-data"));
            } catch (Exception e) {
                // BềEqua nếu phiên bản không hềEtrợ
            }
        }
        
        itemStack.setAmount(section.getInt("amount"));
        
        // Unbreakable không có trong 1.12.2
        if (!IS_PRE_113) {
            meta.setUnbreakable(section.getBoolean("unbreakable"));
        }
        
        meta.setLore(Chat.colorizewp(section.getStringList("lore")
                .stream().map(s -> s.replace("#status#", getStatus(p))).collect(Collectors.toList())));
        meta.setDisplayName(Chat.colorizewp(section.getString("name")));
        
        applyEnchantsAndFlags(meta, section);
        
        itemStack.setItemMeta(meta);
        return itemStack;
    }

    public static ItemStack getItemConfig(Player p, String material, String name, ConfigurationSection section) {
        String materialName = material.split(";")[0];
        ItemStack itemStack = createCompatibleItemStack(materialName);
        ItemMeta meta = itemStack.getItemMeta();
        
        // Xử lý custom model data nếu có (chềEtừ 1.14+)
        if (section.contains("custom-model-data") && !nmsAssistant.isVersionLessThan(14)) {
            try {
                meta.setCustomModelData(section.getInt("custom-model-data"));
            } catch (Exception e) {
                // BềEqua nếu phiên bản không hềEtrợ
            }
        }
        
        itemStack.setAmount(section.getInt("amount"));
        
        // Unbreakable không có trong 1.12.2
        if (!IS_PRE_113) {
            meta.setUnbreakable(section.getBoolean("unbreakable"));
        }
        
        meta.setLore(Chat.colorizewp(section.getStringList("lore")
                .stream().map(s -> s.replace("#item_amount#", String.valueOf(MineManager.getPlayerBlock(p, material)))
                        .replace("#max_storage#", String.valueOf(MineManager.getMaxBlock(p)))).collect(Collectors.toList())));
        meta.setDisplayName(Chat.colorizewp(name));
        
        applyEnchantsAndFlags(meta, section);
        
        itemStack.setItemMeta(meta);
        return itemStack;
    }
    
    /**
     * Helper method to apply enchants and flags to an ItemMeta
     */
    private static void applyEnchantsAndFlags(ItemMeta meta, ConfigurationSection section) {
        if (section.contains("enchants")) {
            for (String enchant_name : Objects.requireNonNull(section.getConfigurationSection("enchants")).getKeys(false)) {
                int level = section.getInt("enchants." + enchant_name);
                Optional<XEnchantment> enchantment = XEnchantment.matchXEnchantment(enchant_name);
                if (enchantment.isPresent() && enchantment.get().getEnchant() != null) {
                    meta.addEnchant(enchantment.get().getEnchant(), level, false);
                }
            }
        }
        if (section.contains("flags")) {
            for (String flag_name : Objects.requireNonNull(section.getConfigurationSection("flags")).getKeys(false)) {
                boolean apply = section.getBoolean("flags." + flag_name);
                if (flag_name.equalsIgnoreCase("ALL")) {
                    if (apply) {
                        // Sử dụng tương thích đa phiên bản cho ItemFlag
                        ItemFlag potionFlag = MaterialCompatibility.getCompatibleItemFlag("HIDE_ADDITIONAL_TOOLTIP", "HIDE_POTION_EFFECTS");
                        meta.addItemFlags(ItemFlag.HIDE_UNBREAKABLE, ItemFlag.HIDE_ATTRIBUTES, ItemFlag.HIDE_DESTROYS, ItemFlag.HIDE_ENCHANTS, ItemFlag.HIDE_PLACED_ON, potionFlag);
                        break;
                    }
                } else {
                    try {
                        meta.addItemFlags(ItemFlag.valueOf(flag_name));
                    } catch (IllegalArgumentException e) {
                        // BềEqua nếu flag không tồn tại trong phiên bản này
                    }
                }
            }
        }
    }
    
    /**
     * Tạo ItemStack tương thích với phiên bản từ tên vật liệu
     */
    private static ItemStack createCompatibleItemStack(String materialName) {
        if (materialName == null || materialName.isEmpty()) {
            materialName = "BLACK_STAINED_GLASS_PANE";
        }
        
        // Kiểm tra nếu chuỗi chứa dấu hai chấm (định dạng legacy cho material và data)
        if (materialName.contains(":")) {
            String[] parts = materialName.split(":");
            String material = parts[0];
            byte data = Byte.parseByte(parts[1]);
            
            if (IS_PRE_113) {
                // Phiên bản 1.12.2 - Sử dụng data value
                try {
                    Material mat = com.hongminh54.storage.compatibility.MaterialCompatibility.getMaterialSafely(material);
                    if (mat != null) {
                        return new ItemStack(mat, 1, data);
                    } else {
                        return new ItemStack(Material.STONE);
                    }
                } catch (Exception e) {
                    // Fallback
                    return new ItemStack(Material.STONE);
                }
            } else {
                // Phiên bản 1.13+ - Chuyển đổi tên material cũ sang mới
                Optional<XMaterial> xMaterial = XMaterial.matchXMaterial(materialName);
                if (xMaterial.isPresent() && xMaterial.get().parseItem() != null) {
                    return xMaterial.get().parseItem();
                } else {
                    // Fallback
                    return new ItemStack(Material.STONE);
                }
            }
        } else {
            // Trường hợp thông thường không có data
            Optional<XMaterial> xMaterialOptional = XMaterial.matchXMaterial(materialName);
            if (xMaterialOptional.isPresent() && xMaterialOptional.get().parseItem() != null) {
                return xMaterialOptional.get().parseItem();
            } else {
                // Fallback nếu không tìm thấy vật liệu trong phiên bản hiện tại
                return new ItemStack(Material.STONE);
            }
        }
    }

    /**
     * Cập nhật lore của một ItemStack bằng cách sử dụng một hàm biến đổi
     * @param itemStack ItemStack cần cập nhật
     * @param transformer Hàm biến đổi lore cũ thành lore mới
     * @return ItemStack với lore đã được cập nhật
     */
    public static ItemStack updateItemLore(ItemStack itemStack, java.util.function.Function<List<String>, List<String>> transformer) {
        if (itemStack == null) return null;
        
        ItemMeta meta = itemStack.getItemMeta();
        if (meta == null) return itemStack;
        
        List<String> lore = meta.getLore();
        if (lore == null) lore = new java.util.ArrayList<>();
        
        List<String> newLore = transformer.apply(lore);
        meta.setLore(Chat.colorizewp(newLore));
        
        itemStack.setItemMeta(meta);
        return itemStack;
    }
}
