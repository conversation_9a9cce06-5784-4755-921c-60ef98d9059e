package com.hongminh54.storage.Utils;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;

import java.util.Collection;

/**
 * Lớp hỗ trợ tương thích Entity API cho Minecraft 1.12.2 - 1.21.x
 * Xử lý entity spawning, EntityType changes, và entity manipulation
 */
public class EntityCompatibility {
    
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_17_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(17);
    
    /**
     * Spawn entity một cách an toàn với tương thích đa phiên bản
     * 
     * @param location Vị trí spawn
     * @param entityType Loại entity
     * @return Entity đã spawn hoặc null nếu có lỗi
     */
    public static Entity spawnEntitySafely(Location location, EntityType entityType) {
        if (location == null || location.getWorld() == null || entityType == null) {
            return null;
        }
        
        try {
            return location.getWorld().spawnEntity(location, entityType);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể spawn entity " + entityType + ": " + e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * Lấy EntityType tương thích đa phiên bản
     * 
     * @param modernName Tên entity phiên bản mới (1.13+)
     * @param legacyName Tên entity phiên bản cũ (1.12.2)
     * @return EntityType tương thích
     */
    public static EntityType getCompatibleEntityType(String modernName, String legacyName) {
        try {
            if (IS_PRE_113 && legacyName != null) {
                return EntityType.valueOf(legacyName);
            } else {
                return EntityType.valueOf(modernName);
            }
        } catch (IllegalArgumentException e) {
            // Thử với tên khác nếu không tìm thấy
            try {
                return EntityType.valueOf(IS_PRE_113 ? modernName : legacyName);
            } catch (IllegalArgumentException ex) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Không tìm thấy EntityType: " + modernName + "/" + legacyName);
                }
                return null;
            }
        }
    }
    
    /**
     * Lấy tất cả entities trong khu vực một cách an toàn
     * 
     * @param center Tâm khu vực
     * @param radius Bán kính
     * @return Collection của Entity
     */
    public static Collection<Entity> getNearbyEntitiesSafely(Location center, double radius) {
        if (center == null || center.getWorld() == null) {
            return java.util.Collections.emptyList();
        }
        
        try {
            return center.getWorld().getNearbyEntities(center, radius, radius, radius);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể lấy nearby entities: " + e.getMessage());
            }
            return java.util.Collections.emptyList();
        }
    }
    
    /**
     * Lấy tất cả entities theo loại trong khu vực
     *
     * @param center Tâm khu vực
     * @param radius Bán kính
     * @param entityType Loại entity
     * @return Collection của Entity
     */
    @SuppressWarnings("unchecked")
    public static <T extends Entity> Collection<T> getNearbyEntitiesByType(Location center, double radius, Class<T> entityType) {
        if (center == null || center.getWorld() == null || entityType == null) {
            return java.util.Collections.emptyList();
        }

        try {
            // Thử với method mới trước (1.16+)
            try {
                java.lang.reflect.Method getNearbyMethod = center.getWorld().getClass().getMethod(
                    "getNearbyEntitiesByType", Class.class, Location.class, double.class);
                return (Collection<T>) getNearbyMethod.invoke(center.getWorld(), entityType, center, radius);
            } catch (Exception e) {
                // Fallback với method cũ hơn
                return getNearbyEntitiesByTypeReflection(center, radius, entityType);
            }
        } catch (Exception e) {
            // Fallback method cho phiên bản cũ
            return getNearbyEntitiesByTypeReflection(center, radius, entityType);
        }
    }
    
    /**
     * Xóa entity một cách an toàn
     * 
     * @param entity Entity cần xóa
     * @return true nếu xóa thành công
     */
    public static boolean removeEntitySafely(Entity entity) {
        if (entity == null || entity.isDead()) {
            return false;
        }
        
        try {
            entity.remove();
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể xóa entity: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Teleport entity một cách an toàn
     * 
     * @param entity Entity cần teleport
     * @param location Vị trí đích
     * @return true nếu teleport thành công
     */
    public static boolean teleportEntitySafely(Entity entity, Location location) {
        if (entity == null || location == null || entity.isDead()) {
            return false;
        }
        
        try {
            return entity.teleport(location);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể teleport entity: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Kiểm tra entity có phải là living entity không
     * 
     * @param entity Entity cần kiểm tra
     * @return true nếu là LivingEntity
     */
    public static boolean isLivingEntity(Entity entity) {
        return entity instanceof LivingEntity;
    }
    
    /**
     * Lấy health của entity một cách an toàn
     * 
     * @param entity Entity
     * @return Health hiện tại hoặc 0 nếu có lỗi
     */
    public static double getEntityHealth(Entity entity) {
        if (!isLivingEntity(entity)) {
            return 0.0;
        }
        
        try {
            LivingEntity livingEntity = (LivingEntity) entity;
            return livingEntity.getHealth();
        } catch (Exception e) {
            return 0.0;
        }
    }
    
    /**
     * Set health cho entity một cách an toàn
     * 
     * @param entity Entity
     * @param health Health mới
     * @return true nếu set thành công
     */
    public static boolean setEntityHealth(Entity entity, double health) {
        if (!isLivingEntity(entity)) {
            return false;
        }
        
        try {
            LivingEntity livingEntity = (LivingEntity) entity;
            double maxHealth = livingEntity.getMaxHealth();
            livingEntity.setHealth(Math.min(health, maxHealth));
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể set health cho entity: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Fallback method sử dụng reflection cho getNearbyEntitiesByType
     */
    @SuppressWarnings("unchecked")
    private static <T extends Entity> Collection<T> getNearbyEntitiesByTypeReflection(Location center, double radius, Class<T> entityType) {
        try {
            Collection<Entity> allEntities = getNearbyEntitiesSafely(center, radius);
            return allEntities.stream()
                    .filter(entityType::isInstance)
                    .map(entity -> (T) entity)
                    .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            return java.util.Collections.emptyList();
        }
    }
    
    /**
     * Lấy tất cả players trong khu vực
     * 
     * @param center Tâm khu vực
     * @param radius Bán kính
     * @return Collection của Player
     */
    public static Collection<Player> getNearbyPlayers(Location center, double radius) {
        return getNearbyEntitiesByType(center, radius, Player.class);
    }
    
    /**
     * Kiểm tra entity có tồn tại và hợp lệ không
     * 
     * @param entity Entity cần kiểm tra
     * @return true nếu entity hợp lệ
     */
    public static boolean isValidEntity(Entity entity) {
        return entity != null && !entity.isDead() && entity.isValid();
    }
    
    /**
     * Lấy tên hiển thị của entity
     * 
     * @param entity Entity
     * @return Tên hiển thị hoặc tên loại entity
     */
    public static String getEntityDisplayName(Entity entity) {
        if (entity == null) {
            return "Unknown";
        }
        
        try {
            if (entity.getCustomName() != null) {
                return entity.getCustomName();
            }
            return entity.getType().name();
        } catch (Exception e) {
            return entity.getType().name();
        }
    }
}
