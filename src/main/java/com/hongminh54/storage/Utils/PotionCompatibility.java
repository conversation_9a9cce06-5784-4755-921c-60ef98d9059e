package com.hongminh54.storage.compatibility;

import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import com.hongminh54.storage.NMS.NMSAssistant;
import com.hongminh54.storage.Storage;

import java.util.Collection;

/**
 * Lớp hỗ trợ tương thích Potion API cho Minecraft 1.12.2 - 1.21.x
 * Xử lý PotionEffectType changes, potion effects, và potion manipulation
 */
public class PotionCompatibility {
    
    private static final NMSAssistant nmsAssistant = new NMSAssistant();
    private static final boolean IS_PRE_113 = nmsAssistant.isVersionLessThan(13);
    private static final boolean IS_PRE_116 = nmsAssistant.isVersionLessThan(16);
    private static final boolean IS_1_20_5_OR_HIGHER = nmsAssistant.isVersionGreaterThanOrEqualTo(20) && 
                                                       nmsAssistant.getNMSVersion().getRevision() >= 5;
    
    /**
     * Lấy PotionEffectType tương thích đa phiên bản
     * 
     * @param modernName Tên effect phiên bản mới (1.20.5+)
     * @param legacyName Tên effect phiên bản cũ (trước 1.20.5)
     * @return PotionEffectType tương thích
     */
    @SuppressWarnings("deprecation")
    public static PotionEffectType getCompatiblePotionEffectType(String modernName, String legacyName) {
        try {
            if (IS_1_20_5_OR_HIGHER) {
                // Thử với tên mới trước
                PotionEffectType effect = PotionEffectType.getByName(modernName);
                if (effect != null) {
                    return effect;
                }
            }
            
            // Thử với tên cũ
            PotionEffectType effect = PotionEffectType.getByName(legacyName);
            if (effect != null) {
                return effect;
            }
            
            // Thử với ID (cho phiên bản rất cũ)
            return getPotionEffectByLegacyId(legacyName);
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không tìm thấy PotionEffectType: " + modernName + "/" + legacyName);
            }
            return null;
        }
    }
    
    /**
     * Áp dụng potion effect một cách an toàn
     * 
     * @param entity Entity nhận effect
     * @param effectType Loại effect
     * @param duration Thời gian (ticks)
     * @param amplifier Cường độ (0 = level 1)
     * @param ambient Có phải ambient effect không
     * @param particles Hiển thị particles không
     * @return true nếu áp dụng thành công
     */
    public static boolean addPotionEffectSafely(LivingEntity entity, PotionEffectType effectType, 
                                               int duration, int amplifier, boolean ambient, boolean particles) {
        if (entity == null || effectType == null) {
            return false;
        }
        
        try {
            PotionEffect effect = new PotionEffect(effectType, duration, amplifier, ambient, particles);
            return entity.addPotionEffect(effect);
        } catch (Exception e) {
            // Fallback không có particles parameter cho phiên bản cũ
            return addPotionEffectLegacy(entity, effectType, duration, amplifier, ambient);
        }
    }
    
    /**
     * Áp dụng potion effect với tham số đơn giản
     * 
     * @param entity Entity nhận effect
     * @param effectType Loại effect
     * @param duration Thời gian (ticks)
     * @param amplifier Cường độ (0 = level 1)
     * @return true nếu áp dụng thành công
     */
    public static boolean addPotionEffectSafely(LivingEntity entity, PotionEffectType effectType, 
                                               int duration, int amplifier) {
        return addPotionEffectSafely(entity, effectType, duration, amplifier, false, true);
    }
    
    /**
     * Xóa potion effect một cách an toàn
     * 
     * @param entity Entity
     * @param effectType Loại effect cần xóa
     * @return true nếu xóa thành công
     */
    public static boolean removePotionEffectSafely(LivingEntity entity, PotionEffectType effectType) {
        if (entity == null || effectType == null) {
            return false;
        }
        
        try {
            entity.removePotionEffect(effectType);
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể xóa potion effect: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Kiểm tra entity có effect không
     * 
     * @param entity Entity
     * @param effectType Loại effect
     * @return true nếu có effect
     */
    public static boolean hasPotionEffect(LivingEntity entity, PotionEffectType effectType) {
        if (entity == null || effectType == null) {
            return false;
        }
        
        try {
            return entity.hasPotionEffect(effectType);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Lấy potion effect hiện tại
     * 
     * @param entity Entity
     * @param effectType Loại effect
     * @return PotionEffect hoặc null nếu không có
     */
    public static PotionEffect getPotionEffect(LivingEntity entity, PotionEffectType effectType) {
        if (entity == null || effectType == null) {
            return null;
        }
        
        try {
            return entity.getPotionEffect(effectType);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Lấy tất cả potion effects hiện tại
     * 
     * @param entity Entity
     * @return Collection của PotionEffect
     */
    public static Collection<PotionEffect> getActivePotionEffects(LivingEntity entity) {
        if (entity == null) {
            return java.util.Collections.emptyList();
        }
        
        try {
            return entity.getActivePotionEffects();
        } catch (Exception e) {
            return java.util.Collections.emptyList();
        }
    }
    
    /**
     * Xóa tất cả potion effects
     * 
     * @param entity Entity
     * @return true nếu xóa thành công
     */
    public static boolean clearAllPotionEffects(LivingEntity entity) {
        if (entity == null) {
            return false;
        }
        
        try {
            Collection<PotionEffect> effects = getActivePotionEffects(entity);
            for (PotionEffect effect : effects) {
                entity.removePotionEffect(effect.getType());
            }
            return true;
        } catch (Exception e) {
            if (Storage.getStorage().isDebug()) {
                Storage.getStorage().getLogger().warning("Không thể xóa tất cả potion effects: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Áp dụng effect với tên string
     * 
     * @param entity Entity
     * @param modernEffectName Tên effect phiên bản mới
     * @param legacyEffectName Tên effect phiên bản cũ
     * @param duration Thời gian (ticks)
     * @param amplifier Cường độ
     * @return true nếu áp dụng thành công
     */
    public static boolean addPotionEffectByName(LivingEntity entity, String modernEffectName, 
                                               String legacyEffectName, int duration, int amplifier) {
        PotionEffectType effectType = getCompatiblePotionEffectType(modernEffectName, legacyEffectName);
        if (effectType == null) {
            return false;
        }
        
        return addPotionEffectSafely(entity, effectType, duration, amplifier);
    }
    
    /**
     * Fallback method cho phiên bản cũ không hỗ trợ particles parameter
     */
    private static boolean addPotionEffectLegacy(LivingEntity entity, PotionEffectType effectType, 
                                                int duration, int amplifier, boolean ambient) {
        try {
            PotionEffect effect = new PotionEffect(effectType, duration, amplifier, ambient);
            return entity.addPotionEffect(effect);
        } catch (Exception e) {
            // Fallback cuối cùng với constructor cơ bản nhất
            try {
                PotionEffect effect = new PotionEffect(effectType, duration, amplifier);
                return entity.addPotionEffect(effect);
            } catch (Exception ex) {
                if (Storage.getStorage().isDebug()) {
                    Storage.getStorage().getLogger().warning("Không thể áp dụng potion effect: " + ex.getMessage());
                }
                return false;
            }
        }
    }
    
    /**
     * Lấy PotionEffectType bằng legacy ID (cho phiên bản rất cũ)
     */
    @SuppressWarnings("deprecation")
    private static PotionEffectType getPotionEffectByLegacyId(String name) {
        try {
            // Mapping một số effect phổ biến
            switch (name.toUpperCase()) {
                case "SPEED":
                case "SWIFTNESS":
                    return PotionEffectType.getById(1);
                case "SLOWNESS":
                case "SLOW":
                    return PotionEffectType.getById(2);
                case "HASTE":
                case "FAST_DIGGING":
                    return PotionEffectType.getById(3);
                case "MINING_FATIGUE":
                case "SLOW_DIGGING":
                    return PotionEffectType.getById(4);
                case "STRENGTH":
                case "INCREASE_DAMAGE":
                    return PotionEffectType.getById(5);
                case "INSTANT_HEALTH":
                case "HEAL":
                    return PotionEffectType.getById(6);
                case "INSTANT_DAMAGE":
                case "HARM":
                    return PotionEffectType.getById(7);
                case "JUMP_BOOST":
                case "JUMP":
                    return PotionEffectType.getById(8);
                case "NAUSEA":
                case "CONFUSION":
                    return PotionEffectType.getById(9);
                case "REGENERATION":
                    return PotionEffectType.getById(10);
                case "RESISTANCE":
                case "DAMAGE_RESISTANCE":
                    return PotionEffectType.getById(11);
                case "FIRE_RESISTANCE":
                    return PotionEffectType.getById(12);
                case "WATER_BREATHING":
                    return PotionEffectType.getById(13);
                case "INVISIBILITY":
                    return PotionEffectType.getById(14);
                case "BLINDNESS":
                    return PotionEffectType.getById(15);
                case "NIGHT_VISION":
                    return PotionEffectType.getById(16);
                case "HUNGER":
                    return PotionEffectType.getById(17);
                case "WEAKNESS":
                    return PotionEffectType.getById(18);
                case "POISON":
                    return PotionEffectType.getById(19);
                case "WITHER":
                    return PotionEffectType.getById(20);
                default:
                    return null;
            }
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Kiểm tra effect có phải là beneficial không
     * 
     * @param effectType PotionEffectType
     * @return true nếu là beneficial effect
     */
    public static boolean isBeneficialEffect(PotionEffectType effectType) {
        if (effectType == null) {
            return false;
        }
        
        try {
            // Sử dụng method mới nếu có
            java.lang.reflect.Method isBeneficial = effectType.getClass().getMethod("isBeneficial");
            return (Boolean) isBeneficial.invoke(effectType);
        } catch (Exception e) {
            // Fallback với hardcoded list cho phiên bản cũ
            return isBeneficialEffectLegacy(effectType);
        }
    }
    
    /**
     * Fallback method để kiểm tra beneficial effect cho phiên bản cũ
     */
    @SuppressWarnings("deprecation")
    private static boolean isBeneficialEffectLegacy(PotionEffectType effectType) {
        // Danh sách các effect có lợi - sử dụng tên mới
        return effectType.equals(PotionEffectType.SPEED) ||
               effectType.equals(PotionEffectType.HASTE) ||
               effectType.equals(PotionEffectType.STRENGTH) ||
               effectType.equals(PotionEffectType.INSTANT_HEALTH) ||
               effectType.equals(PotionEffectType.JUMP_BOOST) ||
               effectType.equals(PotionEffectType.REGENERATION) ||
               effectType.equals(PotionEffectType.RESISTANCE) ||
               effectType.equals(PotionEffectType.FIRE_RESISTANCE) ||
               effectType.equals(PotionEffectType.WATER_BREATHING) ||
               effectType.equals(PotionEffectType.INVISIBILITY) ||
               effectType.equals(PotionEffectType.NIGHT_VISION);
    }
}
